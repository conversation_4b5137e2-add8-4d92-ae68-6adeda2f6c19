'use client';

import { Settings, ChevronDown, ChevronUp } from 'lucide-react';
import { useContextStore } from '@/store/contextStore';

export default function AdvancedOptionsToggle() {
  const { 
    showAdvancedOptions, 
    setShowAdvancedOptions, 
    currentLanguage 
  } = useContextStore();
  
  const isArabic = currentLanguage === 'ar';

  const translations = {
    ar: {
      showAdvancedOptions: 'إظهار الخيارات المتقدمة',
      hideAdvancedOptions: 'إخفاء الخيارات المتقدمة',
      advancedOptionsDescription: 'خيارات التخصيص والتحكم المتقدمة'
    },
    en: {
      showAdvancedOptions: 'Show Advanced Options',
      hideAdvancedOptions: 'Hide Advanced Options',
      advancedOptionsDescription: 'Advanced customization and control options'
    }
  };

  const t = translations[currentLanguage];

  return (
    <div className={`fixed top-1/2 transform -translate-y-1/2 z-40 ${isArabic ? 'left-4' : 'right-4'}`}>
      <div className="bg-white/95 dark:bg-gray-800/95 backdrop-blur-md rounded-2xl shadow-xl border border-gray-200/50 dark:border-gray-700/50 p-3">
        <button
          onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
          className={`
            group relative flex flex-col items-center justify-center w-20 h-28
            rounded-xl transition-all duration-300 ease-in-out
            ${showAdvancedOptions
              ? 'bg-gradient-to-br from-indigo-50 to-purple-100 dark:from-indigo-900/30 dark:to-purple-900/30 border-2 border-indigo-200 dark:border-indigo-700'
              : 'bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 border-2 border-gray-200 dark:border-gray-600 hover:from-gray-100 hover:to-gray-200 dark:hover:from-gray-700 dark:hover:to-gray-800'
            }
            shadow-lg hover:shadow-xl
          `}
          title={showAdvancedOptions ? t.hideAdvancedOptions : t.showAdvancedOptions}
        >
          {/* Icon */}
          <div className={`
            flex items-center justify-center w-10 h-10 rounded-lg mb-2 transition-all duration-300
            ${showAdvancedOptions
              ? 'bg-indigo-100 dark:bg-indigo-800'
              : 'bg-gray-100 dark:bg-gray-700 group-hover:bg-gray-200 dark:group-hover:bg-gray-600'
            }
          `}>
            <Settings className={`
              w-5 h-5 transition-all duration-300
              ${showAdvancedOptions
                ? 'text-indigo-600 dark:text-indigo-400 rotate-90'
                : 'text-gray-600 dark:text-gray-400 group-hover:rotate-90'
              }
            `} />
          </div>

          {/* Text */}
          <div className={`
            text-xs font-medium text-center leading-tight transition-colors duration-300 px-1
            ${showAdvancedOptions
              ? 'text-indigo-700 dark:text-indigo-300'
              : 'text-gray-600 dark:text-gray-400'
            }
          `}>
            <div className="font-arabic mb-0.5">
              {isArabic ? 'خيارات' : 'Advanced'}
            </div>
            <div className="font-arabic">
              {isArabic ? 'متقدمة' : 'Options'}
            </div>
          </div>

          {/* Arrow indicator */}
          <div className={`
            absolute -bottom-1 left-1/2 transform -translate-x-1/2 transition-all duration-300
            ${showAdvancedOptions ? 'text-indigo-500 dark:text-indigo-400' : 'text-gray-400 dark:text-gray-500'}
          `}>
            {showAdvancedOptions ? (
              <ChevronUp className="w-3 h-3" />
            ) : (
              <ChevronDown className="w-3 h-3" />
            )}
          </div>

          {/* Subtle glow effect */}
          <div className={`
            absolute inset-0 rounded-xl transition-opacity duration-300
            ${showAdvancedOptions 
              ? 'bg-gradient-to-r from-transparent via-indigo-100/20 to-transparent opacity-100' 
              : 'bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100'
            }
          `} />

          {/* Active indicator */}
          {showAdvancedOptions && (
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-indigo-500 dark:bg-indigo-400 rounded-full animate-pulse" />
          )}
        </button>

        {/* Tooltip */}
        <div className={`
          absolute top-1/2 transform -translate-y-1/2 transition-all duration-300 pointer-events-none group-hover:opacity-100 opacity-0
          ${isArabic ? 'right-full mr-3' : 'left-full ml-3'}
        `}>
          <div className="bg-gray-900 dark:bg-gray-700 text-white text-xs px-3 py-2 rounded-lg whitespace-nowrap font-arabic shadow-lg">
            {t.advancedOptionsDescription}
            <div className={`
              absolute top-1/2 transform -translate-y-1/2 w-2 h-2 bg-gray-900 dark:bg-gray-700 rotate-45
              ${isArabic ? 'right-0 translate-x-1/2' : 'left-0 -translate-x-1/2'}
            `} />
          </div>
        </div>
      </div>
    </div>
  );
}
