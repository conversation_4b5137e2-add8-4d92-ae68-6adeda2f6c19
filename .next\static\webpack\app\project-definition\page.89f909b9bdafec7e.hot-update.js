"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/project-definition/page",{

/***/ "(app-pages-browser)/./src/components/SmartFieldAssistant.tsx":
/*!************************************************!*\
  !*** ./src/components/SmartFieldAssistant.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SmartFieldAssistant; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Sparkles,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Sparkles,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Sparkles,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wand-sparkles.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction SmartFieldAssistant(param) {\n    let { fieldName, fieldValue, onValueChange, placeholder, context, className = \"\" } = param;\n    _s();\n    const { currentLanguage, getActiveProviders, getAllData } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore)();\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [generatedSuggestions, setGeneratedSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showSuggestions, setShowSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [copiedIndex, setCopiedIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeProviders, setActiveProviders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isArabic = currentLanguage === \"ar\";\n    // تجنب مشاكل الهيدريشن\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n        setActiveProviders(getActiveProviders());\n    }, [\n        getActiveProviders\n    ]);\n    // تحقق من وجود مقدم خدمة صالح\n    const hasValidProvider = mounted && activeProviders.some((p)=>p.apiKey && p.validationStatus === \"valid\" && p.selectedModels && p.selectedModels.length > 0);\n    const translations = {\n        generateWithAI: isArabic ? \"\\uD83D\\uDCC4 توليد بالذكاء الاصطناعي\" : \"\\uD83D\\uDCC4 Generate with AI\",\n        generating: isArabic ? \"جاري التوليد...\" : \"Generating...\",\n        suggestions: isArabic ? \"اقتراحات ذكية\" : \"Smart Suggestions\",\n        useThis: isArabic ? \"استخدام هذا\" : \"Use This\",\n        copy: isArabic ? \"نسخ\" : \"Copy\",\n        copied: isArabic ? \"تم النسخ\" : \"Copied\",\n        noProviders: isArabic ? \"يرجى إعداد مقدم خدمة AI وتحديد النماذج في صفحة الإعدادات أولاً\" : \"Please configure an AI provider and select models in Settings first\",\n        error: isArabic ? \"حدث خطأ أثناء التوليد\" : \"Error occurred during generation\",\n        tryAgain: isArabic ? \"حاول مرة أخرى\" : \"Try Again\",\n        regenerate: isArabic ? \"إعادة توليد\" : \"Regenerate\",\n        fastGeneration: isArabic ? \"توليد سريع (محسّن)\" : \"Fast Generation (Optimized)\",\n        timeout: isArabic ? \"انتهت مهلة الطلب - حاول مرة أخرى\" : \"Request timeout - try again\"\n    };\n    const generateSuggestions = async ()=>{\n        if (!hasValidProvider) {\n            console.warn(\"No valid provider available:\", {\n                activeProviders,\n                hasValidProvider\n            });\n            alert(translations.noProviders);\n            return;\n        }\n        setIsGenerating(true);\n        try {\n            const allContext = getAllData();\n            const provider = activeProviders.find((p)=>p.apiKey && p.validationStatus === \"valid\");\n            console.log(\"Using provider:\", provider === null || provider === void 0 ? void 0 : provider.name, \"with model:\", provider === null || provider === void 0 ? void 0 : provider.selectedModels[0]);\n            if (!provider) {\n                throw new Error(\"No valid provider found\");\n            }\n            // إنشاء prompt ذكي بناءً على السياق والحقل\n            const prompt = createSmartPrompt(fieldName, fieldValue, allContext, isArabic);\n            console.log(\"Generated prompt:\", prompt);\n            const requestBody = {\n                providerId: provider.id,\n                apiKey: provider.apiKey,\n                model: provider.selectedModels[0] || \"gpt-3.5-turbo\",\n                messages: [\n                    {\n                        role: \"user\",\n                        content: prompt\n                    }\n                ],\n                context: allContext,\n                fieldName,\n                language: currentLanguage,\n                temperature: 0.7,\n                maxTokens: 200 // تقليل maxTokens بشكل كبير للسرعة\n            };\n            console.log(\"Sending request to API:\", requestBody);\n            // إضافة timeout للطلب من جانب العميل\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>{\n                controller.abort();\n            }, 35000); // 35 ثانية timeout\n            const response = await fetch(\"/api/llm/generate\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(requestBody),\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            console.log(\"API Response status:\", response.status);\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error(\"API Error:\", errorText);\n                throw new Error(\"API Error: \".concat(response.status, \" - \").concat(errorText));\n            }\n            const result = await response.json();\n            console.log(\"API Result:\", result);\n            if (result.success) {\n                // استخدام أول اقتراح مباشرة في خانة الكتابة\n                const suggestions = parseSuggestions(result.content);\n                console.log(\"Parsed suggestions:\", suggestions);\n                if (suggestions.length > 0) {\n                    // وضع أول اقتراح في خانة الكتابة\n                    onValueChange(suggestions[0]);\n                    // حفظ باقي الاقتراحات للاستخدام لاحقاً\n                    setGeneratedSuggestions(suggestions);\n                } else {\n                    const errorMsg = isArabic ? \"لم يتم العثور على اقتراحات مناسبة\" : \"No suitable suggestions found\";\n                    onValueChange(errorMsg);\n                }\n            } else {\n                throw new Error(result.error || \"Generation failed\");\n            }\n        } catch (error) {\n            console.error(\"Generation error:\", error);\n            let errorMessage = translations.error;\n            if (error instanceof Error) {\n                if (error.name === \"AbortError\") {\n                    errorMessage = translations.timeout;\n                } else if (error.message.includes(\"timeout\")) {\n                    errorMessage = translations.timeout;\n                } else {\n                    errorMessage = \"\".concat(translations.error, \": \").concat(error.message);\n                }\n            }\n            onValueChange(errorMessage);\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const createSmartPrompt = (fieldName, currentValue, context, isArabic)=>{\n        // استخراج الخيارات المتقدمة من السياق\n        const projectDef = (context === null || context === void 0 ? void 0 : context.projectDefinition) || {};\n        const advancedContext = {\n            projectType: projectDef.projectType,\n            projectNature: projectDef.projectNature,\n            geographicRegion: projectDef.geographicRegion,\n            targetPlatforms: projectDef.targetPlatforms || [],\n            primaryLanguages: projectDef.primaryLanguages || [],\n            complexity: projectDef.complexity,\n            deploymentType: projectDef.deploymentType\n        };\n        // إنشاء سياق متقدم للـ prompt مع التركيز على الخيارات الجديدة\n        const contextString = Object.entries(advancedContext).filter((param)=>{\n            let [_, value] = param;\n            return value && (Array.isArray(value) ? value.length > 0 : true);\n        }).map((param)=>{\n            let [key, value] = param;\n            return \"\".concat(key, \": \").concat(Array.isArray(value) ? value.join(\", \") : value);\n        }).join(\", \");\n        // تحسين الـ prompts لتكون أكثر تفصيلاً وذكاءً\n        const fieldPrompts = {\n            // Project Definition Module\n            name: {\n                ar: \"بناءً على السياق المتاح، اقترح 3 أسماء إبداعية ومناسبة للمشروع\",\n                en: \"Based on the available context, suggest 3 creative and suitable project names\"\n            },\n            purpose: {\n                ar: \"اكتب 3 أوصاف مختلفة ومفصلة لغرض المشروع، مع مراعاة الاسم والسياق\",\n                en: \"Write 3 different and detailed project purpose descriptions, considering the name and context\"\n            },\n            targetUsers: {\n                ar: \"حدد 3 مجموعات مختلفة من المستخدمين المستهدفين بناءً على غرض المشروع\",\n                en: \"Define 3 different target user groups based on the project purpose\"\n            },\n            goals: {\n                ar: \"اقترح 3 أهداف محددة وقابلة للقياس للمشروع\",\n                en: \"Suggest 3 specific and measurable project goals\"\n            },\n            scope: {\n                ar: \"حدد 3 نطاقات مختلفة للمشروع (صغير، متوسط، كبير)\",\n                en: \"Define 3 different project scopes (small, medium, large)\"\n            },\n            timeline: {\n                ar: \"اقترح 3 جداول زمنية مختلفة للمشروع\",\n                en: \"Suggest 3 different project timelines\"\n            },\n            // Context Map Module\n            timeContext: {\n                ar: \"حدد 3 سياقات زمنية مختلفة مناسبة للمشروع\",\n                en: \"Define 3 different time contexts suitable for the project\"\n            },\n            language: {\n                ar: \"اقترح 3 استراتيجيات لغوية للمشروع\",\n                en: \"Suggest 3 language strategies for the project\"\n            },\n            location: {\n                ar: \"حدد 3 مواقع جغرافية مستهدفة للمشروع\",\n                en: \"Define 3 target geographical locations for the project\"\n            },\n            culturalContext: {\n                ar: \"اقترح 3 اعتبارات ثقافية مهمة للمشروع\",\n                en: \"Suggest 3 important cultural considerations for the project\"\n            },\n            // Emotional Tone Module\n            personality: {\n                ar: \"اقترح 3 شخصيات مختلفة للمشروع تناسب المستخدمين المستهدفين\",\n                en: \"Suggest 3 different project personalities that suit the target users\"\n            },\n            communicationStyle: {\n                ar: \"حدد 3 أساليب تواصل مختلفة مناسبة للمشروع\",\n                en: \"Define 3 different communication styles suitable for the project\"\n            },\n            // Technical Layer Module\n            programmingLanguages: {\n                ar: \"اقترح 3 لغات برمجة مناسبة للمشروع مع التبرير\",\n                en: \"Suggest 3 suitable programming languages for the project with justification\"\n            },\n            frameworks: {\n                ar: \"حدد 3 إطارات عمل تقنية مناسبة للمشروع\",\n                en: \"Define 3 technical frameworks suitable for the project\"\n            }\n        };\n        const fieldPrompt = fieldPrompts[fieldName];\n        const basePrompt = fieldPrompt ? isArabic ? fieldPrompt.ar : fieldPrompt.en : isArabic ? \"اقترح محتوى ذكي ومناسب لـ \".concat(fieldName) : \"Suggest smart and suitable content for \".concat(fieldName);\n        // بناء سياق أكثر ذكاءً\n        const contextInfo = buildIntelligentContext(context, fieldName, isArabic);\n        const instructions = isArabic ? \"قدم 3 اقتراحات مرقمة ومفصلة، كل اقتراح في سطر منفصل. اجعل كل اقتراح متماسكاً مع السياق العام للمشروع.\" : \"Provide 3 numbered and detailed suggestions, each on a separate line. Make each suggestion coherent with the overall project context.\";\n        return \"\".concat(contextInfo, \"\\n\").concat(basePrompt, \"\\n\").concat(instructions);\n    };\n    // وظيفة لبناء سياق ذكي\n    const buildIntelligentContext = (context, fieldName, isArabic)=>{\n        var _context_projectDefinition, _context_projectDefinition1, _context_projectDefinition2;\n        const contextParts = [];\n        // معلومات المشروع الأساسية\n        if (context === null || context === void 0 ? void 0 : (_context_projectDefinition = context.projectDefinition) === null || _context_projectDefinition === void 0 ? void 0 : _context_projectDefinition.name) {\n            contextParts.push(isArabic ? \"اسم المشروع: \".concat(context.projectDefinition.name) : \"Project Name: \".concat(context.projectDefinition.name));\n        }\n        if (context === null || context === void 0 ? void 0 : (_context_projectDefinition1 = context.projectDefinition) === null || _context_projectDefinition1 === void 0 ? void 0 : _context_projectDefinition1.purpose) {\n            contextParts.push(isArabic ? \"الغرض: \".concat(context.projectDefinition.purpose.substring(0, 100), \"...\") : \"Purpose: \".concat(context.projectDefinition.purpose.substring(0, 100), \"...\"));\n        }\n        if (context === null || context === void 0 ? void 0 : (_context_projectDefinition2 = context.projectDefinition) === null || _context_projectDefinition2 === void 0 ? void 0 : _context_projectDefinition2.targetUsers) {\n            contextParts.push(isArabic ? \"المستخدمون المستهدفون: \".concat(context.projectDefinition.targetUsers.substring(0, 80), \"...\") : \"Target Users: \".concat(context.projectDefinition.targetUsers.substring(0, 80), \"...\"));\n        }\n        // الخيارات المتقدمة\n        const projectDef = (context === null || context === void 0 ? void 0 : context.projectDefinition) || {};\n        if (projectDef.projectType) {\n            contextParts.push(isArabic ? \"نوع المشروع: \".concat(projectDef.projectType) : \"Project Type: \".concat(projectDef.projectType));\n        }\n        if (projectDef.targetPlatforms && projectDef.targetPlatforms.length > 0) {\n            contextParts.push(isArabic ? \"المنصات المستهدفة: \".concat(projectDef.targetPlatforms.join(\", \")) : \"Target Platforms: \".concat(projectDef.targetPlatforms.join(\", \")));\n        }\n        if (projectDef.primaryLanguages && projectDef.primaryLanguages.length > 0) {\n            contextParts.push(isArabic ? \"لغات البرمجة: \".concat(projectDef.primaryLanguages.join(\", \")) : \"Programming Languages: \".concat(projectDef.primaryLanguages.join(\", \")));\n        }\n        if (projectDef.complexity) {\n            contextParts.push(isArabic ? \"مستوى التعقيد: \".concat(projectDef.complexity) : \"Complexity Level: \".concat(projectDef.complexity));\n        }\n        if (projectDef.teamSize) {\n            contextParts.push(isArabic ? \"حجم الفريق: \".concat(projectDef.teamSize) : \"Team Size: \".concat(projectDef.teamSize));\n        }\n        if (projectDef.budget) {\n            contextParts.push(isArabic ? \"نطاق الميزانية: \".concat(projectDef.budget) : \"Budget Range: \".concat(projectDef.budget));\n        }\n        // سياق إضافي حسب المجال\n        if (fieldName.includes(\"technical\") || fieldName.includes(\"programming\") || fieldName.includes(\"frameworks\")) {\n            var _context_technicalLayer;\n            if (context === null || context === void 0 ? void 0 : (_context_technicalLayer = context.technicalLayer) === null || _context_technicalLayer === void 0 ? void 0 : _context_technicalLayer.programmingLanguages) {\n                contextParts.push(isArabic ? \"التقنيات المستخدمة: \".concat(context.technicalLayer.programmingLanguages.substring(0, 60), \"...\") : \"Technologies: \".concat(context.technicalLayer.programmingLanguages.substring(0, 60), \"...\"));\n            }\n        }\n        if (fieldName.includes(\"emotional\") || fieldName.includes(\"personality\") || fieldName.includes(\"communication\")) {\n            var _context_emotionalTone;\n            if (context === null || context === void 0 ? void 0 : (_context_emotionalTone = context.emotionalTone) === null || _context_emotionalTone === void 0 ? void 0 : _context_emotionalTone.personality) {\n                contextParts.push(isArabic ? \"الشخصية المطلوبة: \".concat(context.emotionalTone.personality.substring(0, 60), \"...\") : \"Required Personality: \".concat(context.emotionalTone.personality.substring(0, 60), \"...\"));\n            }\n        }\n        return contextParts.length > 0 ? (isArabic ? \"السياق الحالي:\\n\" : \"Current Context:\\n\") + contextParts.join(\"\\n\") : isArabic ? \"مشروع جديد\" : \"New Project\";\n    };\n    const parseSuggestions = (content)=>{\n        // تقسيم المحتوى إلى اقتراحات منفصلة\n        const lines = content.split(\"\\n\").filter((line)=>line.trim());\n        const suggestions = [];\n        for (const line of lines){\n            // البحث عن الأسطر المرقمة أو التي تبدأ برقم\n            if (/^\\d+[.\\-\\)]\\s*/.test(line.trim()) || /^[•\\-\\*]\\s*/.test(line.trim())) {\n                const cleaned = line.replace(/^\\d+[.\\-\\)]\\s*/, \"\").replace(/^[•\\-\\*]\\s*/, \"\").trim();\n                if (cleaned && cleaned.length > 10) {\n                    suggestions.push(cleaned);\n                }\n            } else if (line.trim().length > 20 && !line.includes(\":\") && suggestions.length < 3) {\n                suggestions.push(line.trim());\n            }\n        }\n        // إذا لم نجد اقتراحات مرقمة، نقسم النص إلى جمل\n        if (suggestions.length === 0) {\n            const sentences = content.split(/[.!?]+/).filter((s)=>s.trim().length > 20);\n            return sentences.slice(0, 3).map((s)=>s.trim());\n        }\n        return suggestions.slice(0, 3);\n    };\n    const copyToClipboard = async (text, index)=>{\n        try {\n            await navigator.clipboard.writeText(text);\n            setCopiedIndex(index);\n            setTimeout(()=>setCopiedIndex(null), 2000);\n        } catch (error) {\n            console.error(\"Failed to copy:\", error);\n        }\n    };\n    const regenerateContent = async ()=>{\n        if (generatedSuggestions.length > 1) {\n            // استخدام الاقتراح التالي إذا كان متوفراً\n            const currentIndex = generatedSuggestions.findIndex((s)=>s === fieldValue);\n            const nextIndex = (currentIndex + 1) % generatedSuggestions.length;\n            onValueChange(generatedSuggestions[nextIndex]);\n        } else {\n            // توليد محتوى جديد\n            await generateSuggestions();\n        }\n    };\n    // تجنب مشاكل الهيدريشن\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-2 \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative flex items-center gap-2 px-4 py-2.5 rounded-lg font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-blue-500/80 to-indigo-600/80 text-white shadow-md opacity-50 font-arabic\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 416,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: [\n                            \"\\uD83D\\uDCC4 \",\n                            isArabic ? \"توليد بالذكاء الاصطناعي\" : \"Generate with AI\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                lineNumber: 415,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n            lineNumber: 414,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center gap-2 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: generateSuggestions,\n                disabled: isGenerating || !hasValidProvider,\n                className: \"relative flex items-center gap-2 px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group \".concat(hasValidProvider ? \"bg-gradient-to-br from-purple-500/80 via-blue-500/80 to-indigo-600/80 hover:from-purple-600/90 hover:via-blue-600/90 hover:to-indigo-700/90 text-white shadow-lg hover:shadow-xl hover:shadow-purple-500/25 hover:scale-105 active:scale-95\" : \"bg-gray-200/50 dark:bg-gray-700/50 text-gray-400 dark:text-gray-500 cursor-not-allowed border-gray-300/30 dark:border-gray-600/30\", \" \").concat(isGenerating ? \"animate-pulse scale-105\" : \"\", \" \").concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                title: hasValidProvider ? isArabic ? \"توليد سريع محسّن - أقل من 5 ثواني\" : \"Fast optimized generation - under 5 seconds\" : translations.noProviders,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 432,\n                        columnNumber: 9\n                    }, this),\n                    hasValidProvider && !isGenerating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 -skew-x-12 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 group-hover:animate-shimmer\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 436,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex items-center gap-2 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                        children: [\n                            isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-4 h-4 animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-4 h-4 group-hover:rotate-12 transition-transform duration-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 443,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-arabic font-medium\",\n                                children: isGenerating ? translations.generating : translations.fastGeneration\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 439,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                lineNumber: 425,\n                columnNumber: 7\n            }, this),\n            fieldValue && generatedSuggestions.length > 0 && !isGenerating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: regenerateContent,\n                className: \"relative flex items-center gap-2 px-3 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-orange-500/80 to-red-500/80 hover:from-orange-600/90 hover:to-red-600/90 text-white shadow-lg hover:shadow-xl hover:shadow-orange-500/25 hover:scale-105 active:scale-95 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                title: isArabic ? \"إعادة توليد\" : \"Regenerate\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 458,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex items-center gap-1 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-4 h-4 group-hover:rotate-180 transition-transform duration-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-arabic font-medium\",\n                                children: isArabic ? \"إعادة توليد\" : \"Regenerate\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 461,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 459,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                lineNumber: 453,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n        lineNumber: 424,\n        columnNumber: 5\n    }, this);\n}\n_s(SmartFieldAssistant, \"ZrnkxJH8z8I5/fadLS+VIi/I8fM=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore\n    ];\n});\n_c = SmartFieldAssistant;\nvar _c;\n$RefreshReg$(_c, \"SmartFieldAssistant\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/SmartFieldAssistant.tsx\n"));

/***/ })

});