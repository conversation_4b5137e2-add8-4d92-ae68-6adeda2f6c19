'use client';

import { useContextStore } from '@/store/contextStore';

export default function ProjectStats() {
  const { projectDefinition, contextMap, emotionalTone, technicalLayer, legalRisk, currentLanguage } = useContextStore();
  const isArabic = currentLanguage === 'ar';

  // حساب إحصائيات المشروع
  const calculateStats = () => {
    const modules = [
      { name: 'projectDefinition', data: projectDefinition },
      { name: 'contextMap', data: contextMap },
      { name: 'emotionalTone', data: emotionalTone },
      { name: 'technicalLayer', data: technicalLayer },
      { name: 'legalRisk', data: legalRisk }
    ];

    let totalFields = 0;
    let completedFields = 0;
    let totalWords = 0;
    let advancedOptionsUsed = 0;

    modules.forEach(module => {
      const fields = Object.entries(module.data);
      totalFields += fields.length;
      
      fields.forEach(([key, value]) => {
        if (value) {
          if (Array.isArray(value)) {
            if (value.length > 0) {
              completedFields++;
              advancedOptionsUsed++;
              totalWords += value.join(' ').split(' ').length;
            }
          } else if (typeof value === 'string' && value.trim()) {
            completedFields++;
            totalWords += value.split(' ').length;
            
            // تحقق من الخيارات المتقدمة
            if (['projectType', 'complexity', 'teamSize', 'budget', 'deploymentType', 
                 'architecturePattern', 'scalingStrategy', 'securityRequirements', 
                 'performanceTargets', 'integrationNeeds', 'monitoringTools'].includes(key)) {
              advancedOptionsUsed++;
            }
          }
        }
      });
    });

    const completionPercentage = totalFields > 0 ? Math.round((completedFields / totalFields) * 100) : 0;
    
    return {
      totalFields,
      completedFields,
      completionPercentage,
      totalWords,
      advancedOptionsUsed,
      estimatedReadingTime: Math.ceil(totalWords / 200) // متوسط 200 كلمة في الدقيقة
    };
  };

  const stats = calculateStats();

  // تحديد مستوى التقدم
  const getProgressLevel = (percentage: number) => {
    if (percentage >= 80) return { level: 'excellent', color: 'green', icon: '🎉' };
    if (percentage >= 60) return { level: 'good', color: 'blue', icon: '👍' };
    if (percentage >= 40) return { level: 'moderate', color: 'yellow', icon: '⚡' };
    if (percentage >= 20) return { level: 'started', color: 'orange', icon: '🚀' };
    return { level: 'beginning', color: 'gray', icon: '📝' };
  };

  const progress = getProgressLevel(stats.completionPercentage);

  const getProgressMessage = () => {
    const messages = {
      excellent: {
        ar: 'ممتاز! مشروعك مكتمل تقريباً',
        en: 'Excellent! Your project is nearly complete'
      },
      good: {
        ar: 'جيد جداً! تقدم ملحوظ',
        en: 'Very good! Notable progress'
      },
      moderate: {
        ar: 'تقدم جيد، استمر!',
        en: 'Good progress, keep going!'
      },
      started: {
        ar: 'بداية جيدة!',
        en: 'Good start!'
      },
      beginning: {
        ar: 'ابدأ رحلتك!',
        en: 'Start your journey!'
      }
    };
    return messages[progress.level as keyof typeof messages]?.[isArabic ? 'ar' : 'en'] || '';
  };

  if (stats.completedFields === 0) {
    return null;
  }

  return (
    <div className="bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl p-6 border border-green-200 dark:border-green-800">
      <div className={`text-center mb-6 ${isArabic ? 'text-right' : 'text-left'}`}>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
          {isArabic ? '📊 إحصائيات المشروع' : '📊 Project Statistics'}
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          {isArabic 
            ? 'تتبع تقدمك وإنجازاتك'
            : 'Track your progress and achievements'
          }
        </p>
      </div>

      {/* شريط التقدم */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            {isArabic ? 'نسبة الإكمال' : 'Completion Rate'}
          </span>
          <span className="text-sm font-bold text-gray-900 dark:text-gray-100">
            {stats.completionPercentage}%
          </span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
          <div 
            className={`h-3 rounded-full transition-all duration-500 ease-out bg-gradient-to-r ${
              progress.color === 'green' ? 'from-green-400 to-emerald-500' :
              progress.color === 'blue' ? 'from-blue-400 to-cyan-500' :
              progress.color === 'yellow' ? 'from-yellow-400 to-orange-500' :
              progress.color === 'orange' ? 'from-orange-400 to-red-500' :
              'from-gray-400 to-gray-500'
            }`}
            style={{ width: `${stats.completionPercentage}%` }}
          />
        </div>
        <div className={`mt-2 text-center ${isArabic ? 'text-right' : 'text-left'}`}>
          <span className="text-sm text-gray-600 dark:text-gray-400">
            {progress.icon} {getProgressMessage()}
          </span>
        </div>
      </div>

      {/* الإحصائيات التفصيلية */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className={`bg-white dark:bg-gray-800 rounded-lg p-4 text-center ${isArabic ? 'text-right' : 'text-left'}`}>
          <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
            {stats.completedFields}
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            {isArabic ? 'حقول مكتملة' : 'Completed Fields'}
          </div>
          <div className="text-xs text-gray-400 dark:text-gray-500">
            {isArabic ? `من أصل ${stats.totalFields}` : `out of ${stats.totalFields}`}
          </div>
        </div>

        <div className={`bg-white dark:bg-gray-800 rounded-lg p-4 text-center ${isArabic ? 'text-right' : 'text-left'}`}>
          <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
            {stats.totalWords}
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            {isArabic ? 'إجمالي الكلمات' : 'Total Words'}
          </div>
          <div className="text-xs text-gray-400 dark:text-gray-500">
            {isArabic ? `${stats.estimatedReadingTime} دقائق قراءة` : `${stats.estimatedReadingTime} min read`}
          </div>
        </div>

        <div className={`bg-white dark:bg-gray-800 rounded-lg p-4 text-center ${isArabic ? 'text-right' : 'text-left'}`}>
          <div className="text-2xl font-bold text-green-600 dark:text-green-400">
            {stats.advancedOptionsUsed}
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            {isArabic ? 'خيارات متقدمة' : 'Advanced Options'}
          </div>
          <div className="text-xs text-gray-400 dark:text-gray-500">
            {isArabic ? 'مستخدمة' : 'Used'}
          </div>
        </div>

        <div className={`bg-white dark:bg-gray-800 rounded-lg p-4 text-center ${isArabic ? 'text-right' : 'text-left'}`}>
          <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
            {Math.round((stats.advancedOptionsUsed / Math.max(stats.completedFields, 1)) * 100)}%
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            {isArabic ? 'مستوى التخصيص' : 'Customization Level'}
          </div>
          <div className="text-xs text-gray-400 dark:text-gray-500">
            {isArabic ? 'متقدم' : 'Advanced'}
          </div>
        </div>
      </div>

      {/* نصائح للتحسين */}
      {stats.completionPercentage < 100 && (
        <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/30 rounded-lg border border-blue-200 dark:border-blue-700">
          <div className="flex items-start gap-2">
            <span className="text-lg">💡</span>
            <div className={isArabic ? 'text-right' : 'text-left'}>
              <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-1">
                {isArabic ? 'نصيحة للتحسين' : 'Improvement Tip'}
              </h4>
              <p className="text-sm text-blue-700 dark:text-blue-300">
                {isArabic 
                  ? `أكمل ${stats.totalFields - stats.completedFields} حقل إضافي للحصول على سياق أكثر شمولية لمشروعك.`
                  : `Complete ${stats.totalFields - stats.completedFields} more fields to get a more comprehensive context for your project.`
                }
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
