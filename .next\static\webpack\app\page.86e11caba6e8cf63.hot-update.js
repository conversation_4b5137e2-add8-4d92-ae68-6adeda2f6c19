"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header.tsx\");\n/* harmony import */ var _components_ProjectSummaryCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ProjectSummaryCard */ \"(app-pages-browser)/./src/components/ProjectSummaryCard.tsx\");\n/* harmony import */ var _components_SmartRecommendations__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/SmartRecommendations */ \"(app-pages-browser)/./src/components/SmartRecommendations.tsx\");\n/* harmony import */ var _components_ProjectStats__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ProjectStats */ \"(app-pages-browser)/./src/components/ProjectStats.tsx\");\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// import ProjectRoadmap from '@/components/ProjectRoadmap';\n\nfunction Home() {\n    _s();\n    const { currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_5__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 \".concat(isArabic ? \"font-arabic\" : \"\"),\n        dir: isArabic ? \"rtl\" : \"ltr\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-16\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    title: isArabic ? \"Craftery\" : \"Craftery\",\n                    subtitle: isArabic ? \"منصة ذكية مدعومة بالذكاء الاصطناعي لبناء وتطوير الأفكار الإبداعية\" : \"AI-powered Idea Builder\",\n                    emoji: \"\\uD83E\\uDDE0\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProjectSummaryCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SmartRecommendations__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProjectStats__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl mb-4\",\n                                    children: \"\\uD83E\\uDDED\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-2 text-gray-900 dark:text-white\",\n                                    children: isArabic ? \"واجهة متعددة الصفحات\" : \"Multi-page Interface\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300\",\n                                    children: isArabic ? \"صفحات مخصصة لكل وحدة مع أسئلة ومخرجات مصممة خصيصاً.\" : \"Dedicated pages for each module with tailored prompts and outputs.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl mb-4\",\n                                    children: \"✍️\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-2 text-gray-900 dark:text-white\",\n                                    children: isArabic ? \"أسئلة موجهة\" : \"Guided Prompts\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300\",\n                                    children: isArabic ? \"أسئلة ذكية لتوجيه عملية تفكيرك عبر كل وحدة.\" : \"Smart questions to guide your thought process through each module.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl mb-4\",\n                                    children: \"\\uD83D\\uDCCB\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-2 text-gray-900 dark:text-white\",\n                                    children: isArabic ? \"نسخ المخرجات\" : \"Output Copying\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300\",\n                                    children: isArabic ? \"أزرار سهلة الاستخدام لنسخ المخرجات الكاملة أو الإجابات الفردية.\" : \"Easy-to-use buttons for copying entire outputs or individual responses.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl mb-4\",\n                                    children: \"\\uD83E\\uDDFE\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-2 text-gray-900 dark:text-white\",\n                                    children: isArabic ? \"مخرجات منظمة\" : \"Structured Outputs\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300\",\n                                    children: isArabic ? \"مخرجات بصيغة Markdown أو HTML أو JSON للتكامل السهل.\" : \"Outputs in Markdown, HTML, or JSON for easy integration.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl mb-4\",\n                                    children: \"\\uD83D\\uDCBE\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-2 text-gray-900 dark:text-white\",\n                                    children: isArabic ? \"حفظ تلقائي وجلسات\" : \"Auto-save & Sessions\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300\",\n                                    children: isArabic ? \"حفظ واسترجاع تلقائي لجلسات المستخدم للراحة.\" : \"Automatic saving and retrieval of user sessions for convenience.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl mb-4\",\n                                    children: \"\\uD83C\\uDF10\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-2 text-gray-900 dark:text-white\",\n                                    children: isArabic ? \"دعم متعدد اللغات\" : \"Multilingual Support\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300\",\n                                    children: isArabic ? \"واجهات باللغتين الإنجليزية والعربية لخدمة جمهور أوسع.\" : \"English and Arabic interfaces to cater to a wider audience.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold text-center mb-12 text-gray-900 dark:text-white\",\n                            children: [\n                                \"\\uD83D\\uDEE0️ \",\n                                isArabic ? \"الوحدات المتاحة\" : \"Available Modules\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/project-definition\",\n                                    className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-blue-500 hover:shadow-xl transition-shadow block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-3 text-gray-900 dark:text-white\",\n                                            children: [\n                                                \"\\uD83C\\uDFAF \",\n                                                isArabic ? \"تعريف المشروع\" : \"Project Definition\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: isArabic ? \"حدد نطاق مشروعك والمستخدمين والأهداف بأسئلة موجهة.\" : \"Outline the scope, users, and goals of your project with guided prompts.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/interactive\",\n                                    className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-green-500 hover:shadow-xl transition-shadow block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-3 text-gray-900 dark:text-white\",\n                                            children: [\n                                                \"\\uD83E\\uDD16 \",\n                                                isArabic ? \"المساعد التفاعلي\" : \"Interactive Assistant\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: isArabic ? \"تجربة محادثة متقدمة مع الذكاء الاصطناعي مع ميزات تفاعلية حديثة.\" : \"Advanced conversational experience with AI featuring modern interactive capabilities.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/context-map\",\n                                    className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-green-500 hover:shadow-xl transition-shadow block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-3 text-gray-900 dark:text-white\",\n                                            children: [\n                                                \"\\uD83D\\uDDFA️ \",\n                                                isArabic ? \"خريطة السياق\" : \"Context Map\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: isArabic ? \"حدد الوقت واللغة والموقع والجوانب السلوكية لمشروعك.\" : \"Define time, language, location, and behavioral aspects of your project.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/emotional-tone\",\n                                    className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-purple-500 hover:shadow-xl transition-shadow block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-3 text-gray-900 dark:text-white\",\n                                            children: [\n                                                \"✨ \",\n                                                isArabic ? \"النبرة والتجربة\" : \"Emotional Tone & Experience\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: isArabic ? \"احدد النبرة العامة وتجربة المستخدم المرغوبة لمشروعك.\" : \"Capture the overall tone and user experience desired for your project.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/technical-layer\",\n                                    className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-orange-500 hover:shadow-xl transition-shadow block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-3 text-gray-900 dark:text-white\",\n                                            children: [\n                                                \"⚙️ \",\n                                                isArabic ? \"الطبقة التقنية\" : \"Technical Layer\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: isArabic ? \"حدد المتطلبات التقنية والأدوات والنماذج المستخدمة.\" : \"Define technical requirements, tools, and models to be used.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/legal-risk\",\n                                    className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-red-500 hover:shadow-xl transition-shadow block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-3 text-gray-900 dark:text-white\",\n                                            children: [\n                                                \"\\uD83D\\uDD12 \",\n                                                isArabic ? \"التحديات والخصوصية\" : \"Legal & Privacy\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: isArabic ? \"تناول التحديات ومخاوف الخصوصية والقضايا التنظيمية.\" : \"Address challenges, privacy concerns, and regulatory issues.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/final-preview\",\n                                    className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-indigo-500 hover:shadow-xl transition-shadow block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-3 text-gray-900 dark:text-white\",\n                                            children: [\n                                                \"\\uD83D\\uDCCB \",\n                                                isArabic ? \"المعاينة النهائية\" : \"Final Preview\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: isArabic ? \"راجع واستخرج السياق الكامل لمشروعك.\" : \"Review and export your complete project context.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"/project-definition\",\n                            className: \"inline-block bg-blue-600 hover:bg-blue-700 text-white font-bold py-4 px-8 rounded-lg text-lg transition-colors duration-200\",\n                            children: isArabic ? \"ابدأ ببناء السياق\" : \"Start Building Context\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-4 text-gray-600 dark:text-gray-300\",\n                            children: isArabic ? \"هل أنت مستعد لإنشاء سياق منظم وقابل للتنفيذ لمشاريع الذكاء الاصطناعي؟\" : \"Ready to create structured, actionable context for your AI projects?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 221,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"TUcpdwZ+GxByrtxz7lNUh3/XmDk=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_5__.useContextStore\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBRXlDO0FBQ3dCO0FBQ0k7QUFDaEI7QUFDckQsNERBQTREO0FBQ0w7QUFFeEMsU0FBU0s7O0lBQ3RCLE1BQU0sRUFBRUMsZUFBZSxFQUFFLEdBQUdGLG9FQUFlQTtJQUMzQyxNQUFNRyxXQUFXRCxvQkFBb0I7SUFFckMscUJBQ0UsOERBQUNFO1FBQUlDLFdBQVcsaUdBQStILE9BQTlCRixXQUFXLGdCQUFnQjtRQUFNRyxLQUFLSCxXQUFXLFFBQVE7a0JBQ3hLLDRFQUFDQztZQUFJQyxXQUFVOzs4QkFFYiw4REFBQ1QsMERBQU1BO29CQUNMVyxPQUFPSixXQUFXLGFBQWE7b0JBQy9CSyxVQUFVTCxXQUNOLHNFQUNBO29CQUVKTSxPQUFNOzs7Ozs7OEJBSVIsOERBQUNMO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDUixzRUFBa0JBOzs7Ozs7Ozs7OzhCQUlyQiw4REFBQ087b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNQLHdFQUFvQkE7Ozs7Ozs7Ozs7OEJBSXZCLDhEQUFDTTtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ04sZ0VBQVlBOzs7Ozs7Ozs7OzhCQVNmLDhEQUFDSztvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQWdCOzs7Ozs7OENBQy9CLDhEQUFDSztvQ0FBR0wsV0FBVTs4Q0FDWEYsV0FBVyx5QkFBeUI7Ozs7Ozs4Q0FFdkMsOERBQUNRO29DQUFFTixXQUFVOzhDQUNWRixXQUNHLHdEQUNBOzs7Ozs7Ozs7Ozs7c0NBS1IsOERBQUNDOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQWdCOzs7Ozs7OENBQy9CLDhEQUFDSztvQ0FBR0wsV0FBVTs4Q0FDWEYsV0FBVyxnQkFBZ0I7Ozs7Ozs4Q0FFOUIsOERBQUNRO29DQUFFTixXQUFVOzhDQUNWRixXQUNHLGdEQUNBOzs7Ozs7Ozs7Ozs7c0NBS1IsOERBQUNDOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQWdCOzs7Ozs7OENBQy9CLDhEQUFDSztvQ0FBR0wsV0FBVTs4Q0FDWEYsV0FBVyxpQkFBaUI7Ozs7Ozs4Q0FFL0IsOERBQUNRO29DQUFFTixXQUFVOzhDQUNWRixXQUNHLG9FQUNBOzs7Ozs7Ozs7Ozs7c0NBS1IsOERBQUNDOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQWdCOzs7Ozs7OENBQy9CLDhEQUFDSztvQ0FBR0wsV0FBVTs4Q0FDWEYsV0FBVyxpQkFBaUI7Ozs7Ozs4Q0FFL0IsOERBQUNRO29DQUFFTixXQUFVOzhDQUNWRixXQUNHLHlEQUNBOzs7Ozs7Ozs7Ozs7c0NBS1IsOERBQUNDOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQWdCOzs7Ozs7OENBQy9CLDhEQUFDSztvQ0FBR0wsV0FBVTs4Q0FDWEYsV0FBVyxzQkFBc0I7Ozs7Ozs4Q0FFcEMsOERBQUNRO29DQUFFTixXQUFVOzhDQUNWRixXQUNHLGdEQUNBOzs7Ozs7Ozs7Ozs7c0NBS1IsOERBQUNDOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQWdCOzs7Ozs7OENBQy9CLDhEQUFDSztvQ0FBR0wsV0FBVTs4Q0FDWEYsV0FBVyxxQkFBcUI7Ozs7Ozs4Q0FFbkMsOERBQUNRO29DQUFFTixXQUFVOzhDQUNWRixXQUNHLDBEQUNBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBT1YsOERBQUNTO29CQUFRUCxXQUFVOztzQ0FDakIsOERBQUNROzRCQUFHUixXQUFVOztnQ0FBcUU7Z0NBQzVFRixXQUFXLG9CQUFvQjs7Ozs7OztzQ0FFdEMsOERBQUNDOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ1M7b0NBQUVDLE1BQUs7b0NBQXNCVixXQUFVOztzREFDdEMsOERBQUNLOzRDQUFHTCxXQUFVOztnREFBMkQ7Z0RBQ25FRixXQUFXLGtCQUFrQjs7Ozs7OztzREFFbkMsOERBQUNROzRDQUFFTixXQUFVO3NEQUNWRixXQUNHLHVEQUNBOzs7Ozs7Ozs7Ozs7OENBS1IsOERBQUNXO29DQUFFQyxNQUFLO29DQUFlVixXQUFVOztzREFDL0IsOERBQUNLOzRDQUFHTCxXQUFVOztnREFBMkQ7Z0RBQ25FRixXQUFXLHFCQUFxQjs7Ozs7OztzREFFdEMsOERBQUNROzRDQUFFTixXQUFVO3NEQUNWRixXQUNHLG9FQUNBOzs7Ozs7Ozs7Ozs7OENBS1IsOERBQUNXO29DQUFFQyxNQUFLO29DQUFlVixXQUFVOztzREFDL0IsOERBQUNLOzRDQUFHTCxXQUFVOztnREFBMkQ7Z0RBQ2xFRixXQUFXLGlCQUFpQjs7Ozs7OztzREFFbkMsOERBQUNROzRDQUFFTixXQUFVO3NEQUNWRixXQUNHLHdEQUNBOzs7Ozs7Ozs7Ozs7OENBS1IsOERBQUNXO29DQUFFQyxNQUFLO29DQUFrQlYsV0FBVTs7c0RBQ2xDLDhEQUFDSzs0Q0FBR0wsV0FBVTs7Z0RBQTJEO2dEQUNwRUYsV0FBVyxvQkFBb0I7Ozs7Ozs7c0RBRXBDLDhEQUFDUTs0Q0FBRU4sV0FBVTtzREFDVkYsV0FDRyx5REFDQTs7Ozs7Ozs7Ozs7OzhDQUtSLDhEQUFDVztvQ0FBRUMsTUFBSztvQ0FBbUJWLFdBQVU7O3NEQUNuQyw4REFBQ0s7NENBQUdMLFdBQVU7O2dEQUEyRDtnREFDbkVGLFdBQVcsbUJBQW1COzs7Ozs7O3NEQUVwQyw4REFBQ1E7NENBQUVOLFdBQVU7c0RBQ1ZGLFdBQ0csdURBQ0E7Ozs7Ozs7Ozs7Ozs4Q0FLUiw4REFBQ1c7b0NBQUVDLE1BQUs7b0NBQWNWLFdBQVU7O3NEQUM5Qiw4REFBQ0s7NENBQUdMLFdBQVU7O2dEQUEyRDtnREFDbkVGLFdBQVcsdUJBQXVCOzs7Ozs7O3NEQUV4Qyw4REFBQ1E7NENBQUVOLFdBQVU7c0RBQ1ZGLFdBQ0csdURBQ0E7Ozs7Ozs7Ozs7Ozs4Q0FLUiw4REFBQ1c7b0NBQUVDLE1BQUs7b0NBQWlCVixXQUFVOztzREFDakMsOERBQUNLOzRDQUFHTCxXQUFVOztnREFBMkQ7Z0RBQ25FRixXQUFXLHNCQUFzQjs7Ozs7OztzREFFdkMsOERBQUNROzRDQUFFTixXQUFVO3NEQUNWRixXQUNHLHdDQUNBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBUVosOERBQUNDO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ1M7NEJBQ0NDLE1BQUs7NEJBQ0xWLFdBQVU7c0NBRVRGLFdBQVcsc0JBQXNCOzs7Ozs7c0NBRXBDLDhEQUFDUTs0QkFBRU4sV0FBVTtzQ0FDVkYsV0FDRywwRUFDQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPaEI7R0FwT3dCRjs7UUFDTUQsZ0VBQWVBOzs7S0FEckJDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvcGFnZS50c3g/ZjY4YSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBIZWFkZXIgZnJvbSAnQC9jb21wb25lbnRzL0hlYWRlcic7XG5pbXBvcnQgUHJvamVjdFN1bW1hcnlDYXJkIGZyb20gJ0AvY29tcG9uZW50cy9Qcm9qZWN0U3VtbWFyeUNhcmQnO1xuaW1wb3J0IFNtYXJ0UmVjb21tZW5kYXRpb25zIGZyb20gJ0AvY29tcG9uZW50cy9TbWFydFJlY29tbWVuZGF0aW9ucyc7XG5pbXBvcnQgUHJvamVjdFN0YXRzIGZyb20gJ0AvY29tcG9uZW50cy9Qcm9qZWN0U3RhdHMnO1xuLy8gaW1wb3J0IFByb2plY3RSb2FkbWFwIGZyb20gJ0AvY29tcG9uZW50cy9Qcm9qZWN0Um9hZG1hcCc7XG5pbXBvcnQgeyB1c2VDb250ZXh0U3RvcmUgfSBmcm9tICdAL3N0b3JlL2NvbnRleHRTdG9yZSc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEhvbWUoKSB7XG4gIGNvbnN0IHsgY3VycmVudExhbmd1YWdlIH0gPSB1c2VDb250ZXh0U3RvcmUoKTtcbiAgY29uc3QgaXNBcmFiaWMgPSBjdXJyZW50TGFuZ3VhZ2UgPT09ICdhcic7XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17YG1pbi1oLXNjcmVlbiBiZy1ncmFkaWVudC10by1iciBmcm9tLWJsdWUtNTAgdG8taW5kaWdvLTEwMCBkYXJrOmZyb20tZ3JheS05MDAgZGFyazp0by1ncmF5LTgwMCAke2lzQXJhYmljID8gJ2ZvbnQtYXJhYmljJyA6ICcnfWB9IGRpcj17aXNBcmFiaWMgPyAncnRsJyA6ICdsdHInfT5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcHgtNCBweS0xNlwiPlxuICAgICAgICB7LyogSGVhZGVyICovfVxuICAgICAgICA8SGVhZGVyXG4gICAgICAgICAgdGl0bGU9e2lzQXJhYmljID8gXCJDcmFmdGVyeVwiIDogXCJDcmFmdGVyeVwifVxuICAgICAgICAgIHN1YnRpdGxlPXtpc0FyYWJpY1xuICAgICAgICAgICAgPyBcItmF2YbYtdipINiw2YPZitipINmF2K/YudmI2YXYqSDYqNin2YTYsNmD2KfYoSDYp9mE2KfYtdi32YbYp9i52Yog2YTYqNmG2KfYoSDZiNiq2LfZiNmK2LEg2KfZhNij2YHZg9in2LEg2KfZhNil2KjYr9in2LnZitipXCJcbiAgICAgICAgICAgIDogXCJBSS1wb3dlcmVkIElkZWEgQnVpbGRlclwiXG4gICAgICAgICAgfVxuICAgICAgICAgIGVtb2ppPVwi8J+noFwiXG4gICAgICAgIC8+XG5cbiAgICAgICAgey8qIFByb2plY3QgU3VtbWFyeSBDYXJkICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLThcIj5cbiAgICAgICAgICA8UHJvamVjdFN1bW1hcnlDYXJkIC8+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBTbWFydCBSZWNvbW1lbmRhdGlvbnMgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItOFwiPlxuICAgICAgICAgIDxTbWFydFJlY29tbWVuZGF0aW9ucyAvPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogUHJvamVjdCBTdGF0aXN0aWNzICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTEyXCI+XG4gICAgICAgICAgPFByb2plY3RTdGF0cyAvPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogUHJvamVjdCBSb2FkbWFwICovfVxuICAgICAgICB7LyogPGRpdiBjbGFzc05hbWU9XCJtYi0xMlwiPlxuICAgICAgICAgIDxQcm9qZWN0Um9hZG1hcCAvPlxuICAgICAgICA8L2Rpdj4gKi99XG5cbiAgICAgICAgey8qIEZlYXR1cmVzIEdyaWQgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtMyBnYXAtOCBtYi0xNlwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgZGFyazpiZy1ncmF5LTgwMCBwLTYgcm91bmRlZC1sZyBzaGFkb3ctbGdcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0zeGwgbWItNFwiPvCfp608L2Rpdj5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgbWItMiB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICB7aXNBcmFiaWMgPyBcItmI2KfYrNmH2Kkg2YXYqti52K/Yr9ipINin2YTYtdmB2K3Yp9iqXCIgOiBcIk11bHRpLXBhZ2UgSW50ZXJmYWNlXCJ9XG4gICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS0zMDBcIj5cbiAgICAgICAgICAgICAge2lzQXJhYmljXG4gICAgICAgICAgICAgICAgPyBcIti12YHYrdin2Kog2YXYrti12LXYqSDZhNmD2YQg2YjYrdiv2Kkg2YXYuSDYo9iz2KbZhNipINmI2YXYrtix2KzYp9iqINmF2LXZhdmF2Kkg2K7YtdmK2LXYp9mLLlwiXG4gICAgICAgICAgICAgICAgOiBcIkRlZGljYXRlZCBwYWdlcyBmb3IgZWFjaCBtb2R1bGUgd2l0aCB0YWlsb3JlZCBwcm9tcHRzIGFuZCBvdXRwdXRzLlwiXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgZGFyazpiZy1ncmF5LTgwMCBwLTYgcm91bmRlZC1sZyBzaGFkb3ctbGdcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0zeGwgbWItNFwiPuKcje+4jzwvZGl2PlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCBtYi0yIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgIHtpc0FyYWJpYyA/IFwi2KPYs9im2YTYqSDZhdmI2KzZh9ipXCIgOiBcIkd1aWRlZCBQcm9tcHRzXCJ9XG4gICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS0zMDBcIj5cbiAgICAgICAgICAgICAge2lzQXJhYmljXG4gICAgICAgICAgICAgICAgPyBcItij2LPYptmE2Kkg2LDZg9mK2Kkg2YTYqtmI2KzZitmHINi52YXZhNmK2Kkg2KrZgdmD2YrYsdmDINi52KjYsSDZg9mEINmI2K3Yr9ipLlwiXG4gICAgICAgICAgICAgICAgOiBcIlNtYXJ0IHF1ZXN0aW9ucyB0byBndWlkZSB5b3VyIHRob3VnaHQgcHJvY2VzcyB0aHJvdWdoIGVhY2ggbW9kdWxlLlwiXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgZGFyazpiZy1ncmF5LTgwMCBwLTYgcm91bmRlZC1sZyBzaGFkb3ctbGdcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0zeGwgbWItNFwiPvCfk4s8L2Rpdj5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgbWItMiB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICB7aXNBcmFiaWMgPyBcItmG2LPYriDYp9mE2YXYrtix2KzYp9iqXCIgOiBcIk91dHB1dCBDb3B5aW5nXCJ9XG4gICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS0zMDBcIj5cbiAgICAgICAgICAgICAge2lzQXJhYmljXG4gICAgICAgICAgICAgICAgPyBcItij2LLYsdin2LEg2LPZh9mE2Kkg2KfZhNin2LPYqtiu2K/Yp9mFINmE2YbYs9iuINin2YTZhdiu2LHYrNin2Kog2KfZhNmD2KfZhdmE2Kkg2KPZiCDYp9mE2KXYrNin2KjYp9iqINin2YTZgdix2K/ZitipLlwiXG4gICAgICAgICAgICAgICAgOiBcIkVhc3ktdG8tdXNlIGJ1dHRvbnMgZm9yIGNvcHlpbmcgZW50aXJlIG91dHB1dHMgb3IgaW5kaXZpZHVhbCByZXNwb25zZXMuXCJcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBkYXJrOmJnLWdyYXktODAwIHAtNiByb3VuZGVkLWxnIHNoYWRvdy1sZ1wiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBtYi00XCI+8J+nvjwvZGl2PlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCBtYi0yIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgIHtpc0FyYWJpYyA/IFwi2YXYrtix2KzYp9iqINmF2YbYuNmF2KlcIiA6IFwiU3RydWN0dXJlZCBPdXRwdXRzXCJ9XG4gICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS0zMDBcIj5cbiAgICAgICAgICAgICAge2lzQXJhYmljXG4gICAgICAgICAgICAgICAgPyBcItmF2K7Ysdis2KfYqiDYqNi12YrYutipIE1hcmtkb3duINij2YggSFRNTCDYo9mIIEpTT04g2YTZhNiq2YPYp9mF2YQg2KfZhNiz2YfZhC5cIlxuICAgICAgICAgICAgICAgIDogXCJPdXRwdXRzIGluIE1hcmtkb3duLCBIVE1MLCBvciBKU09OIGZvciBlYXN5IGludGVncmF0aW9uLlwiXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgZGFyazpiZy1ncmF5LTgwMCBwLTYgcm91bmRlZC1sZyBzaGFkb3ctbGdcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0zeGwgbWItNFwiPvCfkr48L2Rpdj5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgbWItMiB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICB7aXNBcmFiaWMgPyBcItit2YHYuCDYqtmE2YLYp9im2Yog2YjYrNmE2LPYp9iqXCIgOiBcIkF1dG8tc2F2ZSAmIFNlc3Npb25zXCJ9XG4gICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS0zMDBcIj5cbiAgICAgICAgICAgICAge2lzQXJhYmljXG4gICAgICAgICAgICAgICAgPyBcItit2YHYuCDZiNin2LPYqtix2KzYp9i5INiq2YTZgtin2KbZiiDZhNis2YTYs9in2Kog2KfZhNmF2LPYqtiu2K/ZhSDZhNmE2LHYp9it2KkuXCJcbiAgICAgICAgICAgICAgICA6IFwiQXV0b21hdGljIHNhdmluZyBhbmQgcmV0cmlldmFsIG9mIHVzZXIgc2Vzc2lvbnMgZm9yIGNvbnZlbmllbmNlLlwiXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgZGFyazpiZy1ncmF5LTgwMCBwLTYgcm91bmRlZC1sZyBzaGFkb3ctbGdcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0zeGwgbWItNFwiPvCfjJA8L2Rpdj5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgbWItMiB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICB7aXNBcmFiaWMgPyBcItiv2LnZhSDZhdiq2LnYr9ivINin2YTZhNi62KfYqlwiIDogXCJNdWx0aWxpbmd1YWwgU3VwcG9ydFwifVxuICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgZGFyazp0ZXh0LWdyYXktMzAwXCI+XG4gICAgICAgICAgICAgIHtpc0FyYWJpY1xuICAgICAgICAgICAgICAgID8gXCLZiNin2KzZh9in2Kog2KjYp9mE2YTYutiq2YrZhiDYp9mE2KXZhtis2YTZitiy2YrYqSDZiNin2YTYudix2KjZitipINmE2K7Yr9mF2Kkg2KzZhdmH2YjYsSDYo9mI2LPYuS5cIlxuICAgICAgICAgICAgICAgIDogXCJFbmdsaXNoIGFuZCBBcmFiaWMgaW50ZXJmYWNlcyB0byBjYXRlciB0byBhIHdpZGVyIGF1ZGllbmNlLlwiXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIE1vZHVsZXMgU2VjdGlvbiAqL31cbiAgICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwibWItMTZcIj5cbiAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtY2VudGVyIG1iLTEyIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICDwn5ug77iPIHtpc0FyYWJpYyA/IFwi2KfZhNmI2K3Yr9in2Kog2KfZhNmF2KrYp9it2KlcIiA6IFwiQXZhaWxhYmxlIE1vZHVsZXNcIn1cbiAgICAgICAgICA8L2gyPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBtZDpncmlkLWNvbHMtMiBnYXAtNlwiPlxuICAgICAgICAgICAgPGEgaHJlZj1cIi9wcm9qZWN0LWRlZmluaXRpb25cIiBjbGFzc05hbWU9XCJiZy13aGl0ZSBkYXJrOmJnLWdyYXktODAwIHAtNiByb3VuZGVkLWxnIHNoYWRvdy1sZyBib3JkZXItbC00IGJvcmRlci1ibHVlLTUwMCBob3ZlcjpzaGFkb3cteGwgdHJhbnNpdGlvbi1zaGFkb3cgYmxvY2tcIj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCBtYi0zIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgICAg8J+OryB7aXNBcmFiaWMgPyBcItiq2LnYsdmK2YEg2KfZhNmF2LTYsdmI2LlcIiA6IFwiUHJvamVjdCBEZWZpbml0aW9uXCJ9XG4gICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgZGFyazp0ZXh0LWdyYXktMzAwXCI+XG4gICAgICAgICAgICAgICAge2lzQXJhYmljXG4gICAgICAgICAgICAgICAgICA/IFwi2K3Yr9ivINmG2LfYp9mCINmF2LTYsdmI2LnZgyDZiNin2YTZhdiz2KrYrtiv2YXZitmGINmI2KfZhNij2YfYr9in2YEg2KjYo9iz2KbZhNipINmF2YjYrNmH2KkuXCJcbiAgICAgICAgICAgICAgICAgIDogXCJPdXRsaW5lIHRoZSBzY29wZSwgdXNlcnMsIGFuZCBnb2FscyBvZiB5b3VyIHByb2plY3Qgd2l0aCBndWlkZWQgcHJvbXB0cy5cIlxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9hPlxuXG4gICAgICAgICAgICA8YSBocmVmPVwiL2ludGVyYWN0aXZlXCIgY2xhc3NOYW1lPVwiYmctd2hpdGUgZGFyazpiZy1ncmF5LTgwMCBwLTYgcm91bmRlZC1sZyBzaGFkb3ctbGcgYm9yZGVyLWwtNCBib3JkZXItZ3JlZW4tNTAwIGhvdmVyOnNoYWRvdy14bCB0cmFuc2l0aW9uLXNoYWRvdyBibG9ja1wiPlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIG1iLTMgdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGVcIj5cbiAgICAgICAgICAgICAgICDwn6SWIHtpc0FyYWJpYyA/IFwi2KfZhNmF2LPYp9i52K8g2KfZhNiq2YHYp9i52YTZilwiIDogXCJJbnRlcmFjdGl2ZSBBc3Npc3RhbnRcIn1cbiAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS0zMDBcIj5cbiAgICAgICAgICAgICAgICB7aXNBcmFiaWNcbiAgICAgICAgICAgICAgICAgID8gXCLYqtis2LHYqNipINmF2K3Yp9iv2KvYqSDZhdiq2YLYr9mF2Kkg2YXYuSDYp9mE2LDZg9in2KEg2KfZhNin2LXYt9mG2KfYudmKINmF2Lkg2YXZitiy2KfYqiDYqtmB2KfYudmE2YrYqSDYrdiv2YrYq9ipLlwiXG4gICAgICAgICAgICAgICAgICA6IFwiQWR2YW5jZWQgY29udmVyc2F0aW9uYWwgZXhwZXJpZW5jZSB3aXRoIEFJIGZlYXR1cmluZyBtb2Rlcm4gaW50ZXJhY3RpdmUgY2FwYWJpbGl0aWVzLlwiXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8L2E+XG5cbiAgICAgICAgICAgIDxhIGhyZWY9XCIvY29udGV4dC1tYXBcIiBjbGFzc05hbWU9XCJiZy13aGl0ZSBkYXJrOmJnLWdyYXktODAwIHAtNiByb3VuZGVkLWxnIHNoYWRvdy1sZyBib3JkZXItbC00IGJvcmRlci1ncmVlbi01MDAgaG92ZXI6c2hhZG93LXhsIHRyYW5zaXRpb24tc2hhZG93IGJsb2NrXCI+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgbWItMyB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgIPCfl7rvuI8ge2lzQXJhYmljID8gXCLYrtix2YrYt9ipINin2YTYs9mK2KfZglwiIDogXCJDb250ZXh0IE1hcFwifVxuICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1ncmF5LTMwMFwiPlxuICAgICAgICAgICAgICAgIHtpc0FyYWJpY1xuICAgICAgICAgICAgICAgICAgPyBcItit2K/YryDYp9mE2YjZgtiqINmI2KfZhNmE2LrYqSDZiNin2YTZhdmI2YLYuSDZiNin2YTYrNmI2KfZhtioINin2YTYs9mE2YjZg9mK2Kkg2YTZhdi02LHZiNi52YMuXCJcbiAgICAgICAgICAgICAgICAgIDogXCJEZWZpbmUgdGltZSwgbGFuZ3VhZ2UsIGxvY2F0aW9uLCBhbmQgYmVoYXZpb3JhbCBhc3BlY3RzIG9mIHlvdXIgcHJvamVjdC5cIlxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9hPlxuXG4gICAgICAgICAgICA8YSBocmVmPVwiL2Vtb3Rpb25hbC10b25lXCIgY2xhc3NOYW1lPVwiYmctd2hpdGUgZGFyazpiZy1ncmF5LTgwMCBwLTYgcm91bmRlZC1sZyBzaGFkb3ctbGcgYm9yZGVyLWwtNCBib3JkZXItcHVycGxlLTUwMCBob3ZlcjpzaGFkb3cteGwgdHJhbnNpdGlvbi1zaGFkb3cgYmxvY2tcIj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCBtYi0zIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgICAg4pyoIHtpc0FyYWJpYyA/IFwi2KfZhNmG2KjYsdipINmI2KfZhNiq2KzYsdio2KlcIiA6IFwiRW1vdGlvbmFsIFRvbmUgJiBFeHBlcmllbmNlXCJ9XG4gICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgZGFyazp0ZXh0LWdyYXktMzAwXCI+XG4gICAgICAgICAgICAgICAge2lzQXJhYmljXG4gICAgICAgICAgICAgICAgICA/IFwi2KfYrdiv2K8g2KfZhNmG2KjYsdipINin2YTYudin2YXYqSDZiNiq2KzYsdio2Kkg2KfZhNmF2LPYqtiu2K/ZhSDYp9mE2YXYsdi62YjYqNipINmE2YXYtNix2YjYudmDLlwiXG4gICAgICAgICAgICAgICAgICA6IFwiQ2FwdHVyZSB0aGUgb3ZlcmFsbCB0b25lIGFuZCB1c2VyIGV4cGVyaWVuY2UgZGVzaXJlZCBmb3IgeW91ciBwcm9qZWN0LlwiXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8L2E+XG5cbiAgICAgICAgICAgIDxhIGhyZWY9XCIvdGVjaG5pY2FsLWxheWVyXCIgY2xhc3NOYW1lPVwiYmctd2hpdGUgZGFyazpiZy1ncmF5LTgwMCBwLTYgcm91bmRlZC1sZyBzaGFkb3ctbGcgYm9yZGVyLWwtNCBib3JkZXItb3JhbmdlLTUwMCBob3ZlcjpzaGFkb3cteGwgdHJhbnNpdGlvbi1zaGFkb3cgYmxvY2tcIj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCBtYi0zIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgICAg4pqZ77iPIHtpc0FyYWJpYyA/IFwi2KfZhNi32KjZgtipINin2YTYqtmC2YbZitipXCIgOiBcIlRlY2huaWNhbCBMYXllclwifVxuICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1ncmF5LTMwMFwiPlxuICAgICAgICAgICAgICAgIHtpc0FyYWJpY1xuICAgICAgICAgICAgICAgICAgPyBcItit2K/YryDYp9mE2YXYqti32YTYqNin2Kog2KfZhNiq2YLZhtmK2Kkg2YjYp9mE2KPYr9mI2KfYqiDZiNin2YTZhtmF2KfYsNisINin2YTZhdiz2KrYrtiv2YXYqS5cIlxuICAgICAgICAgICAgICAgICAgOiBcIkRlZmluZSB0ZWNobmljYWwgcmVxdWlyZW1lbnRzLCB0b29scywgYW5kIG1vZGVscyB0byBiZSB1c2VkLlwiXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8L2E+XG5cbiAgICAgICAgICAgIDxhIGhyZWY9XCIvbGVnYWwtcmlza1wiIGNsYXNzTmFtZT1cImJnLXdoaXRlIGRhcms6YmctZ3JheS04MDAgcC02IHJvdW5kZWQtbGcgc2hhZG93LWxnIGJvcmRlci1sLTQgYm9yZGVyLXJlZC01MDAgaG92ZXI6c2hhZG93LXhsIHRyYW5zaXRpb24tc2hhZG93IGJsb2NrXCI+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgbWItMyB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgIPCflJIge2lzQXJhYmljID8gXCLYp9mE2KrYrdiv2YrYp9iqINmI2KfZhNiu2LXZiNi12YrYqVwiIDogXCJMZWdhbCAmIFByaXZhY3lcIn1cbiAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS0zMDBcIj5cbiAgICAgICAgICAgICAgICB7aXNBcmFiaWNcbiAgICAgICAgICAgICAgICAgID8gXCLYqtmG2KfZiNmEINin2YTYqtit2K/Zitin2Kog2YjZhdiu2KfZiNmBINin2YTYrti12YjYtdmK2Kkg2YjYp9mE2YLYttin2YrYpyDYp9mE2KrZhti42YrZhdmK2KkuXCJcbiAgICAgICAgICAgICAgICAgIDogXCJBZGRyZXNzIGNoYWxsZW5nZXMsIHByaXZhY3kgY29uY2VybnMsIGFuZCByZWd1bGF0b3J5IGlzc3Vlcy5cIlxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9hPlxuXG4gICAgICAgICAgICA8YSBocmVmPVwiL2ZpbmFsLXByZXZpZXdcIiBjbGFzc05hbWU9XCJiZy13aGl0ZSBkYXJrOmJnLWdyYXktODAwIHAtNiByb3VuZGVkLWxnIHNoYWRvdy1sZyBib3JkZXItbC00IGJvcmRlci1pbmRpZ28tNTAwIGhvdmVyOnNoYWRvdy14bCB0cmFuc2l0aW9uLXNoYWRvdyBibG9ja1wiPlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIG1iLTMgdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGVcIj5cbiAgICAgICAgICAgICAgICDwn5OLIHtpc0FyYWJpYyA/IFwi2KfZhNmF2LnYp9mK2YbYqSDYp9mE2YbZh9in2KbZitipXCIgOiBcIkZpbmFsIFByZXZpZXdcIn1cbiAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS0zMDBcIj5cbiAgICAgICAgICAgICAgICB7aXNBcmFiaWNcbiAgICAgICAgICAgICAgICAgID8gXCLYsdin2KzYuSDZiNin2LPYqtiu2LHYrCDYp9mE2LPZitin2YIg2KfZhNmD2KfZhdmEINmE2YXYtNix2YjYudmDLlwiXG4gICAgICAgICAgICAgICAgICA6IFwiUmV2aWV3IGFuZCBleHBvcnQgeW91ciBjb21wbGV0ZSBwcm9qZWN0IGNvbnRleHQuXCJcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9zZWN0aW9uPlxuXG4gICAgICAgIHsvKiBDVEEgU2VjdGlvbiAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgIDxhXG4gICAgICAgICAgICBocmVmPVwiL3Byb2plY3QtZGVmaW5pdGlvblwiXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJpbmxpbmUtYmxvY2sgYmctYmx1ZS02MDAgaG92ZXI6YmctYmx1ZS03MDAgdGV4dC13aGl0ZSBmb250LWJvbGQgcHktNCBweC04IHJvdW5kZWQtbGcgdGV4dC1sZyB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIHtpc0FyYWJpYyA/IFwi2KfYqNiv2KMg2KjYqNmG2KfYoSDYp9mE2LPZitin2YJcIiA6IFwiU3RhcnQgQnVpbGRpbmcgQ29udGV4dFwifVxuICAgICAgICAgIDwvYT5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC00IHRleHQtZ3JheS02MDAgZGFyazp0ZXh0LWdyYXktMzAwXCI+XG4gICAgICAgICAgICB7aXNBcmFiaWNcbiAgICAgICAgICAgICAgPyBcItmH2YQg2KPZhtiqINmF2LPYqti52K8g2YTYpdmG2LTYp9ihINiz2YrYp9mCINmF2YbYuNmFINmI2YLYp9io2YQg2YTZhNiq2YbZgdmK2LAg2YTZhdi02KfYsdmK2Lkg2KfZhNiw2YPYp9ihINin2YTYp9i12LfZhtin2LnZitifXCJcbiAgICAgICAgICAgICAgOiBcIlJlYWR5IHRvIGNyZWF0ZSBzdHJ1Y3R1cmVkLCBhY3Rpb25hYmxlIGNvbnRleHQgZm9yIHlvdXIgQUkgcHJvamVjdHM/XCJcbiAgICAgICAgICAgIH1cbiAgICAgICAgICA8L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiSGVhZGVyIiwiUHJvamVjdFN1bW1hcnlDYXJkIiwiU21hcnRSZWNvbW1lbmRhdGlvbnMiLCJQcm9qZWN0U3RhdHMiLCJ1c2VDb250ZXh0U3RvcmUiLCJIb21lIiwiY3VycmVudExhbmd1YWdlIiwiaXNBcmFiaWMiLCJkaXYiLCJjbGFzc05hbWUiLCJkaXIiLCJ0aXRsZSIsInN1YnRpdGxlIiwiZW1vamkiLCJoMyIsInAiLCJzZWN0aW9uIiwiaDIiLCJhIiwiaHJlZiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});