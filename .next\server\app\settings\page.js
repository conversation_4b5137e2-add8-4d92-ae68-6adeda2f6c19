/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/settings/page";
exports.ids = ["app/settings/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsettings%2Fpage&page=%2Fsettings%2Fpage&appPaths=%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fsettings%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsettings%2Fpage&page=%2Fsettings%2Fpage&appPaths=%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fsettings%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'settings',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/settings/page.tsx */ \"(rsc)/./src/app/settings/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/settings/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/settings/page\",\n        pathname: \"/settings\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsettings%2Fpage&page=%2Fsettings%2Fpage&appPaths=%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fsettings%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CStoreHydration.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Cstyles%5C%5Cglass-effects.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Cstyles%5C%5Ctext-improvements.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CStoreHydration.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Cstyles%5C%5Cglass-effects.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Cstyles%5C%5Ctext-improvements.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/StoreHydration.tsx */ \"(ssr)/./src/components/StoreHydration.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ThemeProvider.tsx */ \"(ssr)/./src/components/ThemeProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2ZhaXNzJTVDJTVDRGVza3RvcCU1QyU1Q0NvbnRleHRLaXQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNmYWlzcyU1QyU1Q0Rlc2t0b3AlNUMlNUNDb250ZXh0S2l0JTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q1N0b3JlSHlkcmF0aW9uLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDZmFpc3MlNUMlNUNEZXNrdG9wJTVDJTVDQ29udGV4dEtpdCU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNUaGVtZVByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlRoZW1lUHJvdmlkZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDZmFpc3MlNUMlNUNEZXNrdG9wJTVDJTVDQ29udGV4dEtpdCU1QyU1Q3NyYyU1QyU1Q3N0eWxlcyU1QyU1Q2dsYXNzLWVmZmVjdHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2ZhaXNzJTVDJTVDRGVza3RvcCU1QyU1Q0NvbnRleHRLaXQlNUMlNUNzcmMlNUMlNUNzdHlsZXMlNUMlNUN0ZXh0LWltcHJvdmVtZW50cy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtMQUE0STtBQUM1STtBQUNBLGdMQUFpSiIsInNvdXJjZXMiOlsid2VicGFjazovL2NyYWZ0ZXJ5Lz8yZWRkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXGZhaXNzXFxcXERlc2t0b3BcXFxcQ29udGV4dEtpdFxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxTdG9yZUh5ZHJhdGlvbi50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlRoZW1lUHJvdmlkZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxmYWlzc1xcXFxEZXNrdG9wXFxcXENvbnRleHRLaXRcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcVGhlbWVQcm92aWRlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CStoreHydration.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Cstyles%5C%5Cglass-effects.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Cstyles%5C%5Ctext-improvements.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/settings/page.tsx */ \"(ssr)/./src/app/settings/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2ZhaXNzJTVDJTVDRGVza3RvcCU1QyU1Q0NvbnRleHRLaXQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNzZXR0aW5ncyU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrS0FBd0ciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jcmFmdGVyeS8/M2QxMSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGZhaXNzXFxcXERlc2t0b3BcXFxcQ29udGV4dEtpdFxcXFxzcmNcXFxcYXBwXFxcXHNldHRpbmdzXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/settings/page.tsx":
/*!***********************************!*\
  !*** ./src/app/settings/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SettingsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(ssr)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* harmony import */ var _lib_llmProviders__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/llmProviders */ \"(ssr)/./src/lib/llmProviders.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/test-tube.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ThemeToggle__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ThemeToggle */ \"(ssr)/./src/components/ThemeToggle.tsx\");\n/* harmony import */ var _components_LanguageToggle__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/LanguageToggle */ \"(ssr)/./src/components/LanguageToggle.tsx\");\n/* harmony import */ var _components_TestAIGeneration__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/TestAIGeneration */ \"(ssr)/./src/components/TestAIGeneration.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nfunction SettingsPage() {\n    const { currentLanguage, apiSettings, showAdvancedOptions, setShowAdvancedOptions, addProvider, updateProvider, removeProvider, validateProvider, getProvider } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_3__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    const [showKeys, setShowKeys] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [validationStates, setValidationStates] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [showAddProvider, setShowAddProvider] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [selectedProviderId, setSelectedProviderId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [errorMessage, setErrorMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [expandedProviders, setExpandedProviders] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [selectedModels, setSelectedModels] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [newCustomModel, setNewCustomModel] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    // تهيئة النماذج المحددة عند تحميل الصفحة\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const configuredProviders = apiSettings.providers || [];\n        const initialModels = {};\n        configuredProviders.forEach((provider)=>{\n            initialModels[provider.id] = provider.selectedModels || [];\n        });\n        setSelectedModels(initialModels);\n    }, [\n        apiSettings.providers\n    ]);\n    const translations = {\n        title: isArabic ? \"إعدادات نماذج الذكاء الاصطناعي\" : \"LLM API Settings\",\n        subtitle: isArabic ? \"قم بتكوين مفاتيح واجهة برمجة التطبيقات ونماذج الذكاء الاصطناعي\" : \"Configure your API keys and AI models\",\n        providers: isArabic ? \"مقدمو خدمات الذكاء الاصطناعي\" : \"LLM Providers\",\n        addProvider: isArabic ? \"إضافة مقدم خدمة جديد\" : \"Add Provider\",\n        apiKey: isArabic ? \"مفتاح واجهة برمجة التطبيقات\" : \"API Key\",\n        baseUrl: isArabic ? \"الرابط الأساسي للخدمة\" : \"Base URL\",\n        testConnection: isArabic ? \"اختبار الاتصال\" : \"Test Connection\",\n        validating: isArabic ? \"جاري التحقق من الاتصال...\" : \"Validating...\",\n        valid: isArabic ? \"الاتصال صحيح\" : \"Valid\",\n        invalid: isArabic ? \"الاتصال غير صحيح\" : \"Invalid\",\n        error: isArabic ? \"حدث خطأ في الاتصال\" : \"Error\",\n        models: isArabic ? \"النماذج المتوفرة\" : \"Available Models\",\n        selectedModels: isArabic ? \"النماذج المختارة\" : \"Selected Models\",\n        addCustomModel: isArabic ? \"إضافة نموذج مخصص\" : \"Add Custom Model\",\n        customModelName: isArabic ? \"اسم النموذج المخصص\" : \"Custom Model Name\",\n        editModels: isArabic ? \"تحرير النماذج\" : \"Edit Models\",\n        saveModels: isArabic ? \"حفظ التغييرات\" : \"Save Models\",\n        noModelsSelected: isArabic ? \"لم يتم اختيار أي نماذج بعد\" : \"No models selected\",\n        cancel: isArabic ? \"إلغاء العملية\" : \"Cancel\",\n        add: isArabic ? \"إضافة\" : \"Add\",\n        backToHome: isArabic ? \"العودة إلى الصفحة الرئيسية\" : \"Back to Home\",\n        active: isArabic ? \"مفعل\" : \"Active\",\n        selectProvider: isArabic ? \"اختر مقدم الخدمة\" : \"Select Provider\",\n        noProvidersConfigured: isArabic ? \"لم يتم تكوين أي مقدم خدمة حتى الآن\" : \"No providers configured yet\",\n        providerAlreadyExists: isArabic ? \"مقدم الخدمة موجود مسبقاً\" : \"Provider already exists\",\n        pleaseSelectProvider: isArabic ? \"يرجى اختيار مقدم خدمة من القائمة\" : \"Please select a provider\",\n        providerNotFound: isArabic ? \"لم يتم العثور على مقدم الخدمة\" : \"Provider not found\",\n        errorAddingProvider: isArabic ? \"حدث خطأ أثناء إضافة مقدم الخدمة\" : \"Error adding provider\",\n        // إعدادات عامة\n        generalSettings: isArabic ? \"الإعدادات العامة\" : \"General Settings\",\n        advancedOptions: isArabic ? \"الخيارات المتقدمة\" : \"Advanced Options\",\n        showAdvancedOptions: isArabic ? \"إظهار الخيارات المتقدمة\" : \"Show Advanced Options\",\n        advancedOptionsDescription: isArabic ? \"عرض خيارات التخصيص المتقدمة في صفحات المشروع\" : \"Display advanced customization options in project pages\"\n    };\n    // دوال إدارة النماذج\n    const toggleModelSelection = (providerId, modelId)=>{\n        setSelectedModels((prev)=>{\n            const currentModels = prev[providerId] || [];\n            const isSelected = currentModels.includes(modelId);\n            return {\n                ...prev,\n                [providerId]: isSelected ? currentModels.filter((id)=>id !== modelId) : [\n                    ...currentModels,\n                    modelId\n                ]\n            };\n        });\n    };\n    const addCustomModel = (providerId)=>{\n        if (newCustomModel.trim()) {\n            setSelectedModels((prev)=>{\n                const currentModels = prev[providerId] || [];\n                return {\n                    ...prev,\n                    [providerId]: [\n                        ...currentModels,\n                        newCustomModel.trim()\n                    ]\n                };\n            });\n            setNewCustomModel(\"\");\n        }\n    };\n    const removeCustomModel = (providerId, modelId)=>{\n        setSelectedModels((prev)=>{\n            const currentModels = prev[providerId] || [];\n            return {\n                ...prev,\n                [providerId]: currentModels.filter((id)=>id !== modelId)\n            };\n        });\n    };\n    const saveProviderModels = (providerId)=>{\n        const models = selectedModels[providerId] || [];\n        updateProvider(providerId, {\n            selectedModels: models\n        });\n        setExpandedProviders((prev)=>({\n                ...prev,\n                [providerId]: false\n            }));\n    };\n    const handleAddProvider = async ()=>{\n        setErrorMessage(\"\");\n        if (!selectedProviderId) {\n            setErrorMessage(translations.pleaseSelectProvider);\n            return;\n        }\n        const providerTemplate = (0,_lib_llmProviders__WEBPACK_IMPORTED_MODULE_4__.getProviderById)(selectedProviderId);\n        if (!providerTemplate) {\n            setErrorMessage(translations.providerNotFound);\n            return;\n        }\n        const existingProvider = getProvider(selectedProviderId);\n        if (existingProvider) {\n            setErrorMessage(translations.providerAlreadyExists);\n            setShowAddProvider(false);\n            setSelectedProviderId(\"\");\n            return;\n        }\n        try {\n            const newProvider = {\n                id: selectedProviderId,\n                apiKey: \"\",\n                selectedModels: [],\n                isEnabled: false,\n                validationStatus: \"pending\",\n                priority: 1,\n                isBackup: false\n            };\n            addProvider(newProvider);\n            setShowAddProvider(false);\n            setSelectedProviderId(\"\");\n            setErrorMessage(\"\");\n        } catch (error) {\n            console.error(\"Error adding provider:\", error);\n            setErrorMessage(translations.errorAddingProvider);\n        }\n    };\n    const handleValidateProvider = async (providerId)=>{\n        setValidationStates((prev)=>({\n                ...prev,\n                [providerId]: {\n                    status: \"validating\"\n                }\n            }));\n        try {\n            const isValid = await validateProvider(providerId);\n            setValidationStates((prev)=>({\n                    ...prev,\n                    [providerId]: {\n                        status: isValid ? \"valid\" : \"invalid\",\n                        message: isValid ? translations.valid : translations.invalid,\n                        lastValidated: new Date()\n                    }\n                }));\n        } catch (error) {\n            setValidationStates((prev)=>({\n                    ...prev,\n                    [providerId]: {\n                        status: \"error\",\n                        message: error instanceof Error ? error.message : translations.error\n                    }\n                }));\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"validating\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"w-4 h-4 animate-spin text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 221,\n                    columnNumber: 16\n                }, this);\n            case \"valid\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"w-4 h-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 16\n                }, this);\n            case \"invalid\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"w-4 h-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 16\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"w-4 h-4 text-orange-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"w-4 h-4 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const configuredProviders = apiSettings.providers || [];\n    const availableProviders = _lib_llmProviders__WEBPACK_IMPORTED_MODULE_4__.LLM_PROVIDERS_DATABASE.filter((p)=>!configuredProviders.some((cp)=>cp.id === p.id));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        dir: isArabic ? \"rtl\" : \"ltr\",\n        className: \"jsx-2abf06658c86e3df\" + \" \" + \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"2abf06658c86e3df\",\n                children: '[dir=\"rtl\"].jsx-2abf06658c86e3df{font-family:\"Tajawal\",\"Arial\",sans-serif}[dir=\"rtl\"].jsx-2abf06658c86e3df *.jsx-2abf06658c86e3df{font-family:\"Tajawal\",\"Arial\",sans-serif;text-align:right}[dir=\"rtl\"].jsx-2abf06658c86e3df .page-container.jsx-2abf06658c86e3df{direction:rtl}[dir=\"rtl\"].jsx-2abf06658c86e3df .header-section.jsx-2abf06658c86e3df{direction:rtl}[dir=\"rtl\"].jsx-2abf06658c86e3df .header-content.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between}[dir=\"rtl\"].jsx-2abf06658c86e3df .header-left.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse}[dir=\"rtl\"].jsx-2abf06658c86e3df .header-right.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse}[dir=\"rtl\"].jsx-2abf06658c86e3df .section-header.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between}[dir=\"rtl\"].jsx-2abf06658c86e3df .provider-card.jsx-2abf06658c86e3df{direction:rtl}[dir=\"rtl\"].jsx-2abf06658c86e3df .provider-header.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between}[dir=\"rtl\"].jsx-2abf06658c86e3df .provider-info.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse;text-align:right}[dir=\"rtl\"].jsx-2abf06658c86e3df .provider-controls.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse}[dir=\"rtl\"].jsx-2abf06658c86e3df .form-section.jsx-2abf06658c86e3df{direction:rtl}[dir=\"rtl\"].jsx-2abf06658c86e3df .form-row.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse}[dir=\"rtl\"].jsx-2abf06658c86e3df .models-section.jsx-2abf06658c86e3df{direction:rtl}[dir=\"rtl\"].jsx-2abf06658c86e3df .models-header.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between}[dir=\"rtl\"].jsx-2abf06658c86e3df .model-item.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse}[dir=\"rtl\"].jsx-2abf06658c86e3df .model-tags.jsx-2abf06658c86e3df{-webkit-box-pack:end;-webkit-justify-content:flex-end;-moz-box-pack:end;-ms-flex-pack:end;justify-content:flex-end}[dir=\"rtl\"].jsx-2abf06658c86e3df input[type=\"text\"].jsx-2abf06658c86e3df,[dir=\"rtl\"].jsx-2abf06658c86e3df input[type=\"password\"].jsx-2abf06658c86e3df,[dir=\"rtl\"].jsx-2abf06658c86e3df select.jsx-2abf06658c86e3df,[dir=\"rtl\"].jsx-2abf06658c86e3df textarea.jsx-2abf06658c86e3df{text-align:right;direction:rtl;font-family:\"Tajawal\",\"Arial\",sans-serif}[dir=\"rtl\"].jsx-2abf06658c86e3df .button-group.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse}[dir=\"rtl\"].jsx-2abf06658c86e3df .button-with-icon.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse}[dir=\"rtl\"].jsx-2abf06658c86e3df .modal-content.jsx-2abf06658c86e3df{direction:rtl;text-align:right}[dir=\"rtl\"].jsx-2abf06658c86e3df .modal-buttons.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse}[dir=\"rtl\"].jsx-2abf06658c86e3df .text-content.jsx-2abf06658c86e3df{text-align:right;direction:rtl}[dir=\"rtl\"].jsx-2abf06658c86e3df .font-arabic.jsx-2abf06658c86e3df{font-family:\"Tajawal\",\"Arial\",sans-serif;text-align:right}'\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-2abf06658c86e3df\" + \" \" + \"header-section bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-2abf06658c86e3df\" + \" \" + `header-content flex items-center justify-between h-16 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2abf06658c86e3df\" + \" \" + `header-left flex items-center gap-4 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        href: \"/\",\n                                        className: `flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                        children: [\n                                            isArabic ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-5 h-5 rotate-180\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"font-arabic text-content\",\n                                                children: translations.backToHome\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2abf06658c86e3df\" + \" \" + `flex items-center gap-3 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"w-6 h-6 text-blue-600 dark:text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-content\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-xl font-bold text-gray-900 dark:text-white font-arabic\",\n                                                        children: translations.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm text-gray-600 dark:text-gray-400 font-arabic\",\n                                                        children: translations.subtitle\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2abf06658c86e3df\" + \" \" + `header-right flex items-center gap-3 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LanguageToggle__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeToggle__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 368,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 367,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 366,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-2abf06658c86e3df\" + \" \" + \"page-container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"p-6 border-b border-gray-200 dark:border-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-lg font-semibold text-gray-900 dark:text-white font-arabic text-content\",\n                                        children: translations.generalSettings\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 413,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"space-y-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-2abf06658c86e3df\" + \" \" + `flex items-center justify-between w-full`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + `${isArabic ? \"text-right\" : \"text-left\"}`,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm font-medium text-gray-900 dark:text-white font-arabic\",\n                                                            children: translations.showAdvancedOptions\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 423,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm text-gray-500 dark:text-gray-400 mt-1 font-arabic\",\n                                                            children: translations.advancedOptionsDescription\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 426,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowAdvancedOptions(!showAdvancedOptions),\n                                                        style: {\n                                                            justifyContent: \"left\"\n                                                        },\n                                                        className: \"jsx-2abf06658c86e3df\" + \" \" + `relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${showAdvancedOptions ? \"bg-blue-600\" : \"bg-gray-200 dark:bg-gray-700\"}`,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + `inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${showAdvancedOptions ? \"translate-x-6\" : \"translate-x-1\"}`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 438,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 431,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 418,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 412,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"p-6 border-b border-gray-200 dark:border-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2abf06658c86e3df\" + \" \" + `section-header flex items-center justify-between ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-lg font-semibold text-gray-900 dark:text-white font-arabic text-content\",\n                                                children: translations.providers\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    setShowAddProvider(true);\n                                                    setErrorMessage(\"\");\n                                                    setSelectedProviderId(\"\");\n                                                },\n                                                className: \"jsx-2abf06658c86e3df\" + \" \" + `button-with-icon flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-arabic ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 465,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-content\",\n                                                        children: translations.addProvider\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 452,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"p-6 space-y-4\",\n                                    children: configuredProviders.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-gray-500 dark:text-gray-400 font-arabic text-content\",\n                                                children: translations.noProvidersConfigured\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 17\n                                    }, this) : configuredProviders.map((provider)=>{\n                                        const providerInfo = (0,_lib_llmProviders__WEBPACK_IMPORTED_MODULE_4__.getProviderById)(provider.id);\n                                        const validationState = validationStates[provider.id];\n                                        if (!providerInfo) return null;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"provider-card border border-gray-200 dark:border-gray-600 rounded-lg p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + `provider-header flex items-start justify-between mb-6 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + `provider-info flex items-center gap-4 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-2xl\",\n                                                                    children: providerInfo.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 490,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"space-y-1 text-content\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"font-semibold text-gray-900 dark:text-white font-arabic\",\n                                                                            children: providerInfo.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 492,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm text-gray-600 dark:text-gray-400 font-arabic\",\n                                                                            children: providerInfo.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 495,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 491,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + `provider-controls flex items-center gap-3 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                                            children: [\n                                                                getStatusIcon(validationState?.status || \"idle\"),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + `flex items-center gap-2 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: provider.isEnabled,\n                                                                            onChange: (e)=>updateProvider(provider.id, {\n                                                                                    isEnabled: e.target.checked\n                                                                                }),\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 506,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm text-gray-600 dark:text-gray-400 font-arabic whitespace-nowrap text-content\",\n                                                                            children: translations.active\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 512,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 505,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>removeProvider(provider.id),\n                                                                    title: isArabic ? \"حذف مقدم الخدمة\" : \"Remove Provider\",\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"p-2 text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-colors\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 522,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 517,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 501,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 488,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"form-section space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"grid grid-cols-1 lg:grid-cols-2 gap-4 items-end\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex flex-col\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 font-arabic text-content\",\n                                                                            children: translations.apiKey\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 530,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"relative\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: showKeys[provider.id] ? \"text\" : \"password\",\n                                                                                    value: provider.apiKey,\n                                                                                    onChange: (e)=>updateProvider(provider.id, {\n                                                                                            apiKey: e.target.value\n                                                                                        }),\n                                                                                    placeholder: providerInfo.apiKeyPlaceholder,\n                                                                                    dir: isArabic ? \"rtl\" : \"ltr\",\n                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + `w-full h-10 px-3 py-2 ${isArabic ? \"pr-10 pl-3\" : \"pr-10 pl-3\"} border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent font-arabic`\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 534,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>setShowKeys((prev)=>({\n                                                                                                ...prev,\n                                                                                                [provider.id]: !prev[provider.id]\n                                                                                            })),\n                                                                                    title: isArabic ? showKeys[provider.id] ? \"إخفاء المفتاح\" : \"إظهار المفتاح\" : showKeys[provider.id] ? \"Hide key\" : \"Show key\",\n                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + `absolute ${isArabic ? \"left-3\" : \"right-3\"} top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600`,\n                                                                                    children: showKeys[provider.id] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 547,\n                                                                                        columnNumber: 58\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 547,\n                                                                                        columnNumber: 91\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 542,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 533,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 529,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex flex-col\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 font-arabic text-content\",\n                                                                            children: translations.baseUrl\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 553,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: provider.baseUrl || providerInfo.baseUrl,\n                                                                            onChange: (e)=>updateProvider(provider.id, {\n                                                                                    baseUrl: e.target.value\n                                                                                }),\n                                                                            placeholder: providerInfo.baseUrl,\n                                                                            dir: isArabic ? \"rtl\" : \"ltr\",\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"w-full h-10 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent font-arabic\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 556,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 552,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 528,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"grid grid-cols-1 lg:grid-cols-2 gap-4 mt-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + `flex ${isArabic ? \"justify-start\" : \"justify-start\"}`,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleValidateProvider(provider.id),\n                                                                        disabled: !provider.apiKey || validationState?.status === \"validating\",\n                                                                        className: \"jsx-2abf06658c86e3df\" + \" \" + `button-with-icon flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-arabic ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                className: \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 575,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-content\",\n                                                                                children: validationState?.status === \"validating\" ? translations.validating : translations.testConnection\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 576,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 570,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 569,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + `flex ${isArabic ? \"justify-end\" : \"justify-end\"}`,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm text-gray-600 dark:text-gray-400 font-arabic text-content\",\n                                                                        children: [\n                                                                            translations.models,\n                                                                            \": \",\n                                                                            providerInfo.models.length\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 584,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 583,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 567,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        validationState?.message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + `mt-2 p-2 rounded text-sm text-content ${validationState.status === \"valid\" ? \"bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300\" : \"bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300\"}`,\n                                                            children: validationState.message\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 591,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"models-section mt-4 border-t border-gray-200 dark:border-gray-600 pt-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"grid grid-cols-1 lg:grid-cols-2 gap-4 mb-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + `flex ${isArabic ? \"justify-start\" : \"justify-start\"}`,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm font-medium text-gray-900 dark:text-white font-arabic text-content\",\n                                                                                children: translations.selectedModels\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 605,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 604,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + `flex ${isArabic ? \"justify-end\" : \"justify-end\"}`,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>{\n                                                                                    setExpandedProviders((prev)=>({\n                                                                                            ...prev,\n                                                                                            [provider.id]: !prev[provider.id]\n                                                                                        }));\n                                                                                    if (!selectedModels[provider.id]) {\n                                                                                        setSelectedModels((prev)=>({\n                                                                                                ...prev,\n                                                                                                [provider.id]: provider.selectedModels || []\n                                                                                            }));\n                                                                                    }\n                                                                                },\n                                                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm text-blue-600 dark:text-blue-400 hover:underline font-arabic text-content\",\n                                                                                children: expandedProviders[provider.id] ? translations.saveModels : translations.editModels\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 612,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 611,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 602,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"mb-3\",\n                                                                    children: (provider.selectedModels || []).length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm text-gray-500 dark:text-gray-400 font-arabic text-content\",\n                                                                        children: translations.noModelsSelected\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 629,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2abf06658c86e3df\" + \" \" + `model-tags flex flex-wrap gap-2 ${isArabic ? \"justify-end\" : \"justify-start\"}`,\n                                                                        children: (provider.selectedModels || []).map((modelId)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded text-xs font-arabic text-content\",\n                                                                                children: modelId\n                                                                            }, modelId, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 635,\n                                                                                columnNumber: 35\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 633,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 627,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                expandedProviders[provider.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"space-y-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-2abf06658c86e3df\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm font-medium text-gray-900 dark:text-white mb-2 font-arabic text-content\",\n                                                                                    children: translations.models\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 651,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"grid grid-cols-1 md:grid-cols-2 gap-2 max-h-40 overflow-y-auto\",\n                                                                                    children: providerInfo.models.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + `model-item flex items-center gap-2 p-2 hover:bg-gray-100 dark:hover:bg-gray-600 rounded cursor-pointer ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                    type: \"checkbox\",\n                                                                                                    checked: (selectedModels[provider.id] || []).includes(model.id),\n                                                                                                    onChange: ()=>toggleModelSelection(provider.id, model.id),\n                                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"rounded border-gray-300 text-blue-600 focus:ring-blue-500 flex-shrink-0\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                    lineNumber: 660,\n                                                                                                    columnNumber: 39\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex-1 min-w-0 text-content\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm font-medium text-gray-900 dark:text-white truncate font-arabic\",\n                                                                                                            children: model.name\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                            lineNumber: 667,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-xs text-gray-500 dark:text-gray-400 truncate font-arabic\",\n                                                                                                            children: model.description\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                            lineNumber: 670,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                    lineNumber: 666,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, model.id, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 656,\n                                                                                            columnNumber: 37\n                                                                                        }, this))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 654,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 650,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-2abf06658c86e3df\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm font-medium text-gray-900 dark:text-white mb-2 font-arabic text-content\",\n                                                                                    children: translations.addCustomModel\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 681,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + `flex gap-2 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"text\",\n                                                                                            value: newCustomModel,\n                                                                                            onChange: (e)=>setNewCustomModel(e.target.value),\n                                                                                            placeholder: translations.customModelName,\n                                                                                            onKeyDown: (e)=>e.key === \"Enter\" && addCustomModel(provider.id),\n                                                                                            dir: isArabic ? \"rtl\" : \"ltr\",\n                                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm font-arabic\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 685,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                            onClick: ()=>addCustomModel(provider.id),\n                                                                                            disabled: !newCustomModel.trim(),\n                                                                                            title: isArabic ? \"إضافة النموذج\" : \"Add Model\",\n                                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm flex-shrink-0\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                                className: \"w-4 h-4\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                lineNumber: 700,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 694,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 684,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 680,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        (selectedModels[provider.id] || []).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-2abf06658c86e3df\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm font-medium text-gray-900 dark:text-white mb-2 font-arabic text-content\",\n                                                                                    children: translations.selectedModels\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 708,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + `model-tags flex flex-wrap gap-2 ${isArabic ? \"justify-end\" : \"justify-start\"}`,\n                                                                                    children: (selectedModels[provider.id] || []).map((modelId)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + `flex items-center gap-1 px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded text-xs ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"font-arabic text-content\",\n                                                                                                    children: modelId\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                    lineNumber: 717,\n                                                                                                    columnNumber: 41\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>removeCustomModel(provider.id, modelId),\n                                                                                                    title: isArabic ? \"حذف النموذج\" : \"Remove Model\",\n                                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-blue-600 dark:text-blue-400 hover:text-red-600 dark:hover:text-red-400 flex-shrink-0\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                                        className: \"w-3 h-3\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                        lineNumber: 723,\n                                                                                                        columnNumber: 43\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                    lineNumber: 718,\n                                                                                                    columnNumber: 41\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, modelId, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 713,\n                                                                                            columnNumber: 39\n                                                                                        }, this))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 711,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 707,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + `button-group flex ${isArabic ? \"justify-start\" : \"justify-end\"}`,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>saveProviderModels(provider.id),\n                                                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm font-arabic text-content\",\n                                                                                children: translations.saveModels\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 733,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 732,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 648,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 601,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, provider.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 487,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 471,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 451,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TestAIGeneration__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 752,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 409,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 408,\n                columnNumber: 7\n            }, this),\n            showAddProvider && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-2abf06658c86e3df\" + \" \" + \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    dir: isArabic ? \"rtl\" : \"ltr\",\n                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"modal-content bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-lg font-semibold text-gray-900 dark:text-white mb-4 font-arabic text-content\",\n                            children: translations.addProvider\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 760,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"space-y-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: selectedProviderId,\n                                onChange: (e)=>setSelectedProviderId(e.target.value),\n                                dir: isArabic ? \"rtl\" : \"ltr\",\n                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white font-arabic\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        className: \"jsx-2abf06658c86e3df\",\n                                        children: translations.selectProvider\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 771,\n                                        columnNumber: 17\n                                    }, this),\n                                    availableProviders.map((provider)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: provider.id,\n                                            className: \"jsx-2abf06658c86e3df\",\n                                            children: [\n                                                provider.icon,\n                                                \" \",\n                                                provider.name\n                                            ]\n                                        }, provider.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 773,\n                                            columnNumber: 19\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 765,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 764,\n                            columnNumber: 13\n                        }, this),\n                        errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2abf06658c86e3df\" + \" \" + `flex items-center gap-2 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-4 h-4 text-red-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 784,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm text-red-700 dark:text-red-300 font-arabic text-content\",\n                                        children: errorMessage\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 785,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 783,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 782,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-2abf06658c86e3df\" + \" \" + `modal-buttons flex gap-3 mt-6 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        setShowAddProvider(false);\n                                        setErrorMessage(\"\");\n                                        setSelectedProviderId(\"\");\n                                    },\n                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors font-arabic text-content\",\n                                    children: translations.cancel\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 793,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleAddProvider,\n                                    disabled: !selectedProviderId,\n                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-arabic text-content\",\n                                    children: translations.add\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 803,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 792,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 759,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 758,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n        lineNumber: 239,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/settings/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/LanguageToggle.tsx":
/*!*******************************************!*\
  !*** ./src/components/LanguageToggle.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LanguageToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction LanguageToggle() {\n    const { currentLanguage, setLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_1__.useContextStore)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: ()=>setLanguage(currentLanguage === \"ar\" ? \"en\" : \"ar\"),\n        className: \"relative w-12 h-12 rounded-xl bg-gradient-to-br from-green-50 to-emerald-100 dark:from-gray-800 dark:to-gray-900 hover:from-green-100 hover:to-emerald-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden\",\n        \"aria-label\": currentLanguage === \"ar\" ? \"Switch to English\" : \"التبديل إلى العربية\",\n        title: currentLanguage === \"ar\" ? \"Switch to English\" : \"التبديل إلى العربية\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `text-sm font-bold text-blue-600 dark:text-blue-400 transition-all duration-500 ease-in-out ${currentLanguage === \"en\" ? \"opacity-100 rotate-0 scale-100\" : \"opacity-0 rotate-180 scale-0\"}`,\n                        children: \"EN\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `text-sm font-bold text-green-600 dark:text-green-400 absolute transition-all duration-500 ease-in-out font-arabic ${currentLanguage === \"ar\" ? \"opacity-100 rotate-0 scale-100\" : \"opacity-0 -rotate-180 scale-0\"}`,\n                        children: \"عر\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/LanguageToggle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/StoreHydration.tsx":
/*!*******************************************!*\
  !*** ./src/components/StoreHydration.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StoreHydration)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction StoreHydration() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // Manually hydrate the store to prevent hydration mismatches\n        const store = _store_contextStore__WEBPACK_IMPORTED_MODULE_1__.useContextStore.getState();\n        if (false) {}\n    }, []);\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9TdG9yZUh5ZHJhdGlvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs2REFFa0M7QUFDcUI7QUFFeEMsU0FBU0U7SUFDdEJGLGdEQUFTQSxDQUFDO1FBQ1IsNkRBQTZEO1FBQzdELE1BQU1HLFFBQVFGLGdFQUFlQSxDQUFDRyxRQUFRO1FBQ3RDLElBQUksS0FBa0IsRUFBYSxFQUVsQztJQUNILEdBQUcsRUFBRTtJQUVMLE9BQU87QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL2NyYWZ0ZXJ5Ly4vc3JjL2NvbXBvbmVudHMvU3RvcmVIeWRyYXRpb24udHN4P2EyOTciXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VDb250ZXh0U3RvcmUgfSBmcm9tICdAL3N0b3JlL2NvbnRleHRTdG9yZSc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFN0b3JlSHlkcmF0aW9uKCkge1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIE1hbnVhbGx5IGh5ZHJhdGUgdGhlIHN0b3JlIHRvIHByZXZlbnQgaHlkcmF0aW9uIG1pc21hdGNoZXNcbiAgICBjb25zdCBzdG9yZSA9IHVzZUNvbnRleHRTdG9yZS5nZXRTdGF0ZSgpO1xuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgdXNlQ29udGV4dFN0b3JlLnBlcnNpc3QucmVoeWRyYXRlKCk7XG4gICAgfVxuICB9LCBbXSk7XG5cbiAgcmV0dXJuIG51bGw7XG59XG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlQ29udGV4dFN0b3JlIiwiU3RvcmVIeWRyYXRpb24iLCJzdG9yZSIsImdldFN0YXRlIiwicGVyc2lzdCIsInJlaHlkcmF0ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/StoreHydration.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/TestAIGeneration.tsx":
/*!*********************************************!*\
  !*** ./src/components/TestAIGeneration.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestAIGeneration)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(ssr)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* harmony import */ var _barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Brain,CheckCircle,Database,Key,Loader2,Server,Sparkles,XCircle,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Brain,CheckCircle,Database,Key,Loader2,Server,Sparkles,XCircle,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Brain,CheckCircle,Database,Key,Loader2,Server,Sparkles,XCircle,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Brain,CheckCircle,Database,Key,Loader2,Server,Sparkles,XCircle,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Brain,CheckCircle,Database,Key,Loader2,Server,Sparkles,XCircle,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Brain,CheckCircle,Database,Key,Loader2,Server,Sparkles,XCircle,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Brain,CheckCircle,Database,Key,Loader2,Server,Sparkles,XCircle,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Brain,CheckCircle,Database,Key,Loader2,Server,Sparkles,XCircle,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Brain,CheckCircle,Database,Key,Loader2,Server,Sparkles,XCircle,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Brain,CheckCircle,Database,Key,Loader2,Server,Sparkles,XCircle,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction TestAIGeneration() {\n    const { getActiveProviders, currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_3__.useContextStore)();\n    const [testResult, setTestResult] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Prevent hydration mismatch\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    const isArabic = currentLanguage === \"ar\";\n    // Only calculate after mounting to prevent hydration issues\n    const activeProviders = mounted ? getActiveProviders() : [];\n    const hasValidProvider = mounted ? activeProviders.some((p)=>p.apiKey && p.validationStatus === \"valid\" && p.selectedModels && p.selectedModels.length > 0) : false;\n    const testAIGeneration = async ()=>{\n        if (!hasValidProvider) {\n            setTestResult(isArabic ? \"لا يوجد مقدم خدمة صالح\" : \"No valid provider found\");\n            return;\n        }\n        setIsLoading(true);\n        setTestResult(\"\");\n        try {\n            const provider = activeProviders.find((p)=>p.apiKey && p.validationStatus === \"valid\");\n            if (!provider) {\n                throw new Error(\"No valid provider found\");\n            }\n            console.log(\"Testing with provider:\", provider);\n            const response = await fetch(\"/api/llm/generate\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    providerId: provider.id,\n                    apiKey: provider.apiKey,\n                    model: provider.selectedModels[0] || \"gpt-3.5-turbo\",\n                    messages: [\n                        {\n                            role: \"user\",\n                            content: isArabic ? \"اقترح 3 أسماء إبداعية لمشروع تطبيق جوال للتسوق الإلكتروني\" : \"Suggest 3 creative names for a mobile e-commerce app project\"\n                        }\n                    ],\n                    context: {\n                        test: true\n                    },\n                    fieldName: \"test\",\n                    language: currentLanguage,\n                    temperature: 0.8,\n                    maxTokens: 500\n                })\n            });\n            console.log(\"Response status:\", response.status);\n            if (!response.ok) {\n                const errorText = await response.text();\n                throw new Error(`API Error: ${response.status} - ${errorText}`);\n            }\n            const result = await response.json();\n            console.log(\"API Result:\", result);\n            if (result.success) {\n                setTestResult(result.content);\n            } else {\n                setTestResult(`Error: ${result.error}`);\n            }\n        } catch (error) {\n            console.error(\"Test error:\", error);\n            setTestResult(`Error: ${error instanceof Error ? error.message : \"Unknown error\"}`);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Prevent hydration mismatch by not rendering until mounted\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative overflow-hidden bg-gradient-to-br from-indigo-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-indigo-900 rounded-2xl shadow-xl border border-gray-200/50 dark:border-gray-700/50 p-8\",\n            dir: isArabic ? \"rtl\" : \"ltr\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-gradient-to-r from-blue-400/10 via-purple-400/10 to-indigo-400/10 dark:from-blue-600/10 dark:via-purple-600/10 dark:to-indigo-600/10\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-6 h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-gray-900 dark:text-white font-arabic\",\n                                    children: isArabic ? \"اختبار توليد الذكاء الاصطناعي\" : \"AI Generation Test\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-pulse space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 dark:bg-gray-600 rounded-lg w-3/4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 dark:bg-gray-600 rounded-lg w-1/2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 dark:bg-gray-600 rounded-lg w-2/3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n            lineNumber: 109,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"9493bb44787cba82\",\n                children: '[dir=\"rtl\"].jsx-9493bb44787cba82 .text-content.jsx-9493bb44787cba82{text-align:right;direction:rtl}[dir=\"rtl\"].jsx-9493bb44787cba82 .font-arabic.jsx-9493bb44787cba82{font-family:\"Tajawal\",\"Arial\",sans-serif;text-align:right}'\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                dir: isArabic ? \"rtl\" : \"ltr\",\n                suppressHydrationWarning: true,\n                className: \"jsx-9493bb44787cba82\" + \" \" + \"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-9493bb44787cba82\" + \" \" + `flex items-center gap-3 mb-6 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-9493bb44787cba82\" + \" \" + \"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-6 h-6 text-blue-600 dark:text-blue-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-9493bb44787cba82\" + \" \" + \"text-content\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"jsx-9493bb44787cba82\" + \" \" + \"text-lg font-semibold text-gray-900 dark:text-white font-arabic\",\n                                        children: isArabic ? \"اختبار توليد الذكاء الاصطناعي\" : \"AI Generation Test\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-9493bb44787cba82\" + \" \" + \"text-sm text-gray-600 dark:text-gray-400 font-arabic\",\n                                        children: isArabic ? \"تحقق من حالة الاتصال مع نماذج الذكاء الاصطناعي\" : \"Check connection status with AI models\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-9493bb44787cba82\" + \" \" + \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-9493bb44787cba82\" + \" \" + \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-9493bb44787cba82\" + \" \" + `flex items-center gap-3 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-9493bb44787cba82\" + \" \" + \"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-5 h-5 text-blue-600 dark:text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-9493bb44787cba82\" + \" \" + \"text-content\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"jsx-9493bb44787cba82\" + \" \" + \"text-sm text-gray-600 dark:text-gray-400 font-arabic\",\n                                                    children: isArabic ? \"مقدمو الخدمة النشطون\" : \"Active Providers\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    suppressHydrationWarning: true,\n                                                    className: \"jsx-9493bb44787cba82\" + \" \" + \"text-xl font-bold text-gray-900 dark:text-white\",\n                                                    children: activeProviders.length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-9493bb44787cba82\" + \" \" + \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-9493bb44787cba82\" + \" \" + `flex items-center gap-3 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-9493bb44787cba82\" + \" \" + `p-2 rounded-lg ${hasValidProvider ? \"bg-green-100 dark:bg-green-900\" : \"bg-red-100 dark:bg-red-900\"}`,\n                                            children: hasValidProvider ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-5 h-5 text-green-600 dark:text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-5 h-5 text-red-600 dark:text-red-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-9493bb44787cba82\" + \" \" + \"text-content\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"jsx-9493bb44787cba82\" + \" \" + \"text-sm text-gray-600 dark:text-gray-400 font-arabic\",\n                                                    children: isArabic ? \"مقدم خدمة صالح\" : \"Valid Provider\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    suppressHydrationWarning: true,\n                                                    className: \"jsx-9493bb44787cba82\" + \" \" + `text-xl font-bold ${hasValidProvider ? \"text-green-600 dark:text-green-400\" : \"text-red-600 dark:text-red-400\"}`,\n                                                    children: hasValidProvider ? \"✅\" : \"❌\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-9493bb44787cba82\" + \" \" + \"space-y-3 mb-6\",\n                        children: activeProviders.map((provider)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-9493bb44787cba82\" + \" \" + \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-9493bb44787cba82\" + \" \" + `flex items-start justify-between ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-9493bb44787cba82\" + \" \" + `flex items-center gap-3 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-9493bb44787cba82\" + \" \" + `p-2 rounded-lg ${provider.validationStatus === \"valid\" ? \"bg-green-100 dark:bg-green-900\" : \"bg-gray-100 dark:bg-gray-600\"}`,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: `w-4 h-4 ${provider.validationStatus === \"valid\" ? \"text-green-600 dark:text-green-400\" : \"text-gray-500\"}`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-9493bb44787cba82\" + \" \" + \"text-content\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"jsx-9493bb44787cba82\" + \" \" + \"font-semibold text-gray-900 dark:text-white font-arabic\",\n                                                                children: provider.id\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"jsx-9493bb44787cba82\" + \" \" + `text-xs font-arabic ${provider.validationStatus === \"valid\" ? \"text-green-600 dark:text-green-400\" : \"text-gray-500\"}`,\n                                                                children: provider.validationStatus\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                                lineNumber: 213,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-9493bb44787cba82\" + \" \" + `flex items-center gap-4 text-xs ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-9493bb44787cba82\" + \" \" + `flex items-center gap-1 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                                lineNumber: 221,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-9493bb44787cba82\" + \" \" + \"font-arabic text-content\",\n                                                                children: [\n                                                                    isArabic ? \"مفتاح API:\" : \"API Key:\",\n                                                                    \" \",\n                                                                    provider.apiKey ? \"✅\" : \"❌\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                                lineNumber: 222,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-9493bb44787cba82\" + \" \" + `flex items-center gap-1 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                                lineNumber: 227,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-9493bb44787cba82\" + \" \" + \"font-arabic text-content\",\n                                                                children: [\n                                                                    isArabic ? \"النماذج المختارة:\" : \"Selected Models:\",\n                                                                    \" \",\n                                                                    provider.selectedModels?.length || 0\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                                lineNumber: 228,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this),\n                                    provider.selectedModels && provider.selectedModels.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-9493bb44787cba82\" + \" \" + \"mt-3 pt-3 border-t border-gray-200 dark:border-gray-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-9493bb44787cba82\" + \" \" + `flex flex-wrap gap-2 ${isArabic ? \"justify-end\" : \"justify-start\"}`,\n                                            children: provider.selectedModels.map((model, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-9493bb44787cba82\" + \" \" + \"px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded text-xs font-arabic\",\n                                                    children: model\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, provider.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-9493bb44787cba82\" + \" \" + `flex ${isArabic ? \"justify-start\" : \"justify-center\"} mb-6`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: testAIGeneration,\n                            disabled: isLoading || !hasValidProvider,\n                            className: \"jsx-9493bb44787cba82\" + \" \" + `flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-arabic ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                            children: [\n                                isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"w-5 h-5 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"jsx-9493bb44787cba82\" + \" \" + \"text-content\",\n                                    children: isLoading ? isArabic ? \"جاري الاختبار...\" : \"Testing...\" : isArabic ? \"اختبار التوليد\" : \"Test Generation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 9\n                    }, this),\n                    testResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-9493bb44787cba82\" + \" \" + \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-9493bb44787cba82\" + \" \" + `flex items-center gap-3 mb-4 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-9493bb44787cba82\" + \" \" + \"p-2 bg-green-100 dark:bg-green-900 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-5 h-5 text-green-600 dark:text-green-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"jsx-9493bb44787cba82\" + \" \" + \"text-lg font-semibold text-gray-900 dark:text-white font-arabic text-content\",\n                                        children: isArabic ? \"نتيجة الاختبار\" : \"Test Result\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-9493bb44787cba82\" + \" \" + \"bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-600\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                    className: \"jsx-9493bb44787cba82\" + \" \" + \"text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap font-arabic text-content leading-relaxed\",\n                                    children: testResult\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/TestAIGeneration.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ThemeProvider.tsx":
/*!******************************************!*\
  !*** ./src/components/ThemeProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme auto */ \n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ThemeProvider({ children }) {\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"light\");\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize theme from localStorage only, ignore system preference after first load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedTheme = localStorage.getItem(\"contextkit-theme\");\n        if (savedTheme && (savedTheme === \"light\" || savedTheme === \"dark\")) {\n            // إذا كان هناك theme محفوظ، استخدمه\n            setTheme(savedTheme);\n        } else {\n            // فقط في المرة الأولى، استخدم system preference\n            const systemTheme = window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\";\n            setTheme(systemTheme);\n            localStorage.setItem(\"contextkit-theme\", systemTheme);\n        }\n        setMounted(true);\n    }, []);\n    // Apply theme to document immediately\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (mounted) {\n            const root = document.documentElement;\n            // إزالة جميع classes المتعلقة بالـ theme أولاً\n            root.classList.remove(\"light\", \"dark\");\n            // إضافة الـ class الصحيح\n            root.classList.add(theme);\n            // حفظ في localStorage مع مفتاح مخصص\n            localStorage.setItem(\"contextkit-theme\", theme);\n            // تطبيق فوري على body أيضاً\n            document.body.setAttribute(\"data-theme\", theme);\n        }\n    }, [\n        theme,\n        mounted\n    ]);\n    const toggleTheme = ()=>{\n        const newTheme = theme === \"light\" ? \"dark\" : \"light\";\n        setTheme(newTheme);\n        // حفظ فوري في localStorage\n        localStorage.setItem(\"contextkit-theme\", newTheme);\n    };\n    // Prevent hydration mismatch\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: {\n            theme,\n            toggleTheme\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            suppressHydrationWarning: true,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeProvider.tsx\",\n            lineNumber: 69,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeProvider.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        // Return default values instead of throwing error during SSR\n        return {\n            theme: \"light\",\n            toggleTheme: ()=>{}\n        };\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ThemeProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ThemeToggle.tsx":
/*!****************************************!*\
  !*** ./src/components/ThemeToggle.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ThemeToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _ThemeProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ThemeProvider */ \"(ssr)/./src/components/ThemeProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ThemeToggle() {\n    const { theme, toggleTheme } = (0,_ThemeProvider__WEBPACK_IMPORTED_MODULE_1__.useTheme)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: toggleTheme,\n        className: \"relative w-12 h-12 rounded-xl bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-900 hover:from-blue-100 hover:to-indigo-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden\",\n        \"aria-label\": `Switch to ${theme === \"light\" ? \"dark\" : \"light\"} mode`,\n        title: `Switch to ${theme === \"light\" ? \"dark\" : \"light\"} mode`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: `w-6 h-6 text-amber-500 transition-all duration-500 ease-in-out ${theme === \"light\" ? \"opacity-100 rotate-0 scale-100\" : \"opacity-0 rotate-180 scale-0\"}`,\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: `w-6 h-6 text-blue-400 absolute transition-all duration-500 ease-in-out ${theme === \"dark\" ? \"opacity-100 rotate-0 scale-100\" : \"opacity-0 -rotate-180 scale-0\"}`,\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ThemeToggle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/llmProviders.ts":
/*!*********************************!*\
  !*** ./src/lib/llmProviders.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LLM_PROVIDERS_DATABASE: () => (/* binding */ LLM_PROVIDERS_DATABASE),\n/* harmony export */   getActiveProviders: () => (/* binding */ getActiveProviders),\n/* harmony export */   getModelById: () => (/* binding */ getModelById),\n/* harmony export */   getProviderBaseUrl: () => (/* binding */ getProviderBaseUrl),\n/* harmony export */   getProviderById: () => (/* binding */ getProviderById),\n/* harmony export */   getProviderHeaders: () => (/* binding */ getProviderHeaders),\n/* harmony export */   searchProviders: () => (/* binding */ searchProviders)\n/* harmony export */ });\n/**\n * قاعدة بيانات شاملة لمقدمي خدمات LLM\n * تحتوي على معلومات كاملة عن كل مقدم خدمة مع Base URLs والنماذج\n */ const LLM_PROVIDERS_DATABASE = [\n    {\n        id: \"openai\",\n        name: \"OpenAI\",\n        icon: \"\\uD83E\\uDD16\",\n        description: \"GPT models from OpenAI - Industry leading language models\",\n        baseUrl: \"https://api.openai.com/v1\",\n        apiKeyPlaceholder: \"sk-...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 128000,\n        models: [\n            {\n                id: \"gpt-4o\",\n                name: \"GPT-4o\",\n                description: \"Most advanced multimodal model with vision capabilities\",\n                contextLength: 128000,\n                pricing: \"$5/1M input, $15/1M output\",\n                inputPrice: 5,\n                outputPrice: 15\n            },\n            {\n                id: \"gpt-4o-mini\",\n                name: \"GPT-4o Mini\",\n                description: \"Faster and more affordable version of GPT-4o\",\n                contextLength: 128000,\n                pricing: \"$0.15/1M input, $0.6/1M output\",\n                inputPrice: 0.15,\n                outputPrice: 0.6\n            },\n            {\n                id: \"gpt-4-turbo\",\n                name: \"GPT-4 Turbo\",\n                description: \"High performance model with latest knowledge\",\n                contextLength: 128000,\n                pricing: \"$10/1M input, $30/1M output\",\n                inputPrice: 10,\n                outputPrice: 30\n            },\n            {\n                id: \"gpt-4\",\n                name: \"GPT-4\",\n                description: \"Original GPT-4 model with strong reasoning\",\n                contextLength: 8192,\n                pricing: \"$30/1M input, $60/1M output\",\n                inputPrice: 30,\n                outputPrice: 60\n            },\n            {\n                id: \"gpt-3.5-turbo\",\n                name: \"GPT-3.5 Turbo\",\n                description: \"Fast and efficient for most tasks\",\n                contextLength: 16385,\n                pricing: \"$0.5/1M input, $1.5/1M output\",\n                inputPrice: 0.5,\n                outputPrice: 1.5\n            },\n            {\n                id: \"gpt-3.5-turbo-instruct\",\n                name: \"GPT-3.5 Turbo Instruct\",\n                description: \"Instruction-following variant\",\n                contextLength: 4096,\n                pricing: \"$1.5/1M input, $2/1M output\",\n                inputPrice: 1.5,\n                outputPrice: 2\n            }\n        ]\n    },\n    {\n        id: \"anthropic\",\n        name: \"Anthropic\",\n        icon: \"\\uD83E\\uDDE0\",\n        description: \"Claude models from Anthropic - Advanced reasoning capabilities\",\n        baseUrl: \"https://api.anthropic.com/v1\",\n        apiKeyPlaceholder: \"sk-ant-...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 200000,\n        headers: {\n            \"anthropic-version\": \"2023-06-01\"\n        },\n        models: [\n            {\n                id: \"claude-3-5-sonnet-20241022\",\n                name: \"Claude 3.5 Sonnet\",\n                description: \"Most intelligent model\",\n                contextLength: 200000,\n                pricing: \"$3/1M input, $15/1M output\",\n                inputPrice: 3,\n                outputPrice: 15\n            },\n            {\n                id: \"claude-3-5-haiku-20241022\",\n                name: \"Claude 3.5 Haiku\",\n                description: \"Fastest model\",\n                contextLength: 200000,\n                pricing: \"$0.25/1M input, $1.25/1M output\",\n                inputPrice: 0.25,\n                outputPrice: 1.25\n            },\n            {\n                id: \"claude-3-opus-20240229\",\n                name: \"Claude 3 Opus\",\n                description: \"Most powerful model\",\n                contextLength: 200000,\n                pricing: \"$15/1M input, $75/1M output\",\n                inputPrice: 15,\n                outputPrice: 75\n            }\n        ]\n    },\n    {\n        id: \"google\",\n        name: \"Google AI\",\n        icon: \"\\uD83D\\uDD0D\",\n        description: \"Gemini models from Google - Multimodal AI capabilities\",\n        baseUrl: \"https://generativelanguage.googleapis.com/v1beta\",\n        apiKeyPlaceholder: \"AIza...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 2000000,\n        models: [\n            {\n                id: \"gemini-1.5-pro\",\n                name: \"Gemini 1.5 Pro\",\n                description: \"Most advanced model with 2M context window\",\n                contextLength: 2000000,\n                pricing: \"$1.25/1M input, $5/1M output\",\n                inputPrice: 1.25,\n                outputPrice: 5\n            },\n            {\n                id: \"gemini-1.5-flash\",\n                name: \"Gemini 1.5 Flash\",\n                description: \"Fast and efficient with 1M context\",\n                contextLength: 1000000,\n                pricing: \"$0.075/1M input, $0.3/1M output\",\n                inputPrice: 0.075,\n                outputPrice: 0.3\n            },\n            {\n                id: \"gemini-1.5-flash-8b\",\n                name: \"Gemini 1.5 Flash 8B\",\n                description: \"Smaller, faster model for simple tasks\",\n                contextLength: 1000000,\n                pricing: \"$0.0375/1M input, $0.15/1M output\",\n                inputPrice: 0.0375,\n                outputPrice: 0.15\n            },\n            {\n                id: \"gemini-pro\",\n                name: \"Gemini Pro\",\n                description: \"Balanced performance for general use\",\n                contextLength: 32768,\n                pricing: \"$0.5/1M input, $1.5/1M output\",\n                inputPrice: 0.5,\n                outputPrice: 1.5\n            },\n            {\n                id: \"gemini-pro-vision\",\n                name: \"Gemini Pro Vision\",\n                description: \"Multimodal model with vision capabilities\",\n                contextLength: 16000,\n                pricing: \"$0.25/1M input, $0.5/1M output\",\n                inputPrice: 0.25,\n                outputPrice: 0.5\n            }\n        ]\n    },\n    {\n        id: \"openrouter\",\n        name: \"OpenRouter\",\n        icon: \"\\uD83D\\uDD00\",\n        description: \"Access to multiple models via OpenRouter - One API for all models\",\n        baseUrl: \"https://openrouter.ai/api/v1\",\n        apiKeyPlaceholder: \"sk-or-...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 200000,\n        headers: {\n            \"HTTP-Referer\": process.env.NEXT_PUBLIC_SITE_URL || \"http://localhost:3000\",\n            \"X-Title\": \"ContextKit\"\n        },\n        models: [\n            {\n                id: \"openai/gpt-4o\",\n                name: \"GPT-4o (via OpenRouter)\",\n                description: \"OpenAI GPT-4o through OpenRouter\",\n                contextLength: 128000,\n                pricing: \"Variable pricing\"\n            },\n            {\n                id: \"anthropic/claude-3.5-sonnet\",\n                name: \"Claude 3.5 Sonnet (via OpenRouter)\",\n                description: \"Anthropic Claude through OpenRouter\",\n                contextLength: 200000,\n                pricing: \"Variable pricing\"\n            },\n            {\n                id: \"google/gemini-pro-1.5\",\n                name: \"Gemini Pro 1.5 (via OpenRouter)\",\n                description: \"Google Gemini through OpenRouter\",\n                contextLength: 1000000,\n                pricing: \"Variable pricing\"\n            },\n            {\n                id: \"meta-llama/llama-3.1-405b-instruct\",\n                name: \"Llama 3.1 405B (via OpenRouter)\",\n                description: \"Meta Llama through OpenRouter\",\n                contextLength: 131072,\n                pricing: \"Variable pricing\"\n            }\n        ]\n    },\n    {\n        id: \"deepseek\",\n        name: \"DeepSeek\",\n        icon: \"\\uD83C\\uDF0A\",\n        description: \"DeepSeek models - Efficient and cost-effective AI\",\n        baseUrl: \"https://api.deepseek.com/v1\",\n        apiKeyPlaceholder: \"sk-...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 32768,\n        models: [\n            {\n                id: \"deepseek-chat\",\n                name: \"DeepSeek Chat\",\n                description: \"General purpose conversational AI\",\n                contextLength: 32768,\n                pricing: \"$0.14/1M input, $0.28/1M output\",\n                inputPrice: 0.14,\n                outputPrice: 0.28\n            },\n            {\n                id: \"deepseek-coder\",\n                name: \"DeepSeek Coder\",\n                description: \"Specialized for code generation\",\n                contextLength: 16384,\n                pricing: \"$0.14/1M input, $0.28/1M output\",\n                inputPrice: 0.14,\n                outputPrice: 0.28\n            }\n        ]\n    },\n    {\n        id: \"mistral\",\n        name: \"Mistral AI\",\n        icon: \"\\uD83C\\uDF1F\",\n        description: \"Mistral AI - Advanced European AI models with multilingual capabilities\",\n        baseUrl: \"https://api.mistral.ai/v1\",\n        apiKeyPlaceholder: \"mistral_...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 128000,\n        models: [\n            {\n                id: \"mistral-large-latest\",\n                name: \"Mistral Large\",\n                description: \"Most advanced model with superior reasoning\",\n                contextLength: 128000,\n                pricing: \"$2/1M input, $6/1M output\",\n                inputPrice: 2,\n                outputPrice: 6\n            },\n            {\n                id: \"mistral-medium-latest\",\n                name: \"Mistral Medium\",\n                description: \"Balanced performance and cost\",\n                contextLength: 32000,\n                pricing: \"$2.7/1M input, $8.1/1M output\",\n                inputPrice: 2.7,\n                outputPrice: 8.1\n            },\n            {\n                id: \"mistral-small-latest\",\n                name: \"Mistral Small\",\n                description: \"Fast and efficient for simple tasks\",\n                contextLength: 32000,\n                pricing: \"$0.2/1M input, $0.6/1M output\",\n                inputPrice: 0.2,\n                outputPrice: 0.6\n            },\n            {\n                id: \"open-mistral-7b\",\n                name: \"Open Mistral 7B\",\n                description: \"Open source model, fast and efficient\",\n                contextLength: 32000,\n                pricing: \"$0.25/1M input, $0.25/1M output\",\n                inputPrice: 0.25,\n                outputPrice: 0.25\n            },\n            {\n                id: \"open-mixtral-8x7b\",\n                name: \"Open Mixtral 8x7B\",\n                description: \"Mixture of experts model\",\n                contextLength: 32000,\n                pricing: \"$0.7/1M input, $0.7/1M output\",\n                inputPrice: 0.7,\n                outputPrice: 0.7\n            },\n            {\n                id: \"open-mixtral-8x22b\",\n                name: \"Open Mixtral 8x22B\",\n                description: \"Larger mixture of experts model\",\n                contextLength: 64000,\n                pricing: \"$2/1M input, $6/1M output\",\n                inputPrice: 2,\n                outputPrice: 6\n            },\n            {\n                id: \"mistral-embed\",\n                name: \"Mistral Embed\",\n                description: \"Embedding model for semantic search\",\n                contextLength: 8192,\n                pricing: \"$0.1/1M tokens\",\n                inputPrice: 0.1,\n                outputPrice: 0\n            }\n        ]\n    },\n    {\n        id: \"cohere\",\n        name: \"Cohere\",\n        icon: \"\\uD83E\\uDDEE\",\n        description: \"Cohere - Enterprise-grade language models with strong multilingual support\",\n        baseUrl: \"https://api.cohere.ai/v1\",\n        apiKeyPlaceholder: \"co_...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 128000,\n        models: [\n            {\n                id: \"command-r-plus\",\n                name: \"Command R+\",\n                description: \"Most advanced model for complex reasoning and RAG\",\n                contextLength: 128000,\n                pricing: \"$3/1M input, $15/1M output\",\n                inputPrice: 3,\n                outputPrice: 15\n            },\n            {\n                id: \"command-r\",\n                name: \"Command R\",\n                description: \"Balanced model for general use and RAG\",\n                contextLength: 128000,\n                pricing: \"$0.5/1M input, $1.5/1M output\",\n                inputPrice: 0.5,\n                outputPrice: 1.5\n            },\n            {\n                id: \"command\",\n                name: \"Command\",\n                description: \"Versatile model for various tasks\",\n                contextLength: 4096,\n                pricing: \"$1/1M input, $2/1M output\",\n                inputPrice: 1,\n                outputPrice: 2\n            },\n            {\n                id: \"command-light\",\n                name: \"Command Light\",\n                description: \"Fast and efficient for simple tasks\",\n                contextLength: 4096,\n                pricing: \"$0.3/1M input, $0.6/1M output\",\n                inputPrice: 0.3,\n                outputPrice: 0.6\n            },\n            {\n                id: \"command-nightly\",\n                name: \"Command Nightly\",\n                description: \"Latest experimental features\",\n                contextLength: 4096,\n                pricing: \"$1/1M input, $2/1M output\",\n                inputPrice: 1,\n                outputPrice: 2\n            }\n        ]\n    },\n    {\n        id: \"groq\",\n        name: \"Groq\",\n        icon: \"⚡\",\n        description: \"Groq - Ultra-fast inference with GroqChip technology\",\n        baseUrl: \"https://api.groq.com/openai/v1\",\n        apiKeyPlaceholder: \"gsk_...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 32768,\n        models: [\n            {\n                id: \"llama-3.1-70b-versatile\",\n                name: \"Llama 3.1 70B\",\n                description: \"Meta Llama 3.1 70B on Groq\",\n                contextLength: 131072,\n                pricing: \"$0.59/1M input, $0.79/1M output\",\n                inputPrice: 0.59,\n                outputPrice: 0.79\n            },\n            {\n                id: \"llama-3.1-8b-instant\",\n                name: \"Llama 3.1 8B\",\n                description: \"Meta Llama 3.1 8B on Groq\",\n                contextLength: 131072,\n                pricing: \"$0.05/1M input, $0.08/1M output\",\n                inputPrice: 0.05,\n                outputPrice: 0.08\n            },\n            {\n                id: \"mixtral-8x7b-32768\",\n                name: \"Mixtral 8x7B\",\n                description: \"Mistral Mixtral 8x7B on Groq\",\n                contextLength: 32768,\n                pricing: \"$0.24/1M input, $0.24/1M output\",\n                inputPrice: 0.24,\n                outputPrice: 0.24\n            }\n        ]\n    }\n];\n/**\n * الحصول على مقدم خدمة بواسطة ID\n */ function getProviderById(id) {\n    return LLM_PROVIDERS_DATABASE.find((provider)=>provider.id === id);\n}\n/**\n * الحصول على جميع مقدمي الخدمة النشطين\n */ function getActiveProviders() {\n    return LLM_PROVIDERS_DATABASE.filter((provider)=>provider.isActive);\n}\n/**\n * الحصول على نموذج بواسطة provider ID و model ID\n */ function getModelById(providerId, modelId) {\n    const provider = getProviderById(providerId);\n    return provider?.models.find((model)=>model.id === modelId);\n}\n/**\n * البحث عن مقدمي الخدمة\n */ function searchProviders(query) {\n    const lowercaseQuery = query.toLowerCase();\n    return LLM_PROVIDERS_DATABASE.filter((provider)=>provider.name.toLowerCase().includes(lowercaseQuery) || provider.description.toLowerCase().includes(lowercaseQuery) || provider.models.some((model)=>model.name.toLowerCase().includes(lowercaseQuery) || model.description.toLowerCase().includes(lowercaseQuery)));\n}\n/**\n * تحديد Base URL التلقائي لمقدم الخدمة\n */ function getProviderBaseUrl(providerId) {\n    const provider = getProviderById(providerId);\n    return provider?.baseUrl || \"\";\n}\n/**\n * الحصول على Headers المطلوبة لمقدم الخدمة\n */ function getProviderHeaders(providerId) {\n    const provider = getProviderById(providerId);\n    const baseHeaders = provider?.headers || {};\n    // إضافة headers خاصة لكل مزود\n    switch(providerId){\n        case \"anthropic\":\n            return {\n                ...baseHeaders,\n                \"anthropic-version\": \"2023-06-01\"\n            };\n        case \"cohere\":\n            return {\n                ...baseHeaders,\n                \"Cohere-Version\": \"2022-12-06\"\n            };\n        case \"mistral\":\n            return {\n                ...baseHeaders,\n                \"User-Agent\": \"ContextKit/1.0\"\n            };\n        default:\n            return baseHeaders;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/llmProviders.ts\n");

/***/ }),

/***/ "(ssr)/./src/store/contextStore.ts":
/*!***********************************!*\
  !*** ./src/store/contextStore.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useContextStore: () => (/* binding */ useContextStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n\n\n// القيم الافتراضية\nconst initialState = {\n    projectDefinition: {\n        name: \"\",\n        purpose: \"\",\n        targetUsers: \"\",\n        goals: \"\",\n        scope: \"\",\n        timeline: \"\",\n        projectType: \"\",\n        targetPlatforms: [],\n        primaryLanguages: [],\n        complexity: \"\",\n        budget: \"\",\n        teamSize: \"\",\n        deploymentType: \"\"\n    },\n    contextMap: {\n        timeContext: \"\",\n        language: \"\",\n        location: \"\",\n        culturalContext: \"\",\n        behavioralAspects: \"\",\n        environmentalFactors: \"\"\n    },\n    emotionalTone: {\n        personality: \"\",\n        communicationStyle: \"\",\n        userExperience: \"\",\n        brandVoice: \"\",\n        emotionalIntelligence: \"\",\n        interactionFlow: \"\"\n    },\n    technicalLayer: {\n        programmingLanguages: \"\",\n        frameworks: \"\",\n        llmModels: \"\",\n        databases: \"\",\n        apis: \"\",\n        infrastructure: \"\",\n        architecturePattern: \"\",\n        scalingStrategy: \"\",\n        securityRequirements: \"\",\n        performanceTargets: \"\",\n        integrationNeeds: \"\",\n        monitoringTools: \"\"\n    },\n    legalRisk: {\n        privacyConcerns: \"\",\n        dataProtection: \"\",\n        compliance: \"\",\n        risks: \"\",\n        mitigation: \"\",\n        ethicalConsiderations: \"\"\n    },\n    currentLanguage: \"ar\",\n    outputFormat: \"markdown\",\n    showAdvancedOptions: true,\n    apiSettings: {\n        providers: [],\n        globalSettings: {\n            temperature: 0.7,\n            topP: 0.9,\n            maxTokens: 1000,\n            timeout: 30000\n        },\n        // Legacy support\n        openaiApiKey: \"\",\n        openrouterApiKey: \"\",\n        customModels: []\n    }\n};\n// إنشاء المتجر مع التخزين المستمر\nconst useContextStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        ...initialState,\n        updateProjectDefinition: (data)=>set((state)=>({\n                    projectDefinition: {\n                        ...state.projectDefinition,\n                        ...data\n                    }\n                })),\n        updateContextMap: (data)=>set((state)=>({\n                    contextMap: {\n                        ...state.contextMap,\n                        ...data\n                    }\n                })),\n        updateEmotionalTone: (data)=>set((state)=>({\n                    emotionalTone: {\n                        ...state.emotionalTone,\n                        ...data\n                    }\n                })),\n        updateTechnicalLayer: (data)=>set((state)=>({\n                    technicalLayer: {\n                        ...state.technicalLayer,\n                        ...data\n                    }\n                })),\n        updateLegalRisk: (data)=>set((state)=>({\n                    legalRisk: {\n                        ...state.legalRisk,\n                        ...data\n                    }\n                })),\n        setLanguage: (lang)=>set({\n                currentLanguage: lang\n            }),\n        setOutputFormat: (format)=>set({\n                outputFormat: format\n            }),\n        setShowAdvancedOptions: (show)=>set({\n                showAdvancedOptions: show\n            }),\n        setApiSettings: (settings)=>set({\n                apiSettings: {\n                    ...settings,\n                    providers: settings.providers || []\n                }\n            }),\n        // LLM Provider management functions\n        addProvider: (provider)=>set((state)=>{\n                const existingProviders = state.apiSettings.providers || [];\n                // تحقق من عدم وجود مقدم خدمة بنفس المعرف\n                if (existingProviders.find((p)=>p.id === provider.id)) {\n                    console.warn(`Provider with id ${provider.id} already exists`);\n                    return state; // لا تضيف مقدم خدمة مكرر\n                }\n                // إضافة القيم الافتراضية للميزات المتقدمة\n                const enhancedProvider = {\n                    ...provider,\n                    priority: provider.priority || 5,\n                    isBackup: provider.isBackup || false,\n                    maxRequestsPerMinute: provider.maxRequestsPerMinute || 60,\n                    timeout: provider.timeout || 30,\n                    retryAttempts: provider.retryAttempts || 3,\n                    stats: provider.stats || {\n                        totalRequests: 0,\n                        successfulRequests: 0,\n                        failedRequests: 0,\n                        averageResponseTime: 0,\n                        totalTokensUsed: 0,\n                        totalCost: 0\n                    }\n                };\n                return {\n                    apiSettings: {\n                        ...state.apiSettings,\n                        providers: [\n                            ...existingProviders,\n                            enhancedProvider\n                        ]\n                    }\n                };\n            }),\n        updateProvider: (providerId, updates)=>set((state)=>({\n                    apiSettings: {\n                        ...state.apiSettings,\n                        providers: (state.apiSettings.providers || []).map((p)=>p.id === providerId ? {\n                                ...p,\n                                ...updates\n                            } : p)\n                    }\n                })),\n        removeProvider: (providerId)=>set((state)=>({\n                    apiSettings: {\n                        ...state.apiSettings,\n                        providers: (state.apiSettings.providers || []).filter((p)=>p.id !== providerId),\n                        defaultProvider: state.apiSettings.defaultProvider === providerId ? undefined : state.apiSettings.defaultProvider\n                    }\n                })),\n        validateProvider: async (providerId)=>{\n            const state = get();\n            const provider = (state.apiSettings.providers || []).find((p)=>p.id === providerId);\n            if (!provider) return false;\n            try {\n                // Update status to pending\n                state.updateProvider(providerId, {\n                    validationStatus: \"pending\",\n                    errorMessage: undefined\n                });\n                // Call validation API\n                const response = await fetch(\"/api/llm/validate\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        providerId: provider.id,\n                        apiKey: provider.apiKey,\n                        baseUrl: provider.baseUrl\n                    })\n                });\n                const result = await response.json();\n                if (result.valid) {\n                    state.updateProvider(providerId, {\n                        validationStatus: \"valid\",\n                        lastValidated: new Date(),\n                        errorMessage: undefined,\n                        isEnabled: true // تفعيل المقدم تلقائياً عند نجاح التحقق\n                    });\n                    return true;\n                } else {\n                    state.updateProvider(providerId, {\n                        validationStatus: \"invalid\",\n                        errorMessage: result.error || \"Validation failed\"\n                    });\n                    return false;\n                }\n            } catch (error) {\n                state.updateProvider(providerId, {\n                    validationStatus: \"error\",\n                    errorMessage: error instanceof Error ? error.message : \"Unknown error\"\n                });\n                return false;\n            }\n        },\n        getProvider: (providerId)=>{\n            const state = get();\n            return (state.apiSettings.providers || []).find((p)=>p.id === providerId);\n        },\n        getActiveProviders: ()=>{\n            const state = get();\n            return (state.apiSettings.providers || []).filter((p)=>p.isEnabled);\n        },\n        setDefaultProvider: (providerId)=>set((state)=>({\n                    apiSettings: {\n                        ...state.apiSettings,\n                        defaultProvider: providerId\n                    }\n                })),\n        // ميزات متقدمة للمزودين\n        getProvidersByPriority: ()=>{\n            const state = get();\n            return (state.apiSettings.providers || []).filter((p)=>p.isEnabled).sort((a, b)=>(b.priority || 5) - (a.priority || 5));\n        },\n        getBackupProviders: ()=>{\n            const state = get();\n            return (state.apiSettings.providers || []).filter((p)=>p.isEnabled && p.isBackup);\n        },\n        updateProviderStats: (id, stats)=>set((state)=>{\n                const providers = state.apiSettings.providers || [];\n                const providerIndex = providers.findIndex((p)=>p.id === id);\n                if (providerIndex !== -1) {\n                    const updatedProviders = [\n                        ...providers\n                    ];\n                    updatedProviders[providerIndex] = {\n                        ...updatedProviders[providerIndex],\n                        stats: {\n                            ...updatedProviders[providerIndex].stats,\n                            ...stats,\n                            lastUsed: new Date()\n                        }\n                    };\n                    return {\n                        apiSettings: {\n                            ...state.apiSettings,\n                            providers: updatedProviders\n                        }\n                    };\n                }\n                return state;\n            }),\n        getBestProvider: (criteria = \"reliability\")=>{\n            const state = get();\n            const activeProviders = (state.apiSettings.providers || []).filter((p)=>p.isEnabled && !p.isBackup);\n            if (activeProviders.length === 0) return undefined;\n            switch(criteria){\n                case \"speed\":\n                    return activeProviders.reduce((best, current)=>{\n                        const bestSpeed = best.stats?.averageResponseTime || Infinity;\n                        const currentSpeed = current.stats?.averageResponseTime || Infinity;\n                        return currentSpeed < bestSpeed ? current : best;\n                    });\n                case \"cost\":\n                    return activeProviders.reduce((best, current)=>{\n                        const bestCost = best.costPerToken || Infinity;\n                        const currentCost = current.costPerToken || Infinity;\n                        return currentCost < bestCost ? current : best;\n                    });\n                case \"reliability\":\n                default:\n                    return activeProviders.reduce((best, current)=>{\n                        const bestReliability = best.stats ? best.stats.successfulRequests / (best.stats.totalRequests || 1) : 0;\n                        const currentReliability = current.stats ? current.stats.successfulRequests / (current.stats.totalRequests || 1) : 0;\n                        return currentReliability > bestReliability ? current : best;\n                    });\n            }\n        },\n        resetAll: ()=>set(initialState),\n        // مسح جميع الإجابات فقط (الاحتفاظ بالإعدادات)\n        clearAllAnswers: ()=>set((state)=>({\n                    ...state,\n                    projectDefinition: {\n                        name: \"\",\n                        purpose: \"\",\n                        targetUsers: \"\",\n                        goals: \"\",\n                        scope: \"\",\n                        timeline: \"\"\n                    },\n                    contextMap: {\n                        timeContext: \"\",\n                        language: \"\",\n                        location: \"\",\n                        culturalContext: \"\",\n                        behavioralAspects: \"\",\n                        environmentalFactors: \"\"\n                    },\n                    emotionalTone: {\n                        personality: \"\",\n                        communicationStyle: \"\",\n                        userExperience: \"\",\n                        brandVoice: \"\",\n                        emotionalIntelligence: \"\",\n                        interactionFlow: \"\"\n                    },\n                    technicalLayer: {\n                        programmingLanguages: \"\",\n                        frameworks: \"\",\n                        llmModels: \"\",\n                        databases: \"\",\n                        apis: \"\",\n                        infrastructure: \"\"\n                    },\n                    legalRisk: {\n                        privacyConcerns: \"\",\n                        dataProtection: \"\",\n                        compliance: \"\",\n                        risks: \"\",\n                        mitigation: \"\",\n                        ethicalConsiderations: \"\"\n                    }\n                })),\n        getModuleData: (module)=>{\n            const state = get();\n            switch(module){\n                case \"project\":\n                    return state.projectDefinition;\n                case \"context\":\n                    return state.contextMap;\n                case \"emotional\":\n                    return state.emotionalTone;\n                case \"technical\":\n                    return state.technicalLayer;\n                case \"legal\":\n                    return state.legalRisk;\n                default:\n                    return {};\n            }\n        },\n        getAllData: ()=>{\n            const state = get();\n            return {\n                projectDefinition: state.projectDefinition,\n                contextMap: state.contextMap,\n                emotionalTone: state.emotionalTone,\n                technicalLayer: state.technicalLayer,\n                legalRisk: state.legalRisk\n            };\n        }\n    }), {\n    name: \"contextkit-storage\",\n    version: 1,\n    skipHydration: true\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/contextStore.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"22d6d015889c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY3JhZnRlcnkvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzdlMzUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIyMmQ2ZDAxNTg4OWNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/styles/glass-effects.css":
/*!**************************************!*\
  !*** ./src/styles/glass-effects.css ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5e0b1f04c4ea\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL2dsYXNzLWVmZmVjdHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY3JhZnRlcnkvLi9zcmMvc3R5bGVzL2dsYXNzLWVmZmVjdHMuY3NzPzVkYjMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI1ZTBiMWYwNGM0ZWFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/glass-effects.css\n");

/***/ }),

/***/ "(rsc)/./src/styles/text-improvements.css":
/*!******************************************!*\
  !*** ./src/styles/text-improvements.css ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"757aecea32c0\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL3RleHQtaW1wcm92ZW1lbnRzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsid2VicGFjazovL2NyYWZ0ZXJ5Ly4vc3JjL3N0eWxlcy90ZXh0LWltcHJvdmVtZW50cy5jc3M/MGJkMSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjc1N2FlY2VhMzJjMFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/text-improvements.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _styles_glass_effects_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../styles/glass-effects.css */ \"(rsc)/./src/styles/glass-effects.css\");\n/* harmony import */ var _styles_text_improvements_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../styles/text-improvements.css */ \"(rsc)/./src/styles/text-improvements.css\");\n/* harmony import */ var _components_ThemeProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ThemeProvider */ \"(rsc)/./src/components/ThemeProvider.tsx\");\n/* harmony import */ var _components_StoreHydration__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/StoreHydration */ \"(rsc)/./src/components/StoreHydration.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"Craftery - AI-powered Idea Builder\",\n    description: \"AI-powered platform for building and developing creative ideas\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"antialiased font-arabic\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeProvider__WEBPACK_IMPORTED_MODULE_4__.ThemeProvider, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_StoreHydration__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this),\n                        children\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/settings/page.tsx":
/*!***********************************!*\
  !*** ./src/app/settings/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\app\settings\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\app\settings\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/StoreHydration.tsx":
/*!*******************************************!*\
  !*** ./src/components/StoreHydration.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\StoreHydration.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\StoreHydration.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/ThemeProvider.tsx":
/*!******************************************!*\
  !*** ./src/components/ThemeProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0),
/* harmony export */   useTheme: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\ThemeProvider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\ThemeProvider.tsx#ThemeProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\ThemeProvider.tsx#useTheme`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react","vendor-chunks/zustand","vendor-chunks/styled-jsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsettings%2Fpage&page=%2Fsettings%2Fpage&appPaths=%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fsettings%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();