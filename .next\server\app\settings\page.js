/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/settings/page";
exports.ids = ["app/settings/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsettings%2Fpage&page=%2Fsettings%2Fpage&appPaths=%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fsettings%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsettings%2Fpage&page=%2Fsettings%2Fpage&appPaths=%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fsettings%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'settings',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/settings/page.tsx */ \"(rsc)/./src/app/settings/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/settings/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/settings/page\",\n        pathname: \"/settings\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsettings%2Fpage&page=%2Fsettings%2Fpage&appPaths=%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fsettings%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CStoreHydration.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Cstyles%5C%5Cglass-effects.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Cstyles%5C%5Ctext-improvements.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CStoreHydration.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Cstyles%5C%5Cglass-effects.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Cstyles%5C%5Ctext-improvements.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/StoreHydration.tsx */ \"(ssr)/./src/components/StoreHydration.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ThemeProvider.tsx */ \"(ssr)/./src/components/ThemeProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2ZhaXNzJTVDJTVDRGVza3RvcCU1QyU1Q0NvbnRleHRLaXQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNmYWlzcyU1QyU1Q0Rlc2t0b3AlNUMlNUNDb250ZXh0S2l0JTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q1N0b3JlSHlkcmF0aW9uLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDZmFpc3MlNUMlNUNEZXNrdG9wJTVDJTVDQ29udGV4dEtpdCU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNUaGVtZVByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlRoZW1lUHJvdmlkZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDZmFpc3MlNUMlNUNEZXNrdG9wJTVDJTVDQ29udGV4dEtpdCU1QyU1Q3NyYyU1QyU1Q3N0eWxlcyU1QyU1Q2dsYXNzLWVmZmVjdHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2ZhaXNzJTVDJTVDRGVza3RvcCU1QyU1Q0NvbnRleHRLaXQlNUMlNUNzcmMlNUMlNUNzdHlsZXMlNUMlNUN0ZXh0LWltcHJvdmVtZW50cy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtMQUE0STtBQUM1STtBQUNBLGdMQUFpSiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbnRleHRraXQvPzJlZGQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcZmFpc3NcXFxcRGVza3RvcFxcXFxDb250ZXh0S2l0XFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXFN0b3JlSHlkcmF0aW9uLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiVGhlbWVQcm92aWRlclwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXGZhaXNzXFxcXERlc2t0b3BcXFxcQ29udGV4dEtpdFxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxUaGVtZVByb3ZpZGVyLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CStoreHydration.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Cstyles%5C%5Cglass-effects.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Cstyles%5C%5Ctext-improvements.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/settings/page.tsx */ \"(ssr)/./src/app/settings/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2ZhaXNzJTVDJTVDRGVza3RvcCU1QyU1Q0NvbnRleHRLaXQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNzZXR0aW5ncyU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrS0FBd0ciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb250ZXh0a2l0Lz8zZDExIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcZmFpc3NcXFxcRGVza3RvcFxcXFxDb250ZXh0S2l0XFxcXHNyY1xcXFxhcHBcXFxcc2V0dGluZ3NcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/settings/page.tsx":
/*!***********************************!*\
  !*** ./src/app/settings/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SettingsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(ssr)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* harmony import */ var _lib_llmProviders__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/llmProviders */ \"(ssr)/./src/lib/llmProviders.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/test-tube.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ThemeToggle__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ThemeToggle */ \"(ssr)/./src/components/ThemeToggle.tsx\");\n/* harmony import */ var _components_LanguageToggle__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/LanguageToggle */ \"(ssr)/./src/components/LanguageToggle.tsx\");\n/* harmony import */ var _components_TestAIGeneration__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/TestAIGeneration */ \"(ssr)/./src/components/TestAIGeneration.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nfunction SettingsPage() {\n    const { currentLanguage, apiSettings, showAdvancedOptions, setShowAdvancedOptions, addProvider, updateProvider, removeProvider, validateProvider, getProvider } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_3__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    const [showKeys, setShowKeys] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [validationStates, setValidationStates] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [showAddProvider, setShowAddProvider] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [selectedProviderId, setSelectedProviderId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [errorMessage, setErrorMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [expandedProviders, setExpandedProviders] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [selectedModels, setSelectedModels] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [newCustomModel, setNewCustomModel] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    // تهيئة النماذج المحددة عند تحميل الصفحة\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const configuredProviders = apiSettings.providers || [];\n        const initialModels = {};\n        configuredProviders.forEach((provider)=>{\n            initialModels[provider.id] = provider.selectedModels || [];\n        });\n        setSelectedModels(initialModels);\n    }, [\n        apiSettings.providers\n    ]);\n    const translations = {\n        title: isArabic ? \"إعدادات نماذج الذكاء الاصطناعي\" : \"LLM API Settings\",\n        subtitle: isArabic ? \"قم بتكوين مفاتيح واجهة برمجة التطبيقات ونماذج الذكاء الاصطناعي\" : \"Configure your API keys and AI models\",\n        providers: isArabic ? \"مقدمو خدمات الذكاء الاصطناعي\" : \"LLM Providers\",\n        addProvider: isArabic ? \"إضافة مقدم خدمة جديد\" : \"Add Provider\",\n        apiKey: isArabic ? \"مفتاح واجهة برمجة التطبيقات\" : \"API Key\",\n        baseUrl: isArabic ? \"الرابط الأساسي للخدمة\" : \"Base URL\",\n        testConnection: isArabic ? \"اختبار الاتصال\" : \"Test Connection\",\n        validating: isArabic ? \"جاري التحقق من الاتصال...\" : \"Validating...\",\n        valid: isArabic ? \"الاتصال صحيح\" : \"Valid\",\n        invalid: isArabic ? \"الاتصال غير صحيح\" : \"Invalid\",\n        error: isArabic ? \"حدث خطأ في الاتصال\" : \"Error\",\n        models: isArabic ? \"النماذج المتوفرة\" : \"Available Models\",\n        selectedModels: isArabic ? \"النماذج المختارة\" : \"Selected Models\",\n        addCustomModel: isArabic ? \"إضافة نموذج مخصص\" : \"Add Custom Model\",\n        customModelName: isArabic ? \"اسم النموذج المخصص\" : \"Custom Model Name\",\n        editModels: isArabic ? \"تحرير النماذج\" : \"Edit Models\",\n        saveModels: isArabic ? \"حفظ التغييرات\" : \"Save Models\",\n        noModelsSelected: isArabic ? \"لم يتم اختيار أي نماذج بعد\" : \"No models selected\",\n        cancel: isArabic ? \"إلغاء العملية\" : \"Cancel\",\n        add: isArabic ? \"إضافة\" : \"Add\",\n        backToHome: isArabic ? \"العودة إلى الصفحة الرئيسية\" : \"Back to Home\",\n        active: isArabic ? \"مفعل\" : \"Active\",\n        selectProvider: isArabic ? \"اختر مقدم الخدمة\" : \"Select Provider\",\n        noProvidersConfigured: isArabic ? \"لم يتم تكوين أي مقدم خدمة حتى الآن\" : \"No providers configured yet\",\n        providerAlreadyExists: isArabic ? \"مقدم الخدمة موجود مسبقاً\" : \"Provider already exists\",\n        pleaseSelectProvider: isArabic ? \"يرجى اختيار مقدم خدمة من القائمة\" : \"Please select a provider\",\n        providerNotFound: isArabic ? \"لم يتم العثور على مقدم الخدمة\" : \"Provider not found\",\n        errorAddingProvider: isArabic ? \"حدث خطأ أثناء إضافة مقدم الخدمة\" : \"Error adding provider\",\n        // إعدادات عامة\n        generalSettings: isArabic ? \"الإعدادات العامة\" : \"General Settings\",\n        advancedOptions: isArabic ? \"الخيارات المتقدمة\" : \"Advanced Options\",\n        showAdvancedOptions: isArabic ? \"إظهار الخيارات المتقدمة\" : \"Show Advanced Options\",\n        advancedOptionsDescription: isArabic ? \"عرض خيارات التخصيص المتقدمة في صفحات المشروع\" : \"Display advanced customization options in project pages\"\n    };\n    // دوال إدارة النماذج\n    const toggleModelSelection = (providerId, modelId)=>{\n        setSelectedModels((prev)=>{\n            const currentModels = prev[providerId] || [];\n            const isSelected = currentModels.includes(modelId);\n            return {\n                ...prev,\n                [providerId]: isSelected ? currentModels.filter((id)=>id !== modelId) : [\n                    ...currentModels,\n                    modelId\n                ]\n            };\n        });\n    };\n    const addCustomModel = (providerId)=>{\n        if (newCustomModel.trim()) {\n            setSelectedModels((prev)=>{\n                const currentModels = prev[providerId] || [];\n                return {\n                    ...prev,\n                    [providerId]: [\n                        ...currentModels,\n                        newCustomModel.trim()\n                    ]\n                };\n            });\n            setNewCustomModel(\"\");\n        }\n    };\n    const removeCustomModel = (providerId, modelId)=>{\n        setSelectedModels((prev)=>{\n            const currentModels = prev[providerId] || [];\n            return {\n                ...prev,\n                [providerId]: currentModels.filter((id)=>id !== modelId)\n            };\n        });\n    };\n    const saveProviderModels = (providerId)=>{\n        const models = selectedModels[providerId] || [];\n        updateProvider(providerId, {\n            selectedModels: models\n        });\n        setExpandedProviders((prev)=>({\n                ...prev,\n                [providerId]: false\n            }));\n    };\n    const handleAddProvider = async ()=>{\n        setErrorMessage(\"\");\n        if (!selectedProviderId) {\n            setErrorMessage(translations.pleaseSelectProvider);\n            return;\n        }\n        const providerTemplate = (0,_lib_llmProviders__WEBPACK_IMPORTED_MODULE_4__.getProviderById)(selectedProviderId);\n        if (!providerTemplate) {\n            setErrorMessage(translations.providerNotFound);\n            return;\n        }\n        const existingProvider = getProvider(selectedProviderId);\n        if (existingProvider) {\n            setErrorMessage(translations.providerAlreadyExists);\n            setShowAddProvider(false);\n            setSelectedProviderId(\"\");\n            return;\n        }\n        try {\n            const newProvider = {\n                id: selectedProviderId,\n                apiKey: \"\",\n                selectedModels: [],\n                isEnabled: false,\n                validationStatus: \"pending\",\n                priority: 1,\n                isBackup: false\n            };\n            addProvider(newProvider);\n            setShowAddProvider(false);\n            setSelectedProviderId(\"\");\n            setErrorMessage(\"\");\n        } catch (error) {\n            console.error(\"Error adding provider:\", error);\n            setErrorMessage(translations.errorAddingProvider);\n        }\n    };\n    const handleValidateProvider = async (providerId)=>{\n        setValidationStates((prev)=>({\n                ...prev,\n                [providerId]: {\n                    status: \"validating\"\n                }\n            }));\n        try {\n            const isValid = await validateProvider(providerId);\n            setValidationStates((prev)=>({\n                    ...prev,\n                    [providerId]: {\n                        status: isValid ? \"valid\" : \"invalid\",\n                        message: isValid ? translations.valid : translations.invalid,\n                        lastValidated: new Date()\n                    }\n                }));\n        } catch (error) {\n            setValidationStates((prev)=>({\n                    ...prev,\n                    [providerId]: {\n                        status: \"error\",\n                        message: error instanceof Error ? error.message : translations.error\n                    }\n                }));\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"validating\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"w-4 h-4 animate-spin text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 221,\n                    columnNumber: 16\n                }, this);\n            case \"valid\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"w-4 h-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 16\n                }, this);\n            case \"invalid\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"w-4 h-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 16\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"w-4 h-4 text-orange-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"w-4 h-4 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const configuredProviders = apiSettings.providers || [];\n    const availableProviders = _lib_llmProviders__WEBPACK_IMPORTED_MODULE_4__.LLM_PROVIDERS_DATABASE.filter((p)=>!configuredProviders.some((cp)=>cp.id === p.id));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        dir: isArabic ? \"rtl\" : \"ltr\",\n        className: \"jsx-2abf06658c86e3df\" + \" \" + \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"2abf06658c86e3df\",\n                children: '[dir=\"rtl\"].jsx-2abf06658c86e3df{font-family:\"Tajawal\",\"Arial\",sans-serif}[dir=\"rtl\"].jsx-2abf06658c86e3df *.jsx-2abf06658c86e3df{font-family:\"Tajawal\",\"Arial\",sans-serif;text-align:right}[dir=\"rtl\"].jsx-2abf06658c86e3df .page-container.jsx-2abf06658c86e3df{direction:rtl}[dir=\"rtl\"].jsx-2abf06658c86e3df .header-section.jsx-2abf06658c86e3df{direction:rtl}[dir=\"rtl\"].jsx-2abf06658c86e3df .header-content.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between}[dir=\"rtl\"].jsx-2abf06658c86e3df .header-left.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse}[dir=\"rtl\"].jsx-2abf06658c86e3df .header-right.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse}[dir=\"rtl\"].jsx-2abf06658c86e3df .section-header.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between}[dir=\"rtl\"].jsx-2abf06658c86e3df .provider-card.jsx-2abf06658c86e3df{direction:rtl}[dir=\"rtl\"].jsx-2abf06658c86e3df .provider-header.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between}[dir=\"rtl\"].jsx-2abf06658c86e3df .provider-info.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse;text-align:right}[dir=\"rtl\"].jsx-2abf06658c86e3df .provider-controls.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse}[dir=\"rtl\"].jsx-2abf06658c86e3df .form-section.jsx-2abf06658c86e3df{direction:rtl}[dir=\"rtl\"].jsx-2abf06658c86e3df .form-row.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse}[dir=\"rtl\"].jsx-2abf06658c86e3df .models-section.jsx-2abf06658c86e3df{direction:rtl}[dir=\"rtl\"].jsx-2abf06658c86e3df .models-header.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between}[dir=\"rtl\"].jsx-2abf06658c86e3df .model-item.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse}[dir=\"rtl\"].jsx-2abf06658c86e3df .model-tags.jsx-2abf06658c86e3df{-webkit-box-pack:end;-webkit-justify-content:flex-end;-moz-box-pack:end;-ms-flex-pack:end;justify-content:flex-end}[dir=\"rtl\"].jsx-2abf06658c86e3df input[type=\"text\"].jsx-2abf06658c86e3df,[dir=\"rtl\"].jsx-2abf06658c86e3df input[type=\"password\"].jsx-2abf06658c86e3df,[dir=\"rtl\"].jsx-2abf06658c86e3df select.jsx-2abf06658c86e3df,[dir=\"rtl\"].jsx-2abf06658c86e3df textarea.jsx-2abf06658c86e3df{text-align:right;direction:rtl;font-family:\"Tajawal\",\"Arial\",sans-serif}[dir=\"rtl\"].jsx-2abf06658c86e3df .button-group.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse}[dir=\"rtl\"].jsx-2abf06658c86e3df .button-with-icon.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse}[dir=\"rtl\"].jsx-2abf06658c86e3df .modal-content.jsx-2abf06658c86e3df{direction:rtl;text-align:right}[dir=\"rtl\"].jsx-2abf06658c86e3df .modal-buttons.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse}[dir=\"rtl\"].jsx-2abf06658c86e3df .text-content.jsx-2abf06658c86e3df{text-align:right;direction:rtl}[dir=\"rtl\"].jsx-2abf06658c86e3df .font-arabic.jsx-2abf06658c86e3df{font-family:\"Tajawal\",\"Arial\",sans-serif;text-align:right}'\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-2abf06658c86e3df\" + \" \" + \"header-section bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-2abf06658c86e3df\" + \" \" + `header-content flex items-center justify-between h-16 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2abf06658c86e3df\" + \" \" + `header-left flex items-center gap-4 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        href: \"/\",\n                                        className: `flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                        children: [\n                                            isArabic ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-5 h-5 rotate-180\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"font-arabic text-content\",\n                                                children: translations.backToHome\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2abf06658c86e3df\" + \" \" + `flex items-center gap-3 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"w-6 h-6 text-blue-600 dark:text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-content\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-xl font-bold text-gray-900 dark:text-white font-arabic\",\n                                                        children: translations.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm text-gray-600 dark:text-gray-400 font-arabic\",\n                                                        children: translations.subtitle\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2abf06658c86e3df\" + \" \" + `header-right flex items-center gap-3 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LanguageToggle__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeToggle__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 368,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 367,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 366,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-2abf06658c86e3df\" + \" \" + \"page-container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"p-6 border-b border-gray-200 dark:border-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-lg font-semibold text-gray-900 dark:text-white font-arabic text-content\",\n                                        children: translations.generalSettings\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 413,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"space-y-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-2abf06658c86e3df\" + \" \" + `flex items-center justify-between w-full`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + `${isArabic ? \"text-right\" : \"text-left\"}`,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm font-medium text-gray-900 dark:text-white font-arabic\",\n                                                            children: translations.showAdvancedOptions\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 423,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm text-gray-500 dark:text-gray-400 mt-1 font-arabic\",\n                                                            children: translations.advancedOptionsDescription\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 426,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowAdvancedOptions(!showAdvancedOptions),\n                                                        style: {\n                                                            justifyContent: \"left\"\n                                                        },\n                                                        className: \"jsx-2abf06658c86e3df\" + \" \" + `relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${showAdvancedOptions ? \"bg-blue-600\" : \"bg-gray-200 dark:bg-gray-700\"}`,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + `inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${showAdvancedOptions ? \"translate-x-6\" : \"translate-x-1\"}`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 438,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 431,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 418,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 412,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"p-6 border-b border-gray-200 dark:border-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2abf06658c86e3df\" + \" \" + `section-header flex items-center justify-between ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-lg font-semibold text-gray-900 dark:text-white font-arabic text-content\",\n                                                children: translations.providers\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    setShowAddProvider(true);\n                                                    setErrorMessage(\"\");\n                                                    setSelectedProviderId(\"\");\n                                                },\n                                                className: \"jsx-2abf06658c86e3df\" + \" \" + `button-with-icon flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-arabic ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 465,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-content\",\n                                                        children: translations.addProvider\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 452,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"p-6 space-y-4\",\n                                    children: configuredProviders.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-gray-500 dark:text-gray-400 font-arabic text-content\",\n                                                children: translations.noProvidersConfigured\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 17\n                                    }, this) : configuredProviders.map((provider)=>{\n                                        const providerInfo = (0,_lib_llmProviders__WEBPACK_IMPORTED_MODULE_4__.getProviderById)(provider.id);\n                                        const validationState = validationStates[provider.id];\n                                        if (!providerInfo) return null;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"provider-card border border-gray-200 dark:border-gray-600 rounded-lg p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + `provider-header flex items-start justify-between mb-6 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + `provider-info flex items-center gap-4 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-2xl\",\n                                                                    children: providerInfo.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 490,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"space-y-1 text-content\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"font-semibold text-gray-900 dark:text-white font-arabic\",\n                                                                            children: providerInfo.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 492,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm text-gray-600 dark:text-gray-400 font-arabic\",\n                                                                            children: providerInfo.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 495,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 491,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + `provider-controls flex items-center gap-3 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                                            children: [\n                                                                getStatusIcon(validationState?.status || \"idle\"),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + `flex items-center gap-2 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: provider.isEnabled,\n                                                                            onChange: (e)=>updateProvider(provider.id, {\n                                                                                    isEnabled: e.target.checked\n                                                                                }),\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 506,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm text-gray-600 dark:text-gray-400 font-arabic whitespace-nowrap text-content\",\n                                                                            children: translations.active\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 512,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 505,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>removeProvider(provider.id),\n                                                                    title: isArabic ? \"حذف مقدم الخدمة\" : \"Remove Provider\",\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"p-2 text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-colors\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 522,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 517,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 501,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 488,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"form-section space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"grid grid-cols-1 lg:grid-cols-2 gap-4 items-end\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex flex-col\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 font-arabic text-content\",\n                                                                            children: translations.apiKey\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 530,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"relative\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: showKeys[provider.id] ? \"text\" : \"password\",\n                                                                                    value: provider.apiKey,\n                                                                                    onChange: (e)=>updateProvider(provider.id, {\n                                                                                            apiKey: e.target.value\n                                                                                        }),\n                                                                                    placeholder: providerInfo.apiKeyPlaceholder,\n                                                                                    dir: isArabic ? \"rtl\" : \"ltr\",\n                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + `w-full h-10 px-3 py-2 ${isArabic ? \"pr-10 pl-3\" : \"pr-10 pl-3\"} border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent font-arabic`\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 534,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>setShowKeys((prev)=>({\n                                                                                                ...prev,\n                                                                                                [provider.id]: !prev[provider.id]\n                                                                                            })),\n                                                                                    title: isArabic ? showKeys[provider.id] ? \"إخفاء المفتاح\" : \"إظهار المفتاح\" : showKeys[provider.id] ? \"Hide key\" : \"Show key\",\n                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + `absolute ${isArabic ? \"left-3\" : \"right-3\"} top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600`,\n                                                                                    children: showKeys[provider.id] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 547,\n                                                                                        columnNumber: 58\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 547,\n                                                                                        columnNumber: 91\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 542,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 533,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 529,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex flex-col\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 font-arabic text-content\",\n                                                                            children: translations.baseUrl\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 553,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: provider.baseUrl || providerInfo.baseUrl,\n                                                                            onChange: (e)=>updateProvider(provider.id, {\n                                                                                    baseUrl: e.target.value\n                                                                                }),\n                                                                            placeholder: providerInfo.baseUrl,\n                                                                            dir: isArabic ? \"rtl\" : \"ltr\",\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"w-full h-10 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent font-arabic\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 556,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 552,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 528,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"grid grid-cols-1 lg:grid-cols-2 gap-4 mt-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + `flex ${isArabic ? \"justify-start\" : \"justify-start\"}`,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleValidateProvider(provider.id),\n                                                                        disabled: !provider.apiKey || validationState?.status === \"validating\",\n                                                                        className: \"jsx-2abf06658c86e3df\" + \" \" + `button-with-icon flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-arabic ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                className: \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 575,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-content\",\n                                                                                children: validationState?.status === \"validating\" ? translations.validating : translations.testConnection\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 576,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 570,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 569,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + `flex ${isArabic ? \"justify-end\" : \"justify-end\"}`,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm text-gray-600 dark:text-gray-400 font-arabic text-content\",\n                                                                        children: [\n                                                                            translations.models,\n                                                                            \": \",\n                                                                            providerInfo.models.length\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 584,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 583,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 567,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        validationState?.message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + `mt-2 p-2 rounded text-sm text-content ${validationState.status === \"valid\" ? \"bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300\" : \"bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300\"}`,\n                                                            children: validationState.message\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 591,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"models-section mt-4 border-t border-gray-200 dark:border-gray-600 pt-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"grid grid-cols-1 lg:grid-cols-2 gap-4 mb-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + `flex ${isArabic ? \"justify-start\" : \"justify-start\"}`,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm font-medium text-gray-900 dark:text-white font-arabic text-content\",\n                                                                                children: translations.selectedModels\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 605,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 604,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + `flex ${isArabic ? \"justify-end\" : \"justify-end\"}`,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>{\n                                                                                    setExpandedProviders((prev)=>({\n                                                                                            ...prev,\n                                                                                            [provider.id]: !prev[provider.id]\n                                                                                        }));\n                                                                                    if (!selectedModels[provider.id]) {\n                                                                                        setSelectedModels((prev)=>({\n                                                                                                ...prev,\n                                                                                                [provider.id]: provider.selectedModels || []\n                                                                                            }));\n                                                                                    }\n                                                                                },\n                                                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm text-blue-600 dark:text-blue-400 hover:underline font-arabic text-content\",\n                                                                                children: expandedProviders[provider.id] ? translations.saveModels : translations.editModels\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 612,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 611,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 602,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"mb-3\",\n                                                                    children: (provider.selectedModels || []).length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm text-gray-500 dark:text-gray-400 font-arabic text-content\",\n                                                                        children: translations.noModelsSelected\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 629,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2abf06658c86e3df\" + \" \" + `model-tags flex flex-wrap gap-2 ${isArabic ? \"justify-end\" : \"justify-start\"}`,\n                                                                        children: (provider.selectedModels || []).map((modelId)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded text-xs font-arabic text-content\",\n                                                                                children: modelId\n                                                                            }, modelId, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 635,\n                                                                                columnNumber: 35\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 633,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 627,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                expandedProviders[provider.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"space-y-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-2abf06658c86e3df\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm font-medium text-gray-900 dark:text-white mb-2 font-arabic text-content\",\n                                                                                    children: translations.models\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 651,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"grid grid-cols-1 md:grid-cols-2 gap-2 max-h-40 overflow-y-auto\",\n                                                                                    children: providerInfo.models.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + `model-item flex items-center gap-2 p-2 hover:bg-gray-100 dark:hover:bg-gray-600 rounded cursor-pointer ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                    type: \"checkbox\",\n                                                                                                    checked: (selectedModels[provider.id] || []).includes(model.id),\n                                                                                                    onChange: ()=>toggleModelSelection(provider.id, model.id),\n                                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"rounded border-gray-300 text-blue-600 focus:ring-blue-500 flex-shrink-0\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                    lineNumber: 660,\n                                                                                                    columnNumber: 39\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex-1 min-w-0 text-content\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm font-medium text-gray-900 dark:text-white truncate font-arabic\",\n                                                                                                            children: model.name\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                            lineNumber: 667,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-xs text-gray-500 dark:text-gray-400 truncate font-arabic\",\n                                                                                                            children: model.description\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                            lineNumber: 670,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                    lineNumber: 666,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, model.id, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 656,\n                                                                                            columnNumber: 37\n                                                                                        }, this))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 654,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 650,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-2abf06658c86e3df\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm font-medium text-gray-900 dark:text-white mb-2 font-arabic text-content\",\n                                                                                    children: translations.addCustomModel\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 681,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + `flex gap-2 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"text\",\n                                                                                            value: newCustomModel,\n                                                                                            onChange: (e)=>setNewCustomModel(e.target.value),\n                                                                                            placeholder: translations.customModelName,\n                                                                                            onKeyDown: (e)=>e.key === \"Enter\" && addCustomModel(provider.id),\n                                                                                            dir: isArabic ? \"rtl\" : \"ltr\",\n                                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm font-arabic\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 685,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                            onClick: ()=>addCustomModel(provider.id),\n                                                                                            disabled: !newCustomModel.trim(),\n                                                                                            title: isArabic ? \"إضافة النموذج\" : \"Add Model\",\n                                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm flex-shrink-0\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                                className: \"w-4 h-4\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                lineNumber: 700,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 694,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 684,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 680,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        (selectedModels[provider.id] || []).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-2abf06658c86e3df\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm font-medium text-gray-900 dark:text-white mb-2 font-arabic text-content\",\n                                                                                    children: translations.selectedModels\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 708,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + `model-tags flex flex-wrap gap-2 ${isArabic ? \"justify-end\" : \"justify-start\"}`,\n                                                                                    children: (selectedModels[provider.id] || []).map((modelId)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + `flex items-center gap-1 px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded text-xs ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"font-arabic text-content\",\n                                                                                                    children: modelId\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                    lineNumber: 717,\n                                                                                                    columnNumber: 41\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>removeCustomModel(provider.id, modelId),\n                                                                                                    title: isArabic ? \"حذف النموذج\" : \"Remove Model\",\n                                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-blue-600 dark:text-blue-400 hover:text-red-600 dark:hover:text-red-400 flex-shrink-0\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                                        className: \"w-3 h-3\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                        lineNumber: 723,\n                                                                                                        columnNumber: 43\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                    lineNumber: 718,\n                                                                                                    columnNumber: 41\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, modelId, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 713,\n                                                                                            columnNumber: 39\n                                                                                        }, this))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 711,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 707,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + `button-group flex ${isArabic ? \"justify-start\" : \"justify-end\"}`,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>saveProviderModels(provider.id),\n                                                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm font-arabic text-content\",\n                                                                                children: translations.saveModels\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 733,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 732,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 648,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 601,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, provider.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 487,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 471,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 451,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TestAIGeneration__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 752,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 409,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 408,\n                columnNumber: 7\n            }, this),\n            showAddProvider && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-2abf06658c86e3df\" + \" \" + \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    dir: isArabic ? \"rtl\" : \"ltr\",\n                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"modal-content bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-lg font-semibold text-gray-900 dark:text-white mb-4 font-arabic text-content\",\n                            children: translations.addProvider\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 760,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"space-y-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: selectedProviderId,\n                                onChange: (e)=>setSelectedProviderId(e.target.value),\n                                dir: isArabic ? \"rtl\" : \"ltr\",\n                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white font-arabic\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        className: \"jsx-2abf06658c86e3df\",\n                                        children: translations.selectProvider\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 771,\n                                        columnNumber: 17\n                                    }, this),\n                                    availableProviders.map((provider)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: provider.id,\n                                            className: \"jsx-2abf06658c86e3df\",\n                                            children: [\n                                                provider.icon,\n                                                \" \",\n                                                provider.name\n                                            ]\n                                        }, provider.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 773,\n                                            columnNumber: 19\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 765,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 764,\n                            columnNumber: 13\n                        }, this),\n                        errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2abf06658c86e3df\" + \" \" + `flex items-center gap-2 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-4 h-4 text-red-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 784,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm text-red-700 dark:text-red-300 font-arabic text-content\",\n                                        children: errorMessage\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 785,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 783,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 782,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-2abf06658c86e3df\" + \" \" + `modal-buttons flex gap-3 mt-6 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        setShowAddProvider(false);\n                                        setErrorMessage(\"\");\n                                        setSelectedProviderId(\"\");\n                                    },\n                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors font-arabic text-content\",\n                                    children: translations.cancel\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 793,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleAddProvider,\n                                    disabled: !selectedProviderId,\n                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-arabic text-content\",\n                                    children: translations.add\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 803,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 792,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 759,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 758,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n        lineNumber: 239,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/settings/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/LanguageToggle.tsx":
/*!*******************************************!*\
  !*** ./src/components/LanguageToggle.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LanguageToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction LanguageToggle() {\n    const { currentLanguage, setLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_1__.useContextStore)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: ()=>setLanguage(currentLanguage === \"ar\" ? \"en\" : \"ar\"),\n        className: \"relative w-12 h-12 rounded-xl bg-gradient-to-br from-green-50 to-emerald-100 dark:from-gray-800 dark:to-gray-900 hover:from-green-100 hover:to-emerald-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden\",\n        \"aria-label\": currentLanguage === \"ar\" ? \"Switch to English\" : \"التبديل إلى العربية\",\n        title: currentLanguage === \"ar\" ? \"Switch to English\" : \"التبديل إلى العربية\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `text-sm font-bold text-blue-600 dark:text-blue-400 transition-all duration-500 ease-in-out ${currentLanguage === \"en\" ? \"opacity-100 rotate-0 scale-100\" : \"opacity-0 rotate-180 scale-0\"}`,\n                        children: \"EN\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `text-sm font-bold text-green-600 dark:text-green-400 absolute transition-all duration-500 ease-in-out font-arabic ${currentLanguage === \"ar\" ? \"opacity-100 rotate-0 scale-100\" : \"opacity-0 -rotate-180 scale-0\"}`,\n                        children: \"عر\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/LanguageToggle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/StoreHydration.tsx":
/*!*******************************************!*\
  !*** ./src/components/StoreHydration.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StoreHydration)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction StoreHydration() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // Manually hydrate the store to prevent hydration mismatches\n        const store = _store_contextStore__WEBPACK_IMPORTED_MODULE_1__.useContextStore.getState();\n        if (false) {}\n    }, []);\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9TdG9yZUh5ZHJhdGlvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs2REFFa0M7QUFDcUI7QUFFeEMsU0FBU0U7SUFDdEJGLGdEQUFTQSxDQUFDO1FBQ1IsNkRBQTZEO1FBQzdELE1BQU1HLFFBQVFGLGdFQUFlQSxDQUFDRyxRQUFRO1FBQ3RDLElBQUksS0FBa0IsRUFBYSxFQUVsQztJQUNILEdBQUcsRUFBRTtJQUVMLE9BQU87QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbnRleHRraXQvLi9zcmMvY29tcG9uZW50cy9TdG9yZUh5ZHJhdGlvbi50c3g/YTI5NyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZUNvbnRleHRTdG9yZSB9IGZyb20gJ0Avc3RvcmUvY29udGV4dFN0b3JlJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gU3RvcmVIeWRyYXRpb24oKSB7XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8gTWFudWFsbHkgaHlkcmF0ZSB0aGUgc3RvcmUgdG8gcHJldmVudCBoeWRyYXRpb24gbWlzbWF0Y2hlc1xuICAgIGNvbnN0IHN0b3JlID0gdXNlQ29udGV4dFN0b3JlLmdldFN0YXRlKCk7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICB1c2VDb250ZXh0U3RvcmUucGVyc2lzdC5yZWh5ZHJhdGUoKTtcbiAgICB9XG4gIH0sIFtdKTtcblxuICByZXR1cm4gbnVsbDtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VDb250ZXh0U3RvcmUiLCJTdG9yZUh5ZHJhdGlvbiIsInN0b3JlIiwiZ2V0U3RhdGUiLCJwZXJzaXN0IiwicmVoeWRyYXRlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/StoreHydration.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/TestAIGeneration.tsx":
/*!*********************************************!*\
  !*** ./src/components/TestAIGeneration.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestAIGeneration)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(ssr)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* harmony import */ var _barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Brain,CheckCircle,Database,Key,Loader2,Server,Sparkles,XCircle,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Brain,CheckCircle,Database,Key,Loader2,Server,Sparkles,XCircle,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Brain,CheckCircle,Database,Key,Loader2,Server,Sparkles,XCircle,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Brain,CheckCircle,Database,Key,Loader2,Server,Sparkles,XCircle,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Brain,CheckCircle,Database,Key,Loader2,Server,Sparkles,XCircle,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Brain,CheckCircle,Database,Key,Loader2,Server,Sparkles,XCircle,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Brain,CheckCircle,Database,Key,Loader2,Server,Sparkles,XCircle,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Brain,CheckCircle,Database,Key,Loader2,Server,Sparkles,XCircle,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Brain,CheckCircle,Database,Key,Loader2,Server,Sparkles,XCircle,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Brain,CheckCircle,Database,Key,Loader2,Server,Sparkles,XCircle,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction TestAIGeneration() {\n    const { getActiveProviders, currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_3__.useContextStore)();\n    const [testResult, setTestResult] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Prevent hydration mismatch\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    const isArabic = currentLanguage === \"ar\";\n    // Only calculate after mounting to prevent hydration issues\n    const activeProviders = mounted ? getActiveProviders() : [];\n    const hasValidProvider = mounted ? activeProviders.some((p)=>p.apiKey && p.validationStatus === \"valid\" && p.selectedModels && p.selectedModels.length > 0) : false;\n    const testAIGeneration = async ()=>{\n        if (!hasValidProvider) {\n            setTestResult(isArabic ? \"لا يوجد مقدم خدمة صالح\" : \"No valid provider found\");\n            return;\n        }\n        setIsLoading(true);\n        setTestResult(\"\");\n        try {\n            const provider = activeProviders.find((p)=>p.apiKey && p.validationStatus === \"valid\");\n            if (!provider) {\n                throw new Error(\"No valid provider found\");\n            }\n            console.log(\"Testing with provider:\", provider);\n            const response = await fetch(\"/api/llm/generate\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    providerId: provider.id,\n                    apiKey: provider.apiKey,\n                    model: provider.selectedModels[0] || \"gpt-3.5-turbo\",\n                    messages: [\n                        {\n                            role: \"user\",\n                            content: isArabic ? \"اقترح 3 أسماء إبداعية لمشروع تطبيق جوال للتسوق الإلكتروني\" : \"Suggest 3 creative names for a mobile e-commerce app project\"\n                        }\n                    ],\n                    context: {\n                        test: true\n                    },\n                    fieldName: \"test\",\n                    language: currentLanguage,\n                    temperature: 0.8,\n                    maxTokens: 500\n                })\n            });\n            console.log(\"Response status:\", response.status);\n            if (!response.ok) {\n                const errorText = await response.text();\n                throw new Error(`API Error: ${response.status} - ${errorText}`);\n            }\n            const result = await response.json();\n            console.log(\"API Result:\", result);\n            if (result.success) {\n                setTestResult(result.content);\n            } else {\n                setTestResult(`Error: ${result.error}`);\n            }\n        } catch (error) {\n            console.error(\"Test error:\", error);\n            setTestResult(`Error: ${error instanceof Error ? error.message : \"Unknown error\"}`);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Prevent hydration mismatch by not rendering until mounted\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative overflow-hidden bg-gradient-to-br from-indigo-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-indigo-900 rounded-2xl shadow-xl border border-gray-200/50 dark:border-gray-700/50 p-8\",\n            dir: isArabic ? \"rtl\" : \"ltr\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-gradient-to-r from-blue-400/10 via-purple-400/10 to-indigo-400/10 dark:from-blue-600/10 dark:via-purple-600/10 dark:to-indigo-600/10\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-6 h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-gray-900 dark:text-white font-arabic\",\n                                    children: isArabic ? \"اختبار توليد الذكاء الاصطناعي\" : \"AI Generation Test\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-pulse space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 dark:bg-gray-600 rounded-lg w-3/4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 dark:bg-gray-600 rounded-lg w-1/2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 dark:bg-gray-600 rounded-lg w-2/3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n            lineNumber: 109,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"9493bb44787cba82\",\n                children: '[dir=\"rtl\"].jsx-9493bb44787cba82 .text-content.jsx-9493bb44787cba82{text-align:right;direction:rtl}[dir=\"rtl\"].jsx-9493bb44787cba82 .font-arabic.jsx-9493bb44787cba82{font-family:\"Tajawal\",\"Arial\",sans-serif;text-align:right}'\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                dir: isArabic ? \"rtl\" : \"ltr\",\n                suppressHydrationWarning: true,\n                className: \"jsx-9493bb44787cba82\" + \" \" + \"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-9493bb44787cba82\" + \" \" + `flex items-center gap-3 mb-6 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-9493bb44787cba82\" + \" \" + \"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-6 h-6 text-blue-600 dark:text-blue-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-9493bb44787cba82\" + \" \" + \"text-content\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"jsx-9493bb44787cba82\" + \" \" + \"text-lg font-semibold text-gray-900 dark:text-white font-arabic\",\n                                        children: isArabic ? \"اختبار توليد الذكاء الاصطناعي\" : \"AI Generation Test\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-9493bb44787cba82\" + \" \" + \"text-sm text-gray-600 dark:text-gray-400 font-arabic\",\n                                        children: isArabic ? \"تحقق من حالة الاتصال مع نماذج الذكاء الاصطناعي\" : \"Check connection status with AI models\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-9493bb44787cba82\" + \" \" + \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-9493bb44787cba82\" + \" \" + \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-9493bb44787cba82\" + \" \" + `flex items-center gap-3 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-9493bb44787cba82\" + \" \" + \"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-5 h-5 text-blue-600 dark:text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-9493bb44787cba82\" + \" \" + \"text-content\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"jsx-9493bb44787cba82\" + \" \" + \"text-sm text-gray-600 dark:text-gray-400 font-arabic\",\n                                                    children: isArabic ? \"مقدمو الخدمة النشطون\" : \"Active Providers\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    suppressHydrationWarning: true,\n                                                    className: \"jsx-9493bb44787cba82\" + \" \" + \"text-xl font-bold text-gray-900 dark:text-white\",\n                                                    children: activeProviders.length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-9493bb44787cba82\" + \" \" + \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-9493bb44787cba82\" + \" \" + `flex items-center gap-3 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-9493bb44787cba82\" + \" \" + `p-2 rounded-lg ${hasValidProvider ? \"bg-green-100 dark:bg-green-900\" : \"bg-red-100 dark:bg-red-900\"}`,\n                                            children: hasValidProvider ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-5 h-5 text-green-600 dark:text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-5 h-5 text-red-600 dark:text-red-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-9493bb44787cba82\" + \" \" + \"text-content\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"jsx-9493bb44787cba82\" + \" \" + \"text-sm text-gray-600 dark:text-gray-400 font-arabic\",\n                                                    children: isArabic ? \"مقدم خدمة صالح\" : \"Valid Provider\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    suppressHydrationWarning: true,\n                                                    className: \"jsx-9493bb44787cba82\" + \" \" + `text-xl font-bold ${hasValidProvider ? \"text-green-600 dark:text-green-400\" : \"text-red-600 dark:text-red-400\"}`,\n                                                    children: hasValidProvider ? \"✅\" : \"❌\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-9493bb44787cba82\" + \" \" + \"space-y-3 mb-6\",\n                        children: activeProviders.map((provider)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-9493bb44787cba82\" + \" \" + \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-9493bb44787cba82\" + \" \" + `flex items-start justify-between ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-9493bb44787cba82\" + \" \" + `flex items-center gap-3 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-9493bb44787cba82\" + \" \" + `p-2 rounded-lg ${provider.validationStatus === \"valid\" ? \"bg-green-100 dark:bg-green-900\" : \"bg-gray-100 dark:bg-gray-600\"}`,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: `w-4 h-4 ${provider.validationStatus === \"valid\" ? \"text-green-600 dark:text-green-400\" : \"text-gray-500\"}`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-9493bb44787cba82\" + \" \" + \"text-content\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"jsx-9493bb44787cba82\" + \" \" + \"font-semibold text-gray-900 dark:text-white font-arabic\",\n                                                                children: provider.id\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"jsx-9493bb44787cba82\" + \" \" + `text-xs font-arabic ${provider.validationStatus === \"valid\" ? \"text-green-600 dark:text-green-400\" : \"text-gray-500\"}`,\n                                                                children: provider.validationStatus\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                                lineNumber: 213,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-9493bb44787cba82\" + \" \" + `flex items-center gap-4 text-xs ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-9493bb44787cba82\" + \" \" + `flex items-center gap-1 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                                lineNumber: 221,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-9493bb44787cba82\" + \" \" + \"font-arabic text-content\",\n                                                                children: [\n                                                                    isArabic ? \"مفتاح API:\" : \"API Key:\",\n                                                                    \" \",\n                                                                    provider.apiKey ? \"✅\" : \"❌\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                                lineNumber: 222,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-9493bb44787cba82\" + \" \" + `flex items-center gap-1 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                                lineNumber: 227,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-9493bb44787cba82\" + \" \" + \"font-arabic text-content\",\n                                                                children: [\n                                                                    isArabic ? \"النماذج المختارة:\" : \"Selected Models:\",\n                                                                    \" \",\n                                                                    provider.selectedModels?.length || 0\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                                lineNumber: 228,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this),\n                                    provider.selectedModels && provider.selectedModels.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-9493bb44787cba82\" + \" \" + \"mt-3 pt-3 border-t border-gray-200 dark:border-gray-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-9493bb44787cba82\" + \" \" + `flex flex-wrap gap-2 ${isArabic ? \"justify-end\" : \"justify-start\"}`,\n                                            children: provider.selectedModels.map((model, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-9493bb44787cba82\" + \" \" + \"px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded text-xs font-arabic\",\n                                                    children: model\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, provider.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-9493bb44787cba82\" + \" \" + `flex ${isArabic ? \"justify-start\" : \"justify-center\"} mb-6`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: testAIGeneration,\n                            disabled: isLoading || !hasValidProvider,\n                            className: \"jsx-9493bb44787cba82\" + \" \" + `flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-arabic ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                            children: [\n                                isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"w-5 h-5 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"jsx-9493bb44787cba82\" + \" \" + \"text-content\",\n                                    children: isLoading ? isArabic ? \"جاري الاختبار...\" : \"Testing...\" : isArabic ? \"اختبار التوليد\" : \"Test Generation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 9\n                    }, this),\n                    testResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-9493bb44787cba82\" + \" \" + \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-9493bb44787cba82\" + \" \" + `flex items-center gap-3 mb-4 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-9493bb44787cba82\" + \" \" + \"p-2 bg-green-100 dark:bg-green-900 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Brain_CheckCircle_Database_Key_Loader2_Server_Sparkles_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-5 h-5 text-green-600 dark:text-green-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"jsx-9493bb44787cba82\" + \" \" + \"text-lg font-semibold text-gray-900 dark:text-white font-arabic text-content\",\n                                        children: isArabic ? \"نتيجة الاختبار\" : \"Test Result\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-9493bb44787cba82\" + \" \" + \"bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-600\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                    className: \"jsx-9493bb44787cba82\" + \" \" + \"text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap font-arabic text-content leading-relaxed\",\n                                    children: testResult\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/TestAIGeneration.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ThemeProvider.tsx":
/*!******************************************!*\
  !*** ./src/components/ThemeProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme auto */ \n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ThemeProvider({ children }) {\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"light\");\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize theme from localStorage only, ignore system preference after first load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedTheme = localStorage.getItem(\"contextkit-theme\");\n        if (savedTheme && (savedTheme === \"light\" || savedTheme === \"dark\")) {\n            // إذا كان هناك theme محفوظ، استخدمه\n            setTheme(savedTheme);\n        } else {\n            // فقط في المرة الأولى، استخدم system preference\n            const systemTheme = window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\";\n            setTheme(systemTheme);\n            localStorage.setItem(\"contextkit-theme\", systemTheme);\n        }\n        setMounted(true);\n    }, []);\n    // Apply theme to document immediately\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (mounted) {\n            const root = document.documentElement;\n            // إزالة جميع classes المتعلقة بالـ theme أولاً\n            root.classList.remove(\"light\", \"dark\");\n            // إضافة الـ class الصحيح\n            root.classList.add(theme);\n            // حفظ في localStorage مع مفتاح مخصص\n            localStorage.setItem(\"contextkit-theme\", theme);\n            // تطبيق فوري على body أيضاً\n            document.body.setAttribute(\"data-theme\", theme);\n        }\n    }, [\n        theme,\n        mounted\n    ]);\n    const toggleTheme = ()=>{\n        const newTheme = theme === \"light\" ? \"dark\" : \"light\";\n        setTheme(newTheme);\n        // حفظ فوري في localStorage\n        localStorage.setItem(\"contextkit-theme\", newTheme);\n    };\n    // Prevent hydration mismatch\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: {\n            theme,\n            toggleTheme\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            suppressHydrationWarning: true,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeProvider.tsx\",\n            lineNumber: 69,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeProvider.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        // Return default values instead of throwing error during SSR\n        return {\n            theme: \"light\",\n            toggleTheme: ()=>{}\n        };\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ThemeProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ThemeToggle.tsx":
/*!****************************************!*\
  !*** ./src/components/ThemeToggle.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ThemeToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _ThemeProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ThemeProvider */ \"(ssr)/./src/components/ThemeProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ThemeToggle() {\n    const { theme, toggleTheme } = (0,_ThemeProvider__WEBPACK_IMPORTED_MODULE_1__.useTheme)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: toggleTheme,\n        className: \"relative w-12 h-12 rounded-xl bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-900 hover:from-blue-100 hover:to-indigo-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden\",\n        \"aria-label\": `Switch to ${theme === \"light\" ? \"dark\" : \"light\"} mode`,\n        title: `Switch to ${theme === \"light\" ? \"dark\" : \"light\"} mode`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: `w-6 h-6 text-amber-500 transition-all duration-500 ease-in-out ${theme === \"light\" ? \"opacity-100 rotate-0 scale-100\" : \"opacity-0 rotate-180 scale-0\"}`,\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: `w-6 h-6 text-blue-400 absolute transition-all duration-500 ease-in-out ${theme === \"dark\" ? \"opacity-100 rotate-0 scale-100\" : \"opacity-0 -rotate-180 scale-0\"}`,\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ThemeToggle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/llmProviders.ts":
/*!*********************************!*\
  !*** ./src/lib/llmProviders.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LLM_PROVIDERS_DATABASE: () => (/* binding */ LLM_PROVIDERS_DATABASE),\n/* harmony export */   getActiveProviders: () => (/* binding */ getActiveProviders),\n/* harmony export */   getModelById: () => (/* binding */ getModelById),\n/* harmony export */   getProviderBaseUrl: () => (/* binding */ getProviderBaseUrl),\n/* harmony export */   getProviderById: () => (/* binding */ getProviderById),\n/* harmony export */   getProviderHeaders: () => (/* binding */ getProviderHeaders),\n/* harmony export */   searchProviders: () => (/* binding */ searchProviders)\n/* harmony export */ });\n/**\n * قاعدة بيانات شاملة لمقدمي خدمات LLM\n * تحتوي على معلومات كاملة عن كل مقدم خدمة مع Base URLs والنماذج\n */ const LLM_PROVIDERS_DATABASE = [\n    {\n        id: \"openai\",\n        name: \"OpenAI\",\n        icon: \"\\uD83E\\uDD16\",\n        description: \"GPT models from OpenAI - Industry leading language models\",\n        baseUrl: \"https://api.openai.com/v1\",\n        apiKeyPlaceholder: \"sk-...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 128000,\n        models: [\n            {\n                id: \"gpt-4o\",\n                name: \"GPT-4o\",\n                description: \"Most advanced multimodal model with vision capabilities\",\n                contextLength: 128000,\n                pricing: \"$5/1M input, $15/1M output\",\n                inputPrice: 5,\n                outputPrice: 15\n            },\n            {\n                id: \"gpt-4o-mini\",\n                name: \"GPT-4o Mini\",\n                description: \"Faster and more affordable version of GPT-4o\",\n                contextLength: 128000,\n                pricing: \"$0.15/1M input, $0.6/1M output\",\n                inputPrice: 0.15,\n                outputPrice: 0.6\n            },\n            {\n                id: \"gpt-4-turbo\",\n                name: \"GPT-4 Turbo\",\n                description: \"High performance model with latest knowledge\",\n                contextLength: 128000,\n                pricing: \"$10/1M input, $30/1M output\",\n                inputPrice: 10,\n                outputPrice: 30\n            },\n            {\n                id: \"gpt-4\",\n                name: \"GPT-4\",\n                description: \"Original GPT-4 model with strong reasoning\",\n                contextLength: 8192,\n                pricing: \"$30/1M input, $60/1M output\",\n                inputPrice: 30,\n                outputPrice: 60\n            },\n            {\n                id: \"gpt-3.5-turbo\",\n                name: \"GPT-3.5 Turbo\",\n                description: \"Fast and efficient for most tasks\",\n                contextLength: 16385,\n                pricing: \"$0.5/1M input, $1.5/1M output\",\n                inputPrice: 0.5,\n                outputPrice: 1.5\n            },\n            {\n                id: \"gpt-3.5-turbo-instruct\",\n                name: \"GPT-3.5 Turbo Instruct\",\n                description: \"Instruction-following variant\",\n                contextLength: 4096,\n                pricing: \"$1.5/1M input, $2/1M output\",\n                inputPrice: 1.5,\n                outputPrice: 2\n            }\n        ]\n    },\n    {\n        id: \"anthropic\",\n        name: \"Anthropic\",\n        icon: \"\\uD83E\\uDDE0\",\n        description: \"Claude models from Anthropic - Advanced reasoning capabilities\",\n        baseUrl: \"https://api.anthropic.com/v1\",\n        apiKeyPlaceholder: \"sk-ant-...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 200000,\n        headers: {\n            \"anthropic-version\": \"2023-06-01\"\n        },\n        models: [\n            {\n                id: \"claude-3-5-sonnet-20241022\",\n                name: \"Claude 3.5 Sonnet\",\n                description: \"Most intelligent model\",\n                contextLength: 200000,\n                pricing: \"$3/1M input, $15/1M output\",\n                inputPrice: 3,\n                outputPrice: 15\n            },\n            {\n                id: \"claude-3-5-haiku-20241022\",\n                name: \"Claude 3.5 Haiku\",\n                description: \"Fastest model\",\n                contextLength: 200000,\n                pricing: \"$0.25/1M input, $1.25/1M output\",\n                inputPrice: 0.25,\n                outputPrice: 1.25\n            },\n            {\n                id: \"claude-3-opus-20240229\",\n                name: \"Claude 3 Opus\",\n                description: \"Most powerful model\",\n                contextLength: 200000,\n                pricing: \"$15/1M input, $75/1M output\",\n                inputPrice: 15,\n                outputPrice: 75\n            }\n        ]\n    },\n    {\n        id: \"google\",\n        name: \"Google AI\",\n        icon: \"\\uD83D\\uDD0D\",\n        description: \"Gemini models from Google - Multimodal AI capabilities\",\n        baseUrl: \"https://generativelanguage.googleapis.com/v1beta\",\n        apiKeyPlaceholder: \"AIza...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 2000000,\n        models: [\n            {\n                id: \"gemini-1.5-pro\",\n                name: \"Gemini 1.5 Pro\",\n                description: \"Most advanced model with 2M context window\",\n                contextLength: 2000000,\n                pricing: \"$1.25/1M input, $5/1M output\",\n                inputPrice: 1.25,\n                outputPrice: 5\n            },\n            {\n                id: \"gemini-1.5-flash\",\n                name: \"Gemini 1.5 Flash\",\n                description: \"Fast and efficient with 1M context\",\n                contextLength: 1000000,\n                pricing: \"$0.075/1M input, $0.3/1M output\",\n                inputPrice: 0.075,\n                outputPrice: 0.3\n            },\n            {\n                id: \"gemini-1.5-flash-8b\",\n                name: \"Gemini 1.5 Flash 8B\",\n                description: \"Smaller, faster model for simple tasks\",\n                contextLength: 1000000,\n                pricing: \"$0.0375/1M input, $0.15/1M output\",\n                inputPrice: 0.0375,\n                outputPrice: 0.15\n            },\n            {\n                id: \"gemini-pro\",\n                name: \"Gemini Pro\",\n                description: \"Balanced performance for general use\",\n                contextLength: 32768,\n                pricing: \"$0.5/1M input, $1.5/1M output\",\n                inputPrice: 0.5,\n                outputPrice: 1.5\n            },\n            {\n                id: \"gemini-pro-vision\",\n                name: \"Gemini Pro Vision\",\n                description: \"Multimodal model with vision capabilities\",\n                contextLength: 16000,\n                pricing: \"$0.25/1M input, $0.5/1M output\",\n                inputPrice: 0.25,\n                outputPrice: 0.5\n            }\n        ]\n    },\n    {\n        id: \"openrouter\",\n        name: \"OpenRouter\",\n        icon: \"\\uD83D\\uDD00\",\n        description: \"Access to multiple models via OpenRouter - One API for all models\",\n        baseUrl: \"https://openrouter.ai/api/v1\",\n        apiKeyPlaceholder: \"sk-or-...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 200000,\n        headers: {\n            \"HTTP-Referer\": process.env.NEXT_PUBLIC_SITE_URL || \"http://localhost:3000\",\n            \"X-Title\": \"ContextKit\"\n        },\n        models: [\n            {\n                id: \"openai/gpt-4o\",\n                name: \"GPT-4o (via OpenRouter)\",\n                description: \"OpenAI GPT-4o through OpenRouter\",\n                contextLength: 128000,\n                pricing: \"Variable pricing\"\n            },\n            {\n                id: \"anthropic/claude-3.5-sonnet\",\n                name: \"Claude 3.5 Sonnet (via OpenRouter)\",\n                description: \"Anthropic Claude through OpenRouter\",\n                contextLength: 200000,\n                pricing: \"Variable pricing\"\n            },\n            {\n                id: \"google/gemini-pro-1.5\",\n                name: \"Gemini Pro 1.5 (via OpenRouter)\",\n                description: \"Google Gemini through OpenRouter\",\n                contextLength: 1000000,\n                pricing: \"Variable pricing\"\n            },\n            {\n                id: \"meta-llama/llama-3.1-405b-instruct\",\n                name: \"Llama 3.1 405B (via OpenRouter)\",\n                description: \"Meta Llama through OpenRouter\",\n                contextLength: 131072,\n                pricing: \"Variable pricing\"\n            }\n        ]\n    },\n    {\n        id: \"deepseek\",\n        name: \"DeepSeek\",\n        icon: \"\\uD83C\\uDF0A\",\n        description: \"DeepSeek models - Efficient and cost-effective AI\",\n        baseUrl: \"https://api.deepseek.com/v1\",\n        apiKeyPlaceholder: \"sk-...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 32768,\n        models: [\n            {\n                id: \"deepseek-chat\",\n                name: \"DeepSeek Chat\",\n                description: \"General purpose conversational AI\",\n                contextLength: 32768,\n                pricing: \"$0.14/1M input, $0.28/1M output\",\n                inputPrice: 0.14,\n                outputPrice: 0.28\n            },\n            {\n                id: \"deepseek-coder\",\n                name: \"DeepSeek Coder\",\n                description: \"Specialized for code generation\",\n                contextLength: 16384,\n                pricing: \"$0.14/1M input, $0.28/1M output\",\n                inputPrice: 0.14,\n                outputPrice: 0.28\n            }\n        ]\n    },\n    {\n        id: \"mistral\",\n        name: \"Mistral AI\",\n        icon: \"\\uD83C\\uDF1F\",\n        description: \"Mistral AI - Advanced European AI models with multilingual capabilities\",\n        baseUrl: \"https://api.mistral.ai/v1\",\n        apiKeyPlaceholder: \"mistral_...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 128000,\n        models: [\n            {\n                id: \"mistral-large-latest\",\n                name: \"Mistral Large\",\n                description: \"Most advanced model with superior reasoning\",\n                contextLength: 128000,\n                pricing: \"$2/1M input, $6/1M output\",\n                inputPrice: 2,\n                outputPrice: 6\n            },\n            {\n                id: \"mistral-medium-latest\",\n                name: \"Mistral Medium\",\n                description: \"Balanced performance and cost\",\n                contextLength: 32000,\n                pricing: \"$2.7/1M input, $8.1/1M output\",\n                inputPrice: 2.7,\n                outputPrice: 8.1\n            },\n            {\n                id: \"mistral-small-latest\",\n                name: \"Mistral Small\",\n                description: \"Fast and efficient for simple tasks\",\n                contextLength: 32000,\n                pricing: \"$0.2/1M input, $0.6/1M output\",\n                inputPrice: 0.2,\n                outputPrice: 0.6\n            },\n            {\n                id: \"open-mistral-7b\",\n                name: \"Open Mistral 7B\",\n                description: \"Open source model, fast and efficient\",\n                contextLength: 32000,\n                pricing: \"$0.25/1M input, $0.25/1M output\",\n                inputPrice: 0.25,\n                outputPrice: 0.25\n            },\n            {\n                id: \"open-mixtral-8x7b\",\n                name: \"Open Mixtral 8x7B\",\n                description: \"Mixture of experts model\",\n                contextLength: 32000,\n                pricing: \"$0.7/1M input, $0.7/1M output\",\n                inputPrice: 0.7,\n                outputPrice: 0.7\n            },\n            {\n                id: \"open-mixtral-8x22b\",\n                name: \"Open Mixtral 8x22B\",\n                description: \"Larger mixture of experts model\",\n                contextLength: 64000,\n                pricing: \"$2/1M input, $6/1M output\",\n                inputPrice: 2,\n                outputPrice: 6\n            },\n            {\n                id: \"mistral-embed\",\n                name: \"Mistral Embed\",\n                description: \"Embedding model for semantic search\",\n                contextLength: 8192,\n                pricing: \"$0.1/1M tokens\",\n                inputPrice: 0.1,\n                outputPrice: 0\n            }\n        ]\n    },\n    {\n        id: \"cohere\",\n        name: \"Cohere\",\n        icon: \"\\uD83E\\uDDEE\",\n        description: \"Cohere - Enterprise-grade language models with strong multilingual support\",\n        baseUrl: \"https://api.cohere.ai/v1\",\n        apiKeyPlaceholder: \"co_...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 128000,\n        models: [\n            {\n                id: \"command-r-plus\",\n                name: \"Command R+\",\n                description: \"Most advanced model for complex reasoning and RAG\",\n                contextLength: 128000,\n                pricing: \"$3/1M input, $15/1M output\",\n                inputPrice: 3,\n                outputPrice: 15\n            },\n            {\n                id: \"command-r\",\n                name: \"Command R\",\n                description: \"Balanced model for general use and RAG\",\n                contextLength: 128000,\n                pricing: \"$0.5/1M input, $1.5/1M output\",\n                inputPrice: 0.5,\n                outputPrice: 1.5\n            },\n            {\n                id: \"command\",\n                name: \"Command\",\n                description: \"Versatile model for various tasks\",\n                contextLength: 4096,\n                pricing: \"$1/1M input, $2/1M output\",\n                inputPrice: 1,\n                outputPrice: 2\n            },\n            {\n                id: \"command-light\",\n                name: \"Command Light\",\n                description: \"Fast and efficient for simple tasks\",\n                contextLength: 4096,\n                pricing: \"$0.3/1M input, $0.6/1M output\",\n                inputPrice: 0.3,\n                outputPrice: 0.6\n            },\n            {\n                id: \"command-nightly\",\n                name: \"Command Nightly\",\n                description: \"Latest experimental features\",\n                contextLength: 4096,\n                pricing: \"$1/1M input, $2/1M output\",\n                inputPrice: 1,\n                outputPrice: 2\n            }\n        ]\n    },\n    {\n        id: \"groq\",\n        name: \"Groq\",\n        icon: \"⚡\",\n        description: \"Groq - Ultra-fast inference with GroqChip technology\",\n        baseUrl: \"https://api.groq.com/openai/v1\",\n        apiKeyPlaceholder: \"gsk_...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 32768,\n        models: [\n            {\n                id: \"llama-3.1-70b-versatile\",\n                name: \"Llama 3.1 70B\",\n                description: \"Meta Llama 3.1 70B on Groq\",\n                contextLength: 131072,\n                pricing: \"$0.59/1M input, $0.79/1M output\",\n                inputPrice: 0.59,\n                outputPrice: 0.79\n            },\n            {\n                id: \"llama-3.1-8b-instant\",\n                name: \"Llama 3.1 8B\",\n                description: \"Meta Llama 3.1 8B on Groq\",\n                contextLength: 131072,\n                pricing: \"$0.05/1M input, $0.08/1M output\",\n                inputPrice: 0.05,\n                outputPrice: 0.08\n            },\n            {\n                id: \"mixtral-8x7b-32768\",\n                name: \"Mixtral 8x7B\",\n                description: \"Mistral Mixtral 8x7B on Groq\",\n                contextLength: 32768,\n                pricing: \"$0.24/1M input, $0.24/1M output\",\n                inputPrice: 0.24,\n                outputPrice: 0.24\n            }\n        ]\n    }\n];\n/**\n * الحصول على مقدم خدمة بواسطة ID\n */ function getProviderById(id) {\n    return LLM_PROVIDERS_DATABASE.find((provider)=>provider.id === id);\n}\n/**\n * الحصول على جميع مقدمي الخدمة النشطين\n */ function getActiveProviders() {\n    return LLM_PROVIDERS_DATABASE.filter((provider)=>provider.isActive);\n}\n/**\n * الحصول على نموذج بواسطة provider ID و model ID\n */ function getModelById(providerId, modelId) {\n    const provider = getProviderById(providerId);\n    return provider?.models.find((model)=>model.id === modelId);\n}\n/**\n * البحث عن مقدمي الخدمة\n */ function searchProviders(query) {\n    const lowercaseQuery = query.toLowerCase();\n    return LLM_PROVIDERS_DATABASE.filter((provider)=>provider.name.toLowerCase().includes(lowercaseQuery) || provider.description.toLowerCase().includes(lowercaseQuery) || provider.models.some((model)=>model.name.toLowerCase().includes(lowercaseQuery) || model.description.toLowerCase().includes(lowercaseQuery)));\n}\n/**\n * تحديد Base URL التلقائي لمقدم الخدمة\n */ function getProviderBaseUrl(providerId) {\n    const provider = getProviderById(providerId);\n    return provider?.baseUrl || \"\";\n}\n/**\n * الحصول على Headers المطلوبة لمقدم الخدمة\n */ function getProviderHeaders(providerId) {\n    const provider = getProviderById(providerId);\n    const baseHeaders = provider?.headers || {};\n    // إضافة headers خاصة لكل مزود\n    switch(providerId){\n        case \"anthropic\":\n            return {\n                ...baseHeaders,\n                \"anthropic-version\": \"2023-06-01\"\n            };\n        case \"cohere\":\n            return {\n                ...baseHeaders,\n                \"Cohere-Version\": \"2022-12-06\"\n            };\n        case \"mistral\":\n            return {\n                ...baseHeaders,\n                \"User-Agent\": \"ContextKit/1.0\"\n            };\n        default:\n            return baseHeaders;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/llmProviders.ts\n");

/***/ }),

/***/ "(ssr)/./src/store/contextStore.ts":
/*!***********************************!*\
  !*** ./src/store/contextStore.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useContextStore: () => (/* binding */ useContextStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n\n\n// القيم الافتراضية\nconst initialState = {\n    projectDefinition: {\n        name: \"\",\n        purpose: \"\",\n        targetUsers: \"\",\n        goals: \"\",\n        scope: \"\",\n        timeline: \"\",\n        projectType: \"\",\n        projectNature: \"\",\n        geographicRegion: \"\",\n        targetPlatforms: [],\n        primaryLanguages: [],\n        complexity: \"\",\n        budget: \"\",\n        teamSize: \"\",\n        deploymentType: \"\"\n    },\n    contextMap: {\n        timeContext: \"\",\n        language: \"\",\n        location: \"\",\n        culturalContext: \"\",\n        behavioralAspects: \"\",\n        environmentalFactors: \"\"\n    },\n    emotionalTone: {\n        personality: \"\",\n        communicationStyle: \"\",\n        userExperience: \"\",\n        brandVoice: \"\",\n        emotionalIntelligence: \"\",\n        interactionFlow: \"\"\n    },\n    technicalLayer: {\n        programmingLanguages: \"\",\n        frameworks: \"\",\n        llmModels: \"\",\n        databases: \"\",\n        apis: \"\",\n        infrastructure: \"\",\n        architecturePattern: \"\",\n        scalingStrategy: \"\",\n        securityRequirements: \"\",\n        performanceTargets: \"\",\n        integrationNeeds: \"\",\n        monitoringTools: \"\"\n    },\n    legalRisk: {\n        privacyConcerns: \"\",\n        dataProtection: \"\",\n        compliance: \"\",\n        risks: \"\",\n        mitigation: \"\",\n        ethicalConsiderations: \"\"\n    },\n    currentLanguage: \"ar\",\n    outputFormat: \"markdown\",\n    showAdvancedOptions: true,\n    apiSettings: {\n        providers: [],\n        globalSettings: {\n            temperature: 0.7,\n            topP: 0.9,\n            maxTokens: 1000,\n            timeout: 30000\n        },\n        // Legacy support\n        openaiApiKey: \"\",\n        openrouterApiKey: \"\",\n        customModels: []\n    }\n};\n// إنشاء المتجر مع التخزين المستمر\nconst useContextStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        ...initialState,\n        updateProjectDefinition: (data)=>set((state)=>({\n                    projectDefinition: {\n                        ...state.projectDefinition,\n                        ...data\n                    }\n                })),\n        updateContextMap: (data)=>set((state)=>({\n                    contextMap: {\n                        ...state.contextMap,\n                        ...data\n                    }\n                })),\n        updateEmotionalTone: (data)=>set((state)=>({\n                    emotionalTone: {\n                        ...state.emotionalTone,\n                        ...data\n                    }\n                })),\n        updateTechnicalLayer: (data)=>set((state)=>({\n                    technicalLayer: {\n                        ...state.technicalLayer,\n                        ...data\n                    }\n                })),\n        updateLegalRisk: (data)=>set((state)=>({\n                    legalRisk: {\n                        ...state.legalRisk,\n                        ...data\n                    }\n                })),\n        setLanguage: (lang)=>set({\n                currentLanguage: lang\n            }),\n        setOutputFormat: (format)=>set({\n                outputFormat: format\n            }),\n        setShowAdvancedOptions: (show)=>set({\n                showAdvancedOptions: show\n            }),\n        setApiSettings: (settings)=>set({\n                apiSettings: {\n                    ...settings,\n                    providers: settings.providers || []\n                }\n            }),\n        // LLM Provider management functions\n        addProvider: (provider)=>set((state)=>{\n                const existingProviders = state.apiSettings.providers || [];\n                // تحقق من عدم وجود مقدم خدمة بنفس المعرف\n                if (existingProviders.find((p)=>p.id === provider.id)) {\n                    console.warn(`Provider with id ${provider.id} already exists`);\n                    return state; // لا تضيف مقدم خدمة مكرر\n                }\n                // إضافة القيم الافتراضية للميزات المتقدمة\n                const enhancedProvider = {\n                    ...provider,\n                    priority: provider.priority || 5,\n                    isBackup: provider.isBackup || false,\n                    maxRequestsPerMinute: provider.maxRequestsPerMinute || 60,\n                    timeout: provider.timeout || 30,\n                    retryAttempts: provider.retryAttempts || 3,\n                    stats: provider.stats || {\n                        totalRequests: 0,\n                        successfulRequests: 0,\n                        failedRequests: 0,\n                        averageResponseTime: 0,\n                        totalTokensUsed: 0,\n                        totalCost: 0\n                    }\n                };\n                return {\n                    apiSettings: {\n                        ...state.apiSettings,\n                        providers: [\n                            ...existingProviders,\n                            enhancedProvider\n                        ]\n                    }\n                };\n            }),\n        updateProvider: (providerId, updates)=>set((state)=>({\n                    apiSettings: {\n                        ...state.apiSettings,\n                        providers: (state.apiSettings.providers || []).map((p)=>p.id === providerId ? {\n                                ...p,\n                                ...updates\n                            } : p)\n                    }\n                })),\n        removeProvider: (providerId)=>set((state)=>({\n                    apiSettings: {\n                        ...state.apiSettings,\n                        providers: (state.apiSettings.providers || []).filter((p)=>p.id !== providerId),\n                        defaultProvider: state.apiSettings.defaultProvider === providerId ? undefined : state.apiSettings.defaultProvider\n                    }\n                })),\n        validateProvider: async (providerId)=>{\n            const state = get();\n            const provider = (state.apiSettings.providers || []).find((p)=>p.id === providerId);\n            if (!provider) return false;\n            try {\n                // Update status to pending\n                state.updateProvider(providerId, {\n                    validationStatus: \"pending\",\n                    errorMessage: undefined\n                });\n                // Call validation API\n                const response = await fetch(\"/api/llm/validate\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        providerId: provider.id,\n                        apiKey: provider.apiKey,\n                        baseUrl: provider.baseUrl\n                    })\n                });\n                const result = await response.json();\n                if (result.valid) {\n                    state.updateProvider(providerId, {\n                        validationStatus: \"valid\",\n                        lastValidated: new Date(),\n                        errorMessage: undefined,\n                        isEnabled: true // تفعيل المقدم تلقائياً عند نجاح التحقق\n                    });\n                    return true;\n                } else {\n                    state.updateProvider(providerId, {\n                        validationStatus: \"invalid\",\n                        errorMessage: result.error || \"Validation failed\"\n                    });\n                    return false;\n                }\n            } catch (error) {\n                state.updateProvider(providerId, {\n                    validationStatus: \"error\",\n                    errorMessage: error instanceof Error ? error.message : \"Unknown error\"\n                });\n                return false;\n            }\n        },\n        getProvider: (providerId)=>{\n            const state = get();\n            return (state.apiSettings.providers || []).find((p)=>p.id === providerId);\n        },\n        getActiveProviders: ()=>{\n            const state = get();\n            return (state.apiSettings.providers || []).filter((p)=>p.isEnabled);\n        },\n        setDefaultProvider: (providerId)=>set((state)=>({\n                    apiSettings: {\n                        ...state.apiSettings,\n                        defaultProvider: providerId\n                    }\n                })),\n        // ميزات متقدمة للمزودين\n        getProvidersByPriority: ()=>{\n            const state = get();\n            return (state.apiSettings.providers || []).filter((p)=>p.isEnabled).sort((a, b)=>(b.priority || 5) - (a.priority || 5));\n        },\n        getBackupProviders: ()=>{\n            const state = get();\n            return (state.apiSettings.providers || []).filter((p)=>p.isEnabled && p.isBackup);\n        },\n        updateProviderStats: (id, stats)=>set((state)=>{\n                const providers = state.apiSettings.providers || [];\n                const providerIndex = providers.findIndex((p)=>p.id === id);\n                if (providerIndex !== -1) {\n                    const updatedProviders = [\n                        ...providers\n                    ];\n                    updatedProviders[providerIndex] = {\n                        ...updatedProviders[providerIndex],\n                        stats: {\n                            ...updatedProviders[providerIndex].stats,\n                            ...stats,\n                            lastUsed: new Date()\n                        }\n                    };\n                    return {\n                        apiSettings: {\n                            ...state.apiSettings,\n                            providers: updatedProviders\n                        }\n                    };\n                }\n                return state;\n            }),\n        getBestProvider: (criteria = \"reliability\")=>{\n            const state = get();\n            const activeProviders = (state.apiSettings.providers || []).filter((p)=>p.isEnabled && !p.isBackup);\n            if (activeProviders.length === 0) return undefined;\n            switch(criteria){\n                case \"speed\":\n                    return activeProviders.reduce((best, current)=>{\n                        const bestSpeed = best.stats?.averageResponseTime || Infinity;\n                        const currentSpeed = current.stats?.averageResponseTime || Infinity;\n                        return currentSpeed < bestSpeed ? current : best;\n                    });\n                case \"cost\":\n                    return activeProviders.reduce((best, current)=>{\n                        const bestCost = best.costPerToken || Infinity;\n                        const currentCost = current.costPerToken || Infinity;\n                        return currentCost < bestCost ? current : best;\n                    });\n                case \"reliability\":\n                default:\n                    return activeProviders.reduce((best, current)=>{\n                        const bestReliability = best.stats ? best.stats.successfulRequests / (best.stats.totalRequests || 1) : 0;\n                        const currentReliability = current.stats ? current.stats.successfulRequests / (current.stats.totalRequests || 1) : 0;\n                        return currentReliability > bestReliability ? current : best;\n                    });\n            }\n        },\n        resetAll: ()=>set(initialState),\n        // مسح جميع الإجابات فقط (الاحتفاظ بالإعدادات)\n        clearAllAnswers: ()=>set((state)=>({\n                    ...state,\n                    projectDefinition: {\n                        name: \"\",\n                        purpose: \"\",\n                        targetUsers: \"\",\n                        goals: \"\",\n                        scope: \"\",\n                        timeline: \"\"\n                    },\n                    contextMap: {\n                        timeContext: \"\",\n                        language: \"\",\n                        location: \"\",\n                        culturalContext: \"\",\n                        behavioralAspects: \"\",\n                        environmentalFactors: \"\"\n                    },\n                    emotionalTone: {\n                        personality: \"\",\n                        communicationStyle: \"\",\n                        userExperience: \"\",\n                        brandVoice: \"\",\n                        emotionalIntelligence: \"\",\n                        interactionFlow: \"\"\n                    },\n                    technicalLayer: {\n                        programmingLanguages: \"\",\n                        frameworks: \"\",\n                        llmModels: \"\",\n                        databases: \"\",\n                        apis: \"\",\n                        infrastructure: \"\"\n                    },\n                    legalRisk: {\n                        privacyConcerns: \"\",\n                        dataProtection: \"\",\n                        compliance: \"\",\n                        risks: \"\",\n                        mitigation: \"\",\n                        ethicalConsiderations: \"\"\n                    }\n                })),\n        getModuleData: (module)=>{\n            const state = get();\n            switch(module){\n                case \"project\":\n                    return state.projectDefinition;\n                case \"context\":\n                    return state.contextMap;\n                case \"emotional\":\n                    return state.emotionalTone;\n                case \"technical\":\n                    return state.technicalLayer;\n                case \"legal\":\n                    return state.legalRisk;\n                default:\n                    return {};\n            }\n        },\n        getAllData: ()=>{\n            const state = get();\n            return {\n                projectDefinition: state.projectDefinition,\n                contextMap: state.contextMap,\n                emotionalTone: state.emotionalTone,\n                technicalLayer: state.technicalLayer,\n                legalRisk: state.legalRisk\n            };\n        }\n    }), {\n    name: \"contextkit-storage\",\n    version: 1,\n    skipHydration: true\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/contextStore.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"22d6d015889c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29udGV4dGtpdC8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/N2UzNSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjIyZDZkMDE1ODg5Y1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/styles/glass-effects.css":
/*!**************************************!*\
  !*** ./src/styles/glass-effects.css ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5e0b1f04c4ea\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL2dsYXNzLWVmZmVjdHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29udGV4dGtpdC8uL3NyYy9zdHlsZXMvZ2xhc3MtZWZmZWN0cy5jc3M/NWRiMyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjVlMGIxZjA0YzRlYVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/glass-effects.css\n");

/***/ }),

/***/ "(rsc)/./src/styles/text-improvements.css":
/*!******************************************!*\
  !*** ./src/styles/text-improvements.css ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"757aecea32c0\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL3RleHQtaW1wcm92ZW1lbnRzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbnRleHRraXQvLi9zcmMvc3R5bGVzL3RleHQtaW1wcm92ZW1lbnRzLmNzcz8wYmQxIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNzU3YWVjZWEzMmMwXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/text-improvements.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _styles_glass_effects_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../styles/glass-effects.css */ \"(rsc)/./src/styles/glass-effects.css\");\n/* harmony import */ var _styles_text_improvements_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../styles/text-improvements.css */ \"(rsc)/./src/styles/text-improvements.css\");\n/* harmony import */ var _components_ThemeProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ThemeProvider */ \"(rsc)/./src/components/ThemeProvider.tsx\");\n/* harmony import */ var _components_StoreHydration__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/StoreHydration */ \"(rsc)/./src/components/StoreHydration.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"Craftery - AI-powered Idea Builder\",\n    description: \"AI-powered platform for building and developing creative ideas\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"antialiased font-arabic\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeProvider__WEBPACK_IMPORTED_MODULE_4__.ThemeProvider, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_StoreHydration__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this),\n                        children\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/settings/page.tsx":
/*!***********************************!*\
  !*** ./src/app/settings/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\app\settings\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\app\settings\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/StoreHydration.tsx":
/*!*******************************************!*\
  !*** ./src/components/StoreHydration.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\StoreHydration.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\StoreHydration.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/ThemeProvider.tsx":
/*!******************************************!*\
  !*** ./src/components/ThemeProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0),
/* harmony export */   useTheme: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\ThemeProvider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\ThemeProvider.tsx#ThemeProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\ThemeProvider.tsx#useTheme`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/zustand","vendor-chunks/@swc","vendor-chunks/styled-jsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsettings%2Fpage&page=%2Fsettings%2Fpage&appPaths=%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fsettings%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();