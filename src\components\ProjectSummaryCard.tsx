'use client';

import { useContextStore } from '@/store/contextStore';
import { 
  PROJECT_TYPES, 
  TARGET_PLATFORMS, 
  PROGRAMMING_LANGUAGES, 
  COMPLEXITY_LEVELS,
  BUDGET_RANGES,
  TEAM_SIZES,
  DEPLOYMENT_TYPES
} from '@/lib/projectOptions';

export default function ProjectSummaryCard() {
  const { projectDefinition, currentLanguage } = useContextStore();
  const isArabic = currentLanguage === 'ar';

  const getOptionLabel = (options: any[], id: string) => {
    const option = options.find(opt => opt.id === id);
    return option ? (isArabic ? option.labelAr : option.label) : '';
  };

  const getMultipleOptionsLabels = (options: any[], ids: string[]) => {
    return ids.map(id => getOptionLabel(options, id)).filter(Boolean).join(', ');
  };

  const hasAdvancedOptions = projectDefinition.projectType || 
    projectDefinition.targetPlatforms?.length > 0 ||
    projectDefinition.primaryLanguages?.length > 0 ||
    projectDefinition.complexity ||
    projectDefinition.budget ||
    projectDefinition.teamSize ||
    projectDefinition.deploymentType;

  if (!hasAdvancedOptions) {
    return null;
  }

  return (
    <div className="bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-800">
      <div className={`text-center mb-4 ${isArabic ? 'text-right' : 'text-left'}`}>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
          {isArabic ? '📋 ملخص تخصيص المشروع' : '📋 Project Customization Summary'}
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          {isArabic 
            ? 'الخيارات المتقدمة المحددة لمشروعك'
            : 'Advanced options selected for your project'
          }
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* نوع المشروع */}
        {projectDefinition.projectType && (
          <div className={`bg-white dark:bg-gray-800 rounded-lg p-4 ${isArabic ? 'text-right' : 'text-left'}`}>
            <div className="flex items-center gap-2 mb-2">
              <span className="text-lg">🎯</span>
              <span className="font-medium text-gray-700 dark:text-gray-300">
                {isArabic ? 'نوع المشروع' : 'Project Type'}
              </span>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {getOptionLabel(PROJECT_TYPES, projectDefinition.projectType)}
            </p>
          </div>
        )}

        {/* مستوى التعقيد */}
        {projectDefinition.complexity && (
          <div className={`bg-white dark:bg-gray-800 rounded-lg p-4 ${isArabic ? 'text-right' : 'text-left'}`}>
            <div className="flex items-center gap-2 mb-2">
              <span className="text-lg">⚡</span>
              <span className="font-medium text-gray-700 dark:text-gray-300">
                {isArabic ? 'مستوى التعقيد' : 'Complexity Level'}
              </span>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {getOptionLabel(COMPLEXITY_LEVELS, projectDefinition.complexity)}
            </p>
          </div>
        )}

        {/* المنصات المستهدفة */}
        {projectDefinition.targetPlatforms && projectDefinition.targetPlatforms.length > 0 && (
          <div className={`bg-white dark:bg-gray-800 rounded-lg p-4 ${isArabic ? 'text-right' : 'text-left'}`}>
            <div className="flex items-center gap-2 mb-2">
              <span className="text-lg">📱</span>
              <span className="font-medium text-gray-700 dark:text-gray-300">
                {isArabic ? 'المنصات المستهدفة' : 'Target Platforms'}
              </span>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {getMultipleOptionsLabels(TARGET_PLATFORMS, projectDefinition.targetPlatforms)}
            </p>
          </div>
        )}

        {/* لغات البرمجة */}
        {projectDefinition.primaryLanguages && projectDefinition.primaryLanguages.length > 0 && (
          <div className={`bg-white dark:bg-gray-800 rounded-lg p-4 ${isArabic ? 'text-right' : 'text-left'}`}>
            <div className="flex items-center gap-2 mb-2">
              <span className="text-lg">💻</span>
              <span className="font-medium text-gray-700 dark:text-gray-300">
                {isArabic ? 'لغات البرمجة' : 'Programming Languages'}
              </span>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {getMultipleOptionsLabels(PROGRAMMING_LANGUAGES, projectDefinition.primaryLanguages)}
            </p>
          </div>
        )}

        {/* حجم الفريق */}
        {projectDefinition.teamSize && (
          <div className={`bg-white dark:bg-gray-800 rounded-lg p-4 ${isArabic ? 'text-right' : 'text-left'}`}>
            <div className="flex items-center gap-2 mb-2">
              <span className="text-lg">👥</span>
              <span className="font-medium text-gray-700 dark:text-gray-300">
                {isArabic ? 'حجم الفريق' : 'Team Size'}
              </span>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {getOptionLabel(TEAM_SIZES, projectDefinition.teamSize)}
            </p>
          </div>
        )}

        {/* نطاق الميزانية */}
        {projectDefinition.budget && (
          <div className={`bg-white dark:bg-gray-800 rounded-lg p-4 ${isArabic ? 'text-right' : 'text-left'}`}>
            <div className="flex items-center gap-2 mb-2">
              <span className="text-lg">💰</span>
              <span className="font-medium text-gray-700 dark:text-gray-300">
                {isArabic ? 'نطاق الميزانية' : 'Budget Range'}
              </span>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {getOptionLabel(BUDGET_RANGES, projectDefinition.budget)}
            </p>
          </div>
        )}

        {/* نوع النشر */}
        {projectDefinition.deploymentType && (
          <div className={`bg-white dark:bg-gray-800 rounded-lg p-4 md:col-span-2 ${isArabic ? 'text-right' : 'text-left'}`}>
            <div className="flex items-center gap-2 mb-2">
              <span className="text-lg">☁️</span>
              <span className="font-medium text-gray-700 dark:text-gray-300">
                {isArabic ? 'نوع النشر' : 'Deployment Type'}
              </span>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {getOptionLabel(DEPLOYMENT_TYPES, projectDefinition.deploymentType)}
            </p>
          </div>
        )}
      </div>

      {/* نصائح ذكية */}
      <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/30 rounded-lg border border-blue-200 dark:border-blue-700">
        <div className="flex items-start gap-2">
          <span className="text-lg">💡</span>
          <div className={isArabic ? 'text-right' : 'text-left'}>
            <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-1">
              {isArabic ? 'نصيحة ذكية' : 'Smart Tip'}
            </h4>
            <p className="text-sm text-blue-700 dark:text-blue-300">
              {isArabic 
                ? 'هذه الخيارات ستساعد الذكاء الاصطناعي في تقديم اقتراحات أكثر دقة وتخصصاً لمشروعك.'
                : 'These options will help AI provide more accurate and specialized suggestions for your project.'
              }
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
