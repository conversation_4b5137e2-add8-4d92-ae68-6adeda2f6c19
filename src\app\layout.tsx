import type { Metada<PERSON> } from "next";
import "./globals.css";
import "../styles/glass-effects.css";
import "../styles/text-improvements.css";
import { ThemeProvider } from "@/components/ThemeProvider";
import StoreHydration from "@/components/StoreHydration";

export const metadata: Metadata = {
  title: "Craftery - AI-powered Idea Builder",
  description: "AI-powered platform for building and developing creative ideas",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      </head>
      <body className="antialiased font-arabic">
        <ThemeProvider>
          <StoreHydration />
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}
