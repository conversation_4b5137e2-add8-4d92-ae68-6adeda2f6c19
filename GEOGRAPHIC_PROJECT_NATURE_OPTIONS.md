# 🌍 إضافة خيارات المنطقة الجغرافية وطبيعة المشروع

## 📋 الملخص | Summary

تم إضافة خيارات جديدة لتحديد المكان/الدولة وطبيعة المشروع إلى خيارات التخصيص المتقدمة، مما يتيح للمستخدمين تحديد المنطقة الجغرافية المستهدفة ونوع العمل بدقة أكبر.

## 🚀 الميزات المضافة | Added Features

### 1. المناطق الجغرافية (GEOGRAPHIC_REGIONS)
- **المغرب** 🇲🇦 - المملكة المغربية - شمال أفريقيا
- **الشرق الأوسط** 🕌 - دول ومناطق الشرق الأوسط
- **شمال أفريقيا** 🏜️ - دول شمال أفريقيا
- **أفريقيا** 🌍 - القارة الأفريقية
- **العالم العربي** 🌙 - الدول والمناطق الناطقة بالعربية
- **أوروبا** 🇪🇺 - الدول الأوروبية
- **أمريكا الشمالية** 🌎 - الولايات المتحدة وكندا والمكسيك
- **آسيا** 🌏 - الدول والمناطق الآسيوية
- **عالمي/في جميع أنحاء العالم** 🌐 - تغطية وتوفر عالمي

### 2. طبيعة المشروع (PROJECT_NATURE)
- **لباس التخرج** 🎓 - أثواب التخرج الأكاديمية والقبعات والإكسسوارات
- **الأزياء والتجارة** 👗 - الملابس والأزياء وأعمال التجارة
- **التعليم** 📚 - الخدمات والمنصات التعليمية
- **الرعاية الصحية** 🏥 - الخدمات الطبية والرعاية الصحية
- **التكنولوجيا المالية** 💳 - الخدمات المصرفية والمدفوعات
- **التجارة الإلكترونية** 🛒 - منصات التسوق الإلكتروني
- **الطعام والشراب** 🍽️ - المطاعم وتوصيل الطعام
- **السفر والسياحة** ✈️ - حجز السفر والسياحة والضيافة
- **العقارات** 🏠 - إدارة الممتلكات وخدمات العقارات
- **الترفيه والإعلام** 🎬 - الألعاب والبث والمحتوى الإعلامي
- **اللوجستيات والنقل** 🚚 - خدمات الشحن والتوصيل والنقل
- **الزراعة والفلاحة** 🌾 - التكنولوجيا الزراعية وحلول الفلاحة
- **التصنيع** 🏭 - التصنيع الصناعي والإنتاج
- **الاستشارات والخدمات** 💼 - الاستشارات المهنية وخدمات الأعمال
- **غير ربحي ومنظمات** 🤝 - المنظمات غير الربحية والقضايا الاجتماعية
- **أخرى** 📋 - أنواع أعمال وصناعات أخرى

## 📁 الملفات المحدثة | Updated Files

### src/lib/projectOptions.ts
```typescript
// المناطق الجغرافية والدول
export const GEOGRAPHIC_REGIONS: ProjectOption[] = [
  {
    id: 'morocco',
    label: 'Morocco',
    labelAr: 'المغرب',
    description: 'Kingdom of Morocco - North Africa',
    descriptionAr: 'المملكة المغربية - شمال أفريقيا',
    icon: '🇲🇦'
  },
  // ... باقي الخيارات
];

// طبيعة المشروع ونوع الأعمال
export const PROJECT_NATURE: ProjectOption[] = [
  {
    id: 'graduation-clothing',
    label: 'Graduation Clothing',
    labelAr: 'لباس التخرج',
    description: 'Academic graduation gowns, caps, and accessories',
    descriptionAr: 'أثواب التخرج الأكاديمية والقبعات والإكسسوارات',
    icon: '🎓'
  },
  // ... باقي الخيارات
];
```

### src/components/AdvancedOptionsPanel.tsx
```typescript
import {
  PROJECT_TYPES,
  TARGET_PLATFORMS,
  PROGRAMMING_LANGUAGES,
  COMPLEXITY_LEVELS,
  BUDGET_RANGES,
  TEAM_SIZES,
  DEPLOYMENT_TYPES,
  GEOGRAPHIC_REGIONS,    // جديد
  PROJECT_NATURE         // جديد
} from '@/lib/projectOptions';

// إضافة المكونات الجديدة
<AdvancedOptionsSelector
  title="Project Nature"
  titleAr="طبيعة المشروع"
  options={PROJECT_NATURE}
  selectedValues={projectDefinition.projectNature ? [projectDefinition.projectNature] : []}
  onSelectionChange={(values) => handleProjectFieldChange('projectNature', values[0] || '')}
  placeholder="Select project nature (e.g., Graduation Clothing)"
  placeholderAr="اختر طبيعة المشروع (مثال: لباس التخرج)"
/>

<AdvancedOptionsSelector
  title="Geographic Region"
  titleAr="المنطقة الجغرافية"
  options={GEOGRAPHIC_REGIONS}
  selectedValues={projectDefinition.geographicRegion ? [projectDefinition.geographicRegion] : []}
  onSelectionChange={(values) => handleProjectFieldChange('geographicRegion', values[0] || '')}
  placeholder="Select target region (e.g., Morocco, Middle East)"
  placeholderAr="اختر المنطقة المستهدفة (مثال: المغرب، الشرق الأوسط)"
/>
```

### src/store/contextStore.ts
```typescript
export interface ProjectDefinition {
  name: string;
  purpose: string;
  targetUsers: string;
  goals: string;
  scope: string;
  timeline: string;
  // خيارات التخصيص المتقدمة
  projectType: string;
  projectNature: string;        // جديد
  geographicRegion: string;     // جديد
  targetPlatforms: string[];
  primaryLanguages: string[];
  complexity: string;
  budget: string;
  teamSize: string;
  deploymentType: string;
}

// القيم الافتراضية
const initialState = {
  projectDefinition: {
    name: '',
    purpose: '',
    targetUsers: '',
    goals: '',
    scope: '',
    timeline: '',
    projectType: '',
    projectNature: '',        // جديد
    geographicRegion: '',     // جديد
    targetPlatforms: [],
    primaryLanguages: [],
    complexity: '',
    budget: '',
    teamSize: '',
    deploymentType: ''
  },
  // ... باقي الحالة
};
```

## 🎯 كيفية الاستخدام | How to Use

### للمستخدمين | For Users
1. **افتح صفحة تعريف المشروع** أو أي صفحة مشروع أخرى
2. **فعّل الإعدادات العامة** باستخدام الزر في أعلى الصفحة
3. **اختر طبيعة المشروع** من القائمة المنسدلة (مثال: لباس التخرج)
4. **حدد المنطقة الجغرافية** المستهدفة (مثال: المغرب، الشرق الأوسط)
5. **احفظ الإعدادات** - سيتم حفظها تلقائياً

### أمثلة عملية | Practical Examples
- **مشروع لباس التخرج في المغرب**: اختر "لباس التخرج" + "المغرب"
- **منصة تعليمية في الشرق الأوسط**: اختر "التعليم" + "الشرق الأوسط"
- **تطبيق توصيل طعام عالمي**: اختر "الطعام والشراب" + "عالمي"

## 🌐 دعم اللغات | Language Support

### الترجمات الكاملة | Complete Translations
- **العربية**: جميع الخيارات مترجمة بالكامل مع أوصاف مفصلة
- **الإنجليزية**: نصوص واضحة ومفهومة
- **الأيقونات**: رموز تعبيرية مناسبة لكل خيار

### التخطيط RTL | RTL Layout
- **القوائم المنسدلة**: تدعم الاتجاه من اليمين لليسار
- **النصوص**: محاذاة صحيحة للعربية
- **الأيقونات**: موضعة بشكل مناسب

## 🔧 التفاصيل التقنية | Technical Details

### بنية البيانات | Data Structure
```typescript
interface ProjectOption {
  id: string;              // معرف فريد
  label: string;           // التسمية بالإنجليزية
  labelAr: string;         // التسمية بالعربية
  description?: string;    // الوصف بالإنجليزية
  descriptionAr?: string;  // الوصف بالعربية
  icon?: string;           // الأيقونة
}
```

### التكامل مع النظام | System Integration
- **إدارة الحالة**: Zustand store
- **التخزين المستمر**: localStorage
- **التحديث التلقائي**: real-time updates

## ✅ الحالة | Status

- ✅ **مكتمل**: إضافة خيارات المناطق الجغرافية (9 خيارات)
- ✅ **مكتمل**: إضافة خيارات طبيعة المشروع (16 خيار)
- ✅ **مكتمل**: تحديث واجهة المستخدم
- ✅ **مكتمل**: تحديث إدارة الحالة
- ✅ **مكتمل**: دعم RTL والترجمات
- ✅ **مكتمل**: التكامل مع النظام الموجود

## 📝 ملاحظات | Notes

- الخيارات الجديدة تظهر في قسم الخيارات المتقدمة
- يمكن اختيار خيار واحد فقط لكل فئة
- الخيارات محفوظة تلقائياً مع باقي بيانات المشروع
- تدعم البحث والتصفية في القوائم المنسدلة
- متوافقة مع جميع المتصفحات الحديثة
