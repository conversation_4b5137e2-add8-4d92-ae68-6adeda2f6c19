"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/project-definition/page",{

/***/ "(app-pages-browser)/./src/components/AdvancedOptionsPanel.tsx":
/*!*************************************************!*\
  !*** ./src/components/AdvancedOptionsPanel.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AdvancedOptionsPanel; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* harmony import */ var _components_AdvancedOptionsSelector__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AdvancedOptionsSelector */ \"(app-pages-browser)/./src/components/AdvancedOptionsSelector.tsx\");\n/* harmony import */ var _lib_projectOptions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/projectOptions */ \"(app-pages-browser)/./src/lib/projectOptions.ts\");\n/* harmony import */ var _lib_technicalOptions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/technicalOptions */ \"(app-pages-browser)/./src/lib/technicalOptions.ts\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction AdvancedOptionsPanel(param) {\n    let { moduleType } = param;\n    _s();\n    const { projectDefinition, technicalLayer, updateProjectDefinition, updateTechnicalLayer, showAdvancedOptions, currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore)();\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const isArabic = currentLanguage === \"ar\";\n    if (!showAdvancedOptions) {\n        return null;\n    }\n    const handleProjectFieldChange = (field, value)=>{\n        updateProjectDefinition({\n            [field]: value\n        });\n    };\n    const handleProjectArrayFieldChange = (field, values)=>{\n        updateProjectDefinition({\n            [field]: values\n        });\n    };\n    const handleTechnicalFieldChange = (field, value)=>{\n        updateTechnicalLayer({\n            [field]: value\n        });\n    };\n    const getTitle = ()=>{\n        if (moduleType === \"project-definition\") {\n            return isArabic ? \"خيارات التخصيص المتقدمة\" : \"Advanced Customization Options\";\n        } else {\n            return isArabic ? \"خيارات تقنية متقدمة\" : \"Advanced Technical Options\";\n        }\n    };\n    const getIcon = ()=>{\n        return moduleType === \"project-definition\" ? \"\\uD83C\\uDFAF\" : \"⚙️\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gradient-to-br from-indigo-50 to-purple-100 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-xl border border-indigo-200 dark:border-indigo-800 mb-8 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 cursor-pointer hover:bg-indigo-100/50 dark:hover:bg-indigo-800/30 transition-colors duration-200\",\n                onClick: ()=>setIsExpanded(!isExpanded),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-2 bg-indigo-100 dark:bg-indigo-800 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl\",\n                                        children: getIcon()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: isArabic ? \"text-right\" : \"text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 dark:text-gray-100\",\n                                            children: getTitle()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                            children: isArabic ? \"خصص مشروعك بخيارات متقدمة للحصول على نتائج أكثر دقة\" : \"Customize your project with advanced options for more precise results\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1 px-3 py-1 bg-indigo-100 dark:bg-indigo-800 rounded-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-3 h-3 text-indigo-600 dark:text-indigo-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs font-medium text-indigo-700 dark:text-indigo-300\",\n                                            children: isArabic ? \"ذكي\" : \"Smart\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this),\n                                isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-5 h-5 text-gray-500 dark:text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-5 h-5 text-gray-500 dark:text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this),\n            isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 pb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700\",\n                    children: [\n                        moduleType === \"project-definition\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedOptionsSelector__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    title: \"Project Type\",\n                                    titleAr: \"نوع المشروع\",\n                                    options: _lib_projectOptions__WEBPACK_IMPORTED_MODULE_4__.PROJECT_TYPES,\n                                    selectedValues: projectDefinition.projectType ? [\n                                        projectDefinition.projectType\n                                    ] : [],\n                                    onSelectionChange: (values)=>handleProjectFieldChange(\"projectType\", values[0] || \"\"),\n                                    placeholder: \"Select project type\",\n                                    placeholderAr: \"اختر نوع المشروع\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedOptionsSelector__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    title: \"Project Nature\",\n                                    titleAr: \"طبيعة المشروع\",\n                                    options: _lib_projectOptions__WEBPACK_IMPORTED_MODULE_4__.PROJECT_NATURE,\n                                    selectedValues: projectDefinition.projectNature ? [\n                                        projectDefinition.projectNature\n                                    ] : [],\n                                    onSelectionChange: (values)=>handleProjectFieldChange(\"projectNature\", values[0] || \"\"),\n                                    placeholder: \"Select project nature (e.g., Graduation Clothing)\",\n                                    placeholderAr: \"اختر طبيعة المشروع (مثال: لباس التخرج)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedOptionsSelector__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    title: \"Geographic Region\",\n                                    titleAr: \"المنطقة الجغرافية\",\n                                    options: _lib_projectOptions__WEBPACK_IMPORTED_MODULE_4__.GEOGRAPHIC_REGIONS,\n                                    selectedValues: projectDefinition.geographicRegion ? [\n                                        projectDefinition.geographicRegion\n                                    ] : [],\n                                    onSelectionChange: (values)=>handleProjectFieldChange(\"geographicRegion\", values[0] || \"\"),\n                                    placeholder: \"Select target region (e.g., Morocco, Middle East)\",\n                                    placeholderAr: \"اختر المنطقة المستهدفة (مثال: المغرب، الشرق الأوسط)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedOptionsSelector__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    title: \"Complexity Level\",\n                                    titleAr: \"مستوى التعقيد\",\n                                    options: _lib_projectOptions__WEBPACK_IMPORTED_MODULE_4__.COMPLEXITY_LEVELS,\n                                    selectedValues: projectDefinition.complexity ? [\n                                        projectDefinition.complexity\n                                    ] : [],\n                                    onSelectionChange: (values)=>handleProjectFieldChange(\"complexity\", values[0] || \"\"),\n                                    placeholder: \"Select complexity level\",\n                                    placeholderAr: \"اختر مستوى التعقيد\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedOptionsSelector__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    title: \"Target Platforms\",\n                                    titleAr: \"المنصات المستهدفة\",\n                                    options: _lib_projectOptions__WEBPACK_IMPORTED_MODULE_4__.TARGET_PLATFORMS,\n                                    selectedValues: projectDefinition.targetPlatforms || [],\n                                    onSelectionChange: (values)=>handleProjectArrayFieldChange(\"targetPlatforms\", values),\n                                    multiSelect: true,\n                                    placeholder: \"Select target platforms\",\n                                    placeholderAr: \"اختر المنصات المستهدفة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedOptionsSelector__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    title: \"Primary Programming Languages\",\n                                    titleAr: \"لغات البرمجة الأساسية\",\n                                    options: _lib_projectOptions__WEBPACK_IMPORTED_MODULE_4__.PROGRAMMING_LANGUAGES,\n                                    selectedValues: projectDefinition.primaryLanguages || [],\n                                    onSelectionChange: (values)=>handleProjectArrayFieldChange(\"primaryLanguages\", values),\n                                    multiSelect: true,\n                                    placeholder: \"Select programming languages\",\n                                    placeholderAr: \"اختر لغات البرمجة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:col-span-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedOptionsSelector__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        title: \"Deployment Type\",\n                                        titleAr: \"نوع النشر\",\n                                        options: _lib_projectOptions__WEBPACK_IMPORTED_MODULE_4__.DEPLOYMENT_TYPES,\n                                        selectedValues: projectDefinition.deploymentType ? [\n                                            projectDefinition.deploymentType\n                                        ] : [],\n                                        onSelectionChange: (values)=>handleProjectFieldChange(\"deploymentType\", values[0] || \"\"),\n                                        placeholder: \"Select deployment type\",\n                                        placeholderAr: \"اختر نوع النشر\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedOptionsSelector__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    title: \"Architecture Pattern\",\n                                    titleAr: \"نمط الهندسة المعمارية\",\n                                    options: _lib_technicalOptions__WEBPACK_IMPORTED_MODULE_5__.ARCHITECTURE_PATTERNS,\n                                    selectedValues: technicalLayer.architecturePattern ? [\n                                        technicalLayer.architecturePattern\n                                    ] : [],\n                                    onSelectionChange: (values)=>handleTechnicalFieldChange(\"architecturePattern\", values[0] || \"\"),\n                                    placeholder: \"Select architecture pattern\",\n                                    placeholderAr: \"اختر نمط الهندسة المعمارية\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedOptionsSelector__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    title: \"Scaling Strategy\",\n                                    titleAr: \"استراتيجية التوسع\",\n                                    options: _lib_technicalOptions__WEBPACK_IMPORTED_MODULE_5__.SCALING_STRATEGIES,\n                                    selectedValues: technicalLayer.scalingStrategy ? [\n                                        technicalLayer.scalingStrategy\n                                    ] : [],\n                                    onSelectionChange: (values)=>handleTechnicalFieldChange(\"scalingStrategy\", values[0] || \"\"),\n                                    placeholder: \"Select scaling strategy\",\n                                    placeholderAr: \"اختر استراتيجية التوسع\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedOptionsSelector__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    title: \"Security Requirements\",\n                                    titleAr: \"متطلبات الأمان\",\n                                    options: _lib_technicalOptions__WEBPACK_IMPORTED_MODULE_5__.SECURITY_REQUIREMENTS,\n                                    selectedValues: technicalLayer.securityRequirements ? [\n                                        technicalLayer.securityRequirements\n                                    ] : [],\n                                    onSelectionChange: (values)=>handleTechnicalFieldChange(\"securityRequirements\", values[0] || \"\"),\n                                    placeholder: \"Select security requirements\",\n                                    placeholderAr: \"اختر متطلبات الأمان\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedOptionsSelector__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    title: \"Performance Targets\",\n                                    titleAr: \"أهداف الأداء\",\n                                    options: _lib_technicalOptions__WEBPACK_IMPORTED_MODULE_5__.PERFORMANCE_TARGETS,\n                                    selectedValues: technicalLayer.performanceTargets ? [\n                                        technicalLayer.performanceTargets\n                                    ] : [],\n                                    onSelectionChange: (values)=>handleTechnicalFieldChange(\"performanceTargets\", values[0] || \"\"),\n                                    placeholder: \"Select performance targets\",\n                                    placeholderAr: \"اختر أهداف الأداء\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedOptionsSelector__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    title: \"Integration Needs\",\n                                    titleAr: \"احتياجات التكامل\",\n                                    options: _lib_technicalOptions__WEBPACK_IMPORTED_MODULE_5__.INTEGRATION_NEEDS,\n                                    selectedValues: technicalLayer.integrationNeeds ? [\n                                        technicalLayer.integrationNeeds\n                                    ] : [],\n                                    onSelectionChange: (values)=>handleTechnicalFieldChange(\"integrationNeeds\", values[0] || \"\"),\n                                    placeholder: \"Select integration needs\",\n                                    placeholderAr: \"اختر احتياجات التكامل\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedOptionsSelector__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    title: \"Monitoring Tools\",\n                                    titleAr: \"أدوات المراقبة\",\n                                    options: _lib_technicalOptions__WEBPACK_IMPORTED_MODULE_5__.MONITORING_TOOLS,\n                                    selectedValues: technicalLayer.monitoringTools ? [\n                                        technicalLayer.monitoringTools\n                                    ] : [],\n                                    onSelectionChange: (values)=>handleTechnicalFieldChange(\"monitoringTools\", values[0] || \"\"),\n                                    placeholder: \"Select monitoring tools\",\n                                    placeholderAr: \"اختر أدوات المراقبة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 p-4 bg-blue-50 dark:bg-blue-900/30 rounded-lg border border-blue-200 dark:border-blue-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: \"\\uD83D\\uDCA1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: isArabic ? \"text-right\" : \"text-left\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-blue-900 dark:text-blue-100 mb-1\",\n                                                children: isArabic ? \"نصيحة ذكية\" : \"Smart Tip\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-blue-700 dark:text-blue-300\",\n                                                children: isArabic ? \"كلما زادت دقة اختياراتك، كانت اقتراحات الذكاء الاصطناعي أكثر تخصصاً وفائدة لمشروعك.\" : \"The more precise your selections, the more specialized and useful AI suggestions will be for your project.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                lineNumber: 115,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n_s(AdvancedOptionsPanel, \"EPSJEo+XZJIf6IgMyymVi4P0tnQ=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore\n    ];\n});\n_c = AdvancedOptionsPanel;\nvar _c;\n$RefreshReg$(_c, \"AdvancedOptionsPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0FkdmFuY2VkT3B0aW9uc1BhbmVsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBRWlDO0FBQ3NCO0FBQ29CO0FBVzdDO0FBUUU7QUFDMEM7QUFNM0QsU0FBU21CLHFCQUFxQixLQUF5QztRQUF6QyxFQUFFQyxVQUFVLEVBQTZCLEdBQXpDOztJQUMzQyxNQUFNLEVBQ0pDLGlCQUFpQixFQUNqQkMsY0FBYyxFQUNkQyx1QkFBdUIsRUFDdkJDLG9CQUFvQixFQUNwQkMsbUJBQW1CLEVBQ25CQyxlQUFlLEVBQ2hCLEdBQUd6QixvRUFBZUE7SUFFbkIsTUFBTSxDQUFDMEIsWUFBWUMsY0FBYyxHQUFHNUIsK0NBQVFBLENBQUM7SUFDN0MsTUFBTTZCLFdBQVdILG9CQUFvQjtJQUVyQyxJQUFJLENBQUNELHFCQUFxQjtRQUN4QixPQUFPO0lBQ1Q7SUFFQSxNQUFNSywyQkFBMkIsQ0FBQ0MsT0FBdUNDO1FBQ3ZFVCx3QkFBd0I7WUFBRSxDQUFDUSxNQUFNLEVBQUVDO1FBQU07SUFDM0M7SUFFQSxNQUFNQyxnQ0FBZ0MsQ0FBQ0YsT0FBdUNHO1FBQzVFWCx3QkFBd0I7WUFBRSxDQUFDUSxNQUFNLEVBQUVHO1FBQU87SUFDNUM7SUFFQSxNQUFNQyw2QkFBNkIsQ0FBQ0osT0FBb0NDO1FBQ3RFUixxQkFBcUI7WUFBRSxDQUFDTyxNQUFNLEVBQUVDO1FBQU07SUFDeEM7SUFFQSxNQUFNSSxXQUFXO1FBQ2YsSUFBSWhCLGVBQWUsc0JBQXNCO1lBQ3ZDLE9BQU9TLFdBQVcsNEJBQTRCO1FBQ2hELE9BQU87WUFDTCxPQUFPQSxXQUFXLHdCQUF3QjtRQUM1QztJQUNGO0lBRUEsTUFBTVEsVUFBVTtRQUNkLE9BQU9qQixlQUFlLHVCQUF1QixpQkFBTztJQUN0RDtJQUVBLHFCQUNFLDhEQUFDa0I7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNEO2dCQUNDQyxXQUFVO2dCQUNWQyxTQUFTLElBQU1aLGNBQWMsQ0FBQ0Q7MEJBRTlCLDRFQUFDVztvQkFBSUMsV0FBVyxxQ0FBd0UsT0FBbkNWLFdBQVcscUJBQXFCOztzQ0FDbkYsOERBQUNTOzRCQUFJQyxXQUFXLDJCQUE4RCxPQUFuQ1YsV0FBVyxxQkFBcUI7OzhDQUN6RSw4REFBQ1M7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNFO3dDQUFLRixXQUFVO2tEQUFXRjs7Ozs7Ozs7Ozs7OENBRTdCLDhEQUFDQztvQ0FBSUMsV0FBV1YsV0FBVyxlQUFlOztzREFDeEMsOERBQUNhOzRDQUFHSCxXQUFVO3NEQUNYSDs7Ozs7O3NEQUVILDhEQUFDTzs0Q0FBRUosV0FBVTtzREFDVlYsV0FDRyx3REFDQTs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU1WLDhEQUFDUzs0QkFBSUMsV0FBVywyQkFBOEQsT0FBbkNWLFdBQVcscUJBQXFCOzs4Q0FDekUsOERBQUNTO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ3JCLDBHQUFRQTs0Q0FBQ3FCLFdBQVU7Ozs7OztzREFDcEIsOERBQUNFOzRDQUFLRixXQUFVO3NEQUNiVixXQUFXLFFBQVE7Ozs7Ozs7Ozs7OztnQ0FHdkJGLDJCQUNDLDhEQUFDViwwR0FBU0E7b0NBQUNzQixXQUFVOzs7Ozt5REFFckIsOERBQUN2QiwwR0FBV0E7b0NBQUN1QixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQU85QlosNEJBQ0MsOERBQUNXO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs7d0JBQ1puQixlQUFlLHFDQUNkLDhEQUFDa0I7NEJBQUlDLFdBQVU7OzhDQUViLDhEQUFDckMsMkVBQXVCQTtvQ0FDdEIwQyxPQUFNO29DQUNOQyxTQUFRO29DQUNSQyxTQUFTM0MsOERBQWFBO29DQUN0QjRDLGdCQUFnQjFCLGtCQUFrQjJCLFdBQVcsR0FBRzt3Q0FBQzNCLGtCQUFrQjJCLFdBQVc7cUNBQUMsR0FBRyxFQUFFO29DQUNwRkMsbUJBQW1CLENBQUNmLFNBQVdKLHlCQUF5QixlQUFlSSxNQUFNLENBQUMsRUFBRSxJQUFJO29DQUNwRmdCLGFBQVk7b0NBQ1pDLGVBQWM7Ozs7Ozs4Q0FJaEIsOERBQUNqRCwyRUFBdUJBO29DQUN0QjBDLE9BQU07b0NBQ05DLFNBQVE7b0NBQ1JDLFNBQVNyQywrREFBY0E7b0NBQ3ZCc0MsZ0JBQWdCMUIsa0JBQWtCK0IsYUFBYSxHQUFHO3dDQUFDL0Isa0JBQWtCK0IsYUFBYTtxQ0FBQyxHQUFHLEVBQUU7b0NBQ3hGSCxtQkFBbUIsQ0FBQ2YsU0FBV0oseUJBQXlCLGlCQUFpQkksTUFBTSxDQUFDLEVBQUUsSUFBSTtvQ0FDdEZnQixhQUFZO29DQUNaQyxlQUFjOzs7Ozs7OENBSWhCLDhEQUFDakQsMkVBQXVCQTtvQ0FDdEIwQyxPQUFNO29DQUNOQyxTQUFRO29DQUNSQyxTQUFTdEMsbUVBQWtCQTtvQ0FDM0J1QyxnQkFBZ0IxQixrQkFBa0JnQyxnQkFBZ0IsR0FBRzt3Q0FBQ2hDLGtCQUFrQmdDLGdCQUFnQjtxQ0FBQyxHQUFHLEVBQUU7b0NBQzlGSixtQkFBbUIsQ0FBQ2YsU0FBV0oseUJBQXlCLG9CQUFvQkksTUFBTSxDQUFDLEVBQUUsSUFBSTtvQ0FDekZnQixhQUFZO29DQUNaQyxlQUFjOzs7Ozs7OENBSWhCLDhEQUFDakQsMkVBQXVCQTtvQ0FDdEIwQyxPQUFNO29DQUNOQyxTQUFRO29DQUNSQyxTQUFTeEMsa0VBQWlCQTtvQ0FDMUJ5QyxnQkFBZ0IxQixrQkFBa0JpQyxVQUFVLEdBQUc7d0NBQUNqQyxrQkFBa0JpQyxVQUFVO3FDQUFDLEdBQUcsRUFBRTtvQ0FDbEZMLG1CQUFtQixDQUFDZixTQUFXSix5QkFBeUIsY0FBY0ksTUFBTSxDQUFDLEVBQUUsSUFBSTtvQ0FDbkZnQixhQUFZO29DQUNaQyxlQUFjOzs7Ozs7OENBSWhCLDhEQUFDakQsMkVBQXVCQTtvQ0FDdEIwQyxPQUFNO29DQUNOQyxTQUFRO29DQUNSQyxTQUFTMUMsaUVBQWdCQTtvQ0FDekIyQyxnQkFBZ0IxQixrQkFBa0JrQyxlQUFlLElBQUksRUFBRTtvQ0FDdkROLG1CQUFtQixDQUFDZixTQUFXRCw4QkFBOEIsbUJBQW1CQztvQ0FDaEZzQixhQUFhO29DQUNiTixhQUFZO29DQUNaQyxlQUFjOzs7Ozs7OENBSWhCLDhEQUFDakQsMkVBQXVCQTtvQ0FDdEIwQyxPQUFNO29DQUNOQyxTQUFRO29DQUNSQyxTQUFTekMsc0VBQXFCQTtvQ0FDOUIwQyxnQkFBZ0IxQixrQkFBa0JvQyxnQkFBZ0IsSUFBSSxFQUFFO29DQUN4RFIsbUJBQW1CLENBQUNmLFNBQVdELDhCQUE4QixvQkFBb0JDO29DQUNqRnNCLGFBQWE7b0NBQ2JOLGFBQVk7b0NBQ1pDLGVBQWM7Ozs7Ozs4Q0FJaEIsOERBQUNiO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDckMsMkVBQXVCQTt3Q0FDdEIwQyxPQUFNO3dDQUNOQyxTQUFRO3dDQUNSQyxTQUFTdkMsaUVBQWdCQTt3Q0FDekJ3QyxnQkFBZ0IxQixrQkFBa0JxQyxjQUFjLEdBQUc7NENBQUNyQyxrQkFBa0JxQyxjQUFjO3lDQUFDLEdBQUcsRUFBRTt3Q0FDMUZULG1CQUFtQixDQUFDZixTQUFXSix5QkFBeUIsa0JBQWtCSSxNQUFNLENBQUMsRUFBRSxJQUFJO3dDQUN2RmdCLGFBQVk7d0NBQ1pDLGVBQWM7Ozs7Ozs7Ozs7Ozs7Ozs7aURBS3BCLDhEQUFDYjs0QkFBSUMsV0FBVTs7OENBRWIsOERBQUNyQywyRUFBdUJBO29DQUN0QjBDLE9BQU07b0NBQ05DLFNBQVE7b0NBQ1JDLFNBQVNwQyx3RUFBcUJBO29DQUM5QnFDLGdCQUFnQnpCLGVBQWVxQyxtQkFBbUIsR0FBRzt3Q0FBQ3JDLGVBQWVxQyxtQkFBbUI7cUNBQUMsR0FBRyxFQUFFO29DQUM5RlYsbUJBQW1CLENBQUNmLFNBQVdDLDJCQUEyQix1QkFBdUJELE1BQU0sQ0FBQyxFQUFFLElBQUk7b0NBQzlGZ0IsYUFBWTtvQ0FDWkMsZUFBYzs7Ozs7OzhDQUloQiw4REFBQ2pELDJFQUF1QkE7b0NBQ3RCMEMsT0FBTTtvQ0FDTkMsU0FBUTtvQ0FDUkMsU0FBU25DLHFFQUFrQkE7b0NBQzNCb0MsZ0JBQWdCekIsZUFBZXNDLGVBQWUsR0FBRzt3Q0FBQ3RDLGVBQWVzQyxlQUFlO3FDQUFDLEdBQUcsRUFBRTtvQ0FDdEZYLG1CQUFtQixDQUFDZixTQUFXQywyQkFBMkIsbUJBQW1CRCxNQUFNLENBQUMsRUFBRSxJQUFJO29DQUMxRmdCLGFBQVk7b0NBQ1pDLGVBQWM7Ozs7Ozs4Q0FJaEIsOERBQUNqRCwyRUFBdUJBO29DQUN0QjBDLE9BQU07b0NBQ05DLFNBQVE7b0NBQ1JDLFNBQVNsQyx3RUFBcUJBO29DQUM5Qm1DLGdCQUFnQnpCLGVBQWV1QyxvQkFBb0IsR0FBRzt3Q0FBQ3ZDLGVBQWV1QyxvQkFBb0I7cUNBQUMsR0FBRyxFQUFFO29DQUNoR1osbUJBQW1CLENBQUNmLFNBQVdDLDJCQUEyQix3QkFBd0JELE1BQU0sQ0FBQyxFQUFFLElBQUk7b0NBQy9GZ0IsYUFBWTtvQ0FDWkMsZUFBYzs7Ozs7OzhDQUloQiw4REFBQ2pELDJFQUF1QkE7b0NBQ3RCMEMsT0FBTTtvQ0FDTkMsU0FBUTtvQ0FDUkMsU0FBU2pDLHNFQUFtQkE7b0NBQzVCa0MsZ0JBQWdCekIsZUFBZXdDLGtCQUFrQixHQUFHO3dDQUFDeEMsZUFBZXdDLGtCQUFrQjtxQ0FBQyxHQUFHLEVBQUU7b0NBQzVGYixtQkFBbUIsQ0FBQ2YsU0FBV0MsMkJBQTJCLHNCQUFzQkQsTUFBTSxDQUFDLEVBQUUsSUFBSTtvQ0FDN0ZnQixhQUFZO29DQUNaQyxlQUFjOzs7Ozs7OENBSWhCLDhEQUFDakQsMkVBQXVCQTtvQ0FDdEIwQyxPQUFNO29DQUNOQyxTQUFRO29DQUNSQyxTQUFTaEMsb0VBQWlCQTtvQ0FDMUJpQyxnQkFBZ0J6QixlQUFleUMsZ0JBQWdCLEdBQUc7d0NBQUN6QyxlQUFleUMsZ0JBQWdCO3FDQUFDLEdBQUcsRUFBRTtvQ0FDeEZkLG1CQUFtQixDQUFDZixTQUFXQywyQkFBMkIsb0JBQW9CRCxNQUFNLENBQUMsRUFBRSxJQUFJO29DQUMzRmdCLGFBQVk7b0NBQ1pDLGVBQWM7Ozs7Ozs4Q0FJaEIsOERBQUNqRCwyRUFBdUJBO29DQUN0QjBDLE9BQU07b0NBQ05DLFNBQVE7b0NBQ1JDLFNBQVMvQixtRUFBZ0JBO29DQUN6QmdDLGdCQUFnQnpCLGVBQWUwQyxlQUFlLEdBQUc7d0NBQUMxQyxlQUFlMEMsZUFBZTtxQ0FBQyxHQUFHLEVBQUU7b0NBQ3RGZixtQkFBbUIsQ0FBQ2YsU0FBV0MsMkJBQTJCLG1CQUFtQkQsTUFBTSxDQUFDLEVBQUUsSUFBSTtvQ0FDMUZnQixhQUFZO29DQUNaQyxlQUFjOzs7Ozs7Ozs7Ozs7c0NBTXBCLDhEQUFDYjs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRTt3Q0FBS0YsV0FBVTtrREFBVTs7Ozs7O2tEQUMxQiw4REFBQ0Q7d0NBQUlDLFdBQVdWLFdBQVcsZUFBZTs7MERBQ3hDLDhEQUFDb0M7Z0RBQUcxQixXQUFVOzBEQUNYVixXQUFXLGVBQWU7Ozs7OzswREFFN0IsOERBQUNjO2dEQUFFSixXQUFVOzBEQUNWVixXQUNHLHdGQUNBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBV3hCO0dBclF3QlY7O1FBUWxCbEIsZ0VBQWVBOzs7S0FSR2tCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL0FkdmFuY2VkT3B0aW9uc1BhbmVsLnRzeD9iZmVkIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VDb250ZXh0U3RvcmUgfSBmcm9tICdAL3N0b3JlL2NvbnRleHRTdG9yZSc7XG5pbXBvcnQgQWR2YW5jZWRPcHRpb25zU2VsZWN0b3IgZnJvbSAnQC9jb21wb25lbnRzL0FkdmFuY2VkT3B0aW9uc1NlbGVjdG9yJztcbmltcG9ydCB7XG4gIFBST0pFQ1RfVFlQRVMsXG4gIFRBUkdFVF9QTEFURk9STVMsXG4gIFBST0dSQU1NSU5HX0xBTkdVQUdFUyxcbiAgQ09NUExFWElUWV9MRVZFTFMsXG4gIEJVREdFVF9SQU5HRVMsXG4gIFRFQU1fU0laRVMsXG4gIERFUExPWU1FTlRfVFlQRVMsXG4gIEdFT0dSQVBISUNfUkVHSU9OUyxcbiAgUFJPSkVDVF9OQVRVUkVcbn0gZnJvbSAnQC9saWIvcHJvamVjdE9wdGlvbnMnO1xuaW1wb3J0IHtcbiAgQVJDSElURUNUVVJFX1BBVFRFUk5TLFxuICBTQ0FMSU5HX1NUUkFURUdJRVMsXG4gIFNFQ1VSSVRZX1JFUVVJUkVNRU5UUyxcbiAgUEVSRk9STUFOQ0VfVEFSR0VUUyxcbiAgSU5URUdSQVRJT05fTkVFRFMsXG4gIE1PTklUT1JJTkdfVE9PTFNcbn0gZnJvbSAnQC9saWIvdGVjaG5pY2FsT3B0aW9ucyc7XG5pbXBvcnQgeyBDaGV2cm9uRG93biwgQ2hldnJvblVwLCBTZXR0aW5ncywgU3BhcmtsZXMgfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuXG5pbnRlcmZhY2UgQWR2YW5jZWRPcHRpb25zUGFuZWxQcm9wcyB7XG4gIG1vZHVsZVR5cGU6ICdwcm9qZWN0LWRlZmluaXRpb24nIHwgJ3RlY2huaWNhbC1sYXllcic7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEFkdmFuY2VkT3B0aW9uc1BhbmVsKHsgbW9kdWxlVHlwZSB9OiBBZHZhbmNlZE9wdGlvbnNQYW5lbFByb3BzKSB7XG4gIGNvbnN0IHsgXG4gICAgcHJvamVjdERlZmluaXRpb24sIFxuICAgIHRlY2huaWNhbExheWVyLCBcbiAgICB1cGRhdGVQcm9qZWN0RGVmaW5pdGlvbiwgXG4gICAgdXBkYXRlVGVjaG5pY2FsTGF5ZXIsXG4gICAgc2hvd0FkdmFuY2VkT3B0aW9ucyxcbiAgICBjdXJyZW50TGFuZ3VhZ2UgXG4gIH0gPSB1c2VDb250ZXh0U3RvcmUoKTtcbiAgXG4gIGNvbnN0IFtpc0V4cGFuZGVkLCBzZXRJc0V4cGFuZGVkXSA9IHVzZVN0YXRlKHRydWUpO1xuICBjb25zdCBpc0FyYWJpYyA9IGN1cnJlbnRMYW5ndWFnZSA9PT0gJ2FyJztcblxuICBpZiAoIXNob3dBZHZhbmNlZE9wdGlvbnMpIHtcbiAgICByZXR1cm4gbnVsbDtcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVByb2plY3RGaWVsZENoYW5nZSA9IChmaWVsZDoga2V5b2YgdHlwZW9mIHByb2plY3REZWZpbml0aW9uLCB2YWx1ZTogc3RyaW5nKSA9PiB7XG4gICAgdXBkYXRlUHJvamVjdERlZmluaXRpb24oeyBbZmllbGRdOiB2YWx1ZSB9KTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVQcm9qZWN0QXJyYXlGaWVsZENoYW5nZSA9IChmaWVsZDoga2V5b2YgdHlwZW9mIHByb2plY3REZWZpbml0aW9uLCB2YWx1ZXM6IHN0cmluZ1tdKSA9PiB7XG4gICAgdXBkYXRlUHJvamVjdERlZmluaXRpb24oeyBbZmllbGRdOiB2YWx1ZXMgfSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlVGVjaG5pY2FsRmllbGRDaGFuZ2UgPSAoZmllbGQ6IGtleW9mIHR5cGVvZiB0ZWNobmljYWxMYXllciwgdmFsdWU6IHN0cmluZykgPT4ge1xuICAgIHVwZGF0ZVRlY2huaWNhbExheWVyKHsgW2ZpZWxkXTogdmFsdWUgfSk7XG4gIH07XG5cbiAgY29uc3QgZ2V0VGl0bGUgPSAoKSA9PiB7XG4gICAgaWYgKG1vZHVsZVR5cGUgPT09ICdwcm9qZWN0LWRlZmluaXRpb24nKSB7XG4gICAgICByZXR1cm4gaXNBcmFiaWMgPyAn2K7Zitin2LHYp9iqINin2YTYqtiu2LXZiti1INin2YTZhdiq2YLYr9mF2KknIDogJ0FkdmFuY2VkIEN1c3RvbWl6YXRpb24gT3B0aW9ucyc7XG4gICAgfSBlbHNlIHtcbiAgICAgIHJldHVybiBpc0FyYWJpYyA/ICfYrtmK2KfYsdin2Kog2KrZgtmG2YrYqSDZhdiq2YLYr9mF2KknIDogJ0FkdmFuY2VkIFRlY2huaWNhbCBPcHRpb25zJztcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgZ2V0SWNvbiA9ICgpID0+IHtcbiAgICByZXR1cm4gbW9kdWxlVHlwZSA9PT0gJ3Byb2plY3QtZGVmaW5pdGlvbicgPyAn8J+OrycgOiAn4pqZ77iPJztcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1pbmRpZ28tNTAgdG8tcHVycGxlLTEwMCBkYXJrOmZyb20taW5kaWdvLTkwMC8yMCBkYXJrOnRvLXB1cnBsZS05MDAvMjAgcm91bmRlZC14bCBib3JkZXIgYm9yZGVyLWluZGlnby0yMDAgZGFyazpib3JkZXItaW5kaWdvLTgwMCBtYi04IG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgIDxkaXYgXG4gICAgICAgIGNsYXNzTmFtZT1cInAtNiBjdXJzb3ItcG9pbnRlciBob3ZlcjpiZy1pbmRpZ28tMTAwLzUwIGRhcms6aG92ZXI6YmctaW5kaWdvLTgwMC8zMCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBcIlxuICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc0V4cGFuZGVkKCFpc0V4cGFuZGVkKX1cbiAgICAgID5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gJHtpc0FyYWJpYyA/ICdmbGV4LXJvdy1yZXZlcnNlJyA6ICcnfWB9PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTMgJHtpc0FyYWJpYyA/ICdmbGV4LXJvdy1yZXZlcnNlJyA6ICcnfWB9PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTIgYmctaW5kaWdvLTEwMCBkYXJrOmJnLWluZGlnby04MDAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhsXCI+e2dldEljb24oKX08L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtpc0FyYWJpYyA/ICd0ZXh0LXJpZ2h0JyA6ICd0ZXh0LWxlZnQnfT5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC1ncmF5LTEwMFwiPlxuICAgICAgICAgICAgICAgIHtnZXRUaXRsZSgpfVxuICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgZGFyazp0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICAgICAge2lzQXJhYmljIFxuICAgICAgICAgICAgICAgICAgPyAn2K7Ytdi1INmF2LTYsdmI2LnZgyDYqNiu2YrYp9ix2KfYqiDZhdiq2YLYr9mF2Kkg2YTZhNit2LXZiNmEINi52YTZiSDZhtiq2KfYptisINij2YPYq9ixINiv2YLYqSdcbiAgICAgICAgICAgICAgICAgIDogJ0N1c3RvbWl6ZSB5b3VyIHByb2plY3Qgd2l0aCBhZHZhbmNlZCBvcHRpb25zIGZvciBtb3JlIHByZWNpc2UgcmVzdWx0cydcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgJHtpc0FyYWJpYyA/ICdmbGV4LXJvdy1yZXZlcnNlJyA6ICcnfWB9PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMSBweC0zIHB5LTEgYmctaW5kaWdvLTEwMCBkYXJrOmJnLWluZGlnby04MDAgcm91bmRlZC1mdWxsXCI+XG4gICAgICAgICAgICAgIDxTcGFya2xlcyBjbGFzc05hbWU9XCJ3LTMgaC0zIHRleHQtaW5kaWdvLTYwMCBkYXJrOnRleHQtaW5kaWdvLTQwMFwiIC8+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1pbmRpZ28tNzAwIGRhcms6dGV4dC1pbmRpZ28tMzAwXCI+XG4gICAgICAgICAgICAgICAge2lzQXJhYmljID8gJ9iw2YPZiicgOiAnU21hcnQnfVxuICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIHtpc0V4cGFuZGVkID8gKFxuICAgICAgICAgICAgICA8Q2hldnJvblVwIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1ncmF5LTUwMCBkYXJrOnRleHQtZ3JheS00MDBcIiAvPlxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgPENoZXZyb25Eb3duIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1ncmF5LTUwMCBkYXJrOnRleHQtZ3JheS00MDBcIiAvPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIENvbnRlbnQgKi99XG4gICAgICB7aXNFeHBhbmRlZCAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtNiBwYi02XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBkYXJrOmJnLWdyYXktODAwIHJvdW5kZWQtbGcgcC02IGJvcmRlciBib3JkZXItZ3JheS0yMDAgZGFyazpib3JkZXItZ3JheS03MDBcIj5cbiAgICAgICAgICAgIHttb2R1bGVUeXBlID09PSAncHJvamVjdC1kZWZpbml0aW9uJyA/IChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC02XCI+XG4gICAgICAgICAgICAgICAgey8qINmG2YjYuSDYp9mE2YXYtNix2YjYuSAqL31cbiAgICAgICAgICAgICAgICA8QWR2YW5jZWRPcHRpb25zU2VsZWN0b3JcbiAgICAgICAgICAgICAgICAgIHRpdGxlPVwiUHJvamVjdCBUeXBlXCJcbiAgICAgICAgICAgICAgICAgIHRpdGxlQXI9XCLZhtmI2Lkg2KfZhNmF2LTYsdmI2LlcIlxuICAgICAgICAgICAgICAgICAgb3B0aW9ucz17UFJPSkVDVF9UWVBFU31cbiAgICAgICAgICAgICAgICAgIHNlbGVjdGVkVmFsdWVzPXtwcm9qZWN0RGVmaW5pdGlvbi5wcm9qZWN0VHlwZSA/IFtwcm9qZWN0RGVmaW5pdGlvbi5wcm9qZWN0VHlwZV0gOiBbXX1cbiAgICAgICAgICAgICAgICAgIG9uU2VsZWN0aW9uQ2hhbmdlPXsodmFsdWVzKSA9PiBoYW5kbGVQcm9qZWN0RmllbGRDaGFuZ2UoJ3Byb2plY3RUeXBlJywgdmFsdWVzWzBdIHx8ICcnKX1cbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiU2VsZWN0IHByb2plY3QgdHlwZVwiXG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlckFyPVwi2KfYrtiq2LEg2YbZiNi5INin2YTZhdi02LHZiNi5XCJcbiAgICAgICAgICAgICAgICAvPlxuXG4gICAgICAgICAgICAgICAgey8qINi32KjZiti52Kkg2KfZhNmF2LTYsdmI2LkgKi99XG4gICAgICAgICAgICAgICAgPEFkdmFuY2VkT3B0aW9uc1NlbGVjdG9yXG4gICAgICAgICAgICAgICAgICB0aXRsZT1cIlByb2plY3QgTmF0dXJlXCJcbiAgICAgICAgICAgICAgICAgIHRpdGxlQXI9XCLYt9io2YrYudipINin2YTZhdi02LHZiNi5XCJcbiAgICAgICAgICAgICAgICAgIG9wdGlvbnM9e1BST0pFQ1RfTkFUVVJFfVxuICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRWYWx1ZXM9e3Byb2plY3REZWZpbml0aW9uLnByb2plY3ROYXR1cmUgPyBbcHJvamVjdERlZmluaXRpb24ucHJvamVjdE5hdHVyZV0gOiBbXX1cbiAgICAgICAgICAgICAgICAgIG9uU2VsZWN0aW9uQ2hhbmdlPXsodmFsdWVzKSA9PiBoYW5kbGVQcm9qZWN0RmllbGRDaGFuZ2UoJ3Byb2plY3ROYXR1cmUnLCB2YWx1ZXNbMF0gfHwgJycpfVxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJTZWxlY3QgcHJvamVjdCBuYXR1cmUgKGUuZy4sIEdyYWR1YXRpb24gQ2xvdGhpbmcpXCJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyQXI9XCLYp9iu2KrYsSDYt9io2YrYudipINin2YTZhdi02LHZiNi5ICjZhdir2KfZhDog2YTYqNin2LMg2KfZhNiq2K7YsdisKVwiXG4gICAgICAgICAgICAgICAgLz5cblxuICAgICAgICAgICAgICAgIHsvKiDYp9mE2YXZhti32YLYqSDYp9mE2KzYutix2KfZgdmK2KkgKi99XG4gICAgICAgICAgICAgICAgPEFkdmFuY2VkT3B0aW9uc1NlbGVjdG9yXG4gICAgICAgICAgICAgICAgICB0aXRsZT1cIkdlb2dyYXBoaWMgUmVnaW9uXCJcbiAgICAgICAgICAgICAgICAgIHRpdGxlQXI9XCLYp9mE2YXZhti32YLYqSDYp9mE2KzYutix2KfZgdmK2KlcIlxuICAgICAgICAgICAgICAgICAgb3B0aW9ucz17R0VPR1JBUEhJQ19SRUdJT05TfVxuICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRWYWx1ZXM9e3Byb2plY3REZWZpbml0aW9uLmdlb2dyYXBoaWNSZWdpb24gPyBbcHJvamVjdERlZmluaXRpb24uZ2VvZ3JhcGhpY1JlZ2lvbl0gOiBbXX1cbiAgICAgICAgICAgICAgICAgIG9uU2VsZWN0aW9uQ2hhbmdlPXsodmFsdWVzKSA9PiBoYW5kbGVQcm9qZWN0RmllbGRDaGFuZ2UoJ2dlb2dyYXBoaWNSZWdpb24nLCB2YWx1ZXNbMF0gfHwgJycpfVxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJTZWxlY3QgdGFyZ2V0IHJlZ2lvbiAoZS5nLiwgTW9yb2NjbywgTWlkZGxlIEVhc3QpXCJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyQXI9XCLYp9iu2KrYsSDYp9mE2YXZhti32YLYqSDYp9mE2YXYs9iq2YfYr9mB2KkgKNmF2KvYp9mEOiDYp9mE2YXYutix2KjYjCDYp9mE2LTYsdmCINin2YTYo9mI2LPYtylcIlxuICAgICAgICAgICAgICAgIC8+XG5cbiAgICAgICAgICAgICAgICB7Lyog2YXYs9iq2YjZiSDYp9mE2KrYudmC2YrYryAqL31cbiAgICAgICAgICAgICAgICA8QWR2YW5jZWRPcHRpb25zU2VsZWN0b3JcbiAgICAgICAgICAgICAgICAgIHRpdGxlPVwiQ29tcGxleGl0eSBMZXZlbFwiXG4gICAgICAgICAgICAgICAgICB0aXRsZUFyPVwi2YXYs9iq2YjZiSDYp9mE2KrYudmC2YrYr1wiXG4gICAgICAgICAgICAgICAgICBvcHRpb25zPXtDT01QTEVYSVRZX0xFVkVMU31cbiAgICAgICAgICAgICAgICAgIHNlbGVjdGVkVmFsdWVzPXtwcm9qZWN0RGVmaW5pdGlvbi5jb21wbGV4aXR5ID8gW3Byb2plY3REZWZpbml0aW9uLmNvbXBsZXhpdHldIDogW119XG4gICAgICAgICAgICAgICAgICBvblNlbGVjdGlvbkNoYW5nZT17KHZhbHVlcykgPT4gaGFuZGxlUHJvamVjdEZpZWxkQ2hhbmdlKCdjb21wbGV4aXR5JywgdmFsdWVzWzBdIHx8ICcnKX1cbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiU2VsZWN0IGNvbXBsZXhpdHkgbGV2ZWxcIlxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXJBcj1cItin2K7YqtixINmF2LPYqtmI2Ykg2KfZhNiq2LnZgtmK2K9cIlxuICAgICAgICAgICAgICAgIC8+XG5cbiAgICAgICAgICAgICAgICB7Lyog2KfZhNmF2YbYtdin2Kog2KfZhNmF2LPYqtmH2K/ZgdipICovfVxuICAgICAgICAgICAgICAgIDxBZHZhbmNlZE9wdGlvbnNTZWxlY3RvclxuICAgICAgICAgICAgICAgICAgdGl0bGU9XCJUYXJnZXQgUGxhdGZvcm1zXCJcbiAgICAgICAgICAgICAgICAgIHRpdGxlQXI9XCLYp9mE2YXZhti12KfYqiDYp9mE2YXYs9iq2YfYr9mB2KlcIlxuICAgICAgICAgICAgICAgICAgb3B0aW9ucz17VEFSR0VUX1BMQVRGT1JNU31cbiAgICAgICAgICAgICAgICAgIHNlbGVjdGVkVmFsdWVzPXtwcm9qZWN0RGVmaW5pdGlvbi50YXJnZXRQbGF0Zm9ybXMgfHwgW119XG4gICAgICAgICAgICAgICAgICBvblNlbGVjdGlvbkNoYW5nZT17KHZhbHVlcykgPT4gaGFuZGxlUHJvamVjdEFycmF5RmllbGRDaGFuZ2UoJ3RhcmdldFBsYXRmb3JtcycsIHZhbHVlcyl9XG4gICAgICAgICAgICAgICAgICBtdWx0aVNlbGVjdD17dHJ1ZX1cbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiU2VsZWN0IHRhcmdldCBwbGF0Zm9ybXNcIlxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXJBcj1cItin2K7YqtixINin2YTZhdmG2LXYp9iqINin2YTZhdiz2KrZh9iv2YHYqVwiXG4gICAgICAgICAgICAgICAgLz5cblxuICAgICAgICAgICAgICAgIHsvKiDZhNi62KfYqiDYp9mE2KjYsdmF2KzYqSDYp9mE2KPYs9in2LPZitipICovfVxuICAgICAgICAgICAgICAgIDxBZHZhbmNlZE9wdGlvbnNTZWxlY3RvclxuICAgICAgICAgICAgICAgICAgdGl0bGU9XCJQcmltYXJ5IFByb2dyYW1taW5nIExhbmd1YWdlc1wiXG4gICAgICAgICAgICAgICAgICB0aXRsZUFyPVwi2YTYutin2Kog2KfZhNio2LHZhdis2Kkg2KfZhNij2LPYp9iz2YrYqVwiXG4gICAgICAgICAgICAgICAgICBvcHRpb25zPXtQUk9HUkFNTUlOR19MQU5HVUFHRVN9XG4gICAgICAgICAgICAgICAgICBzZWxlY3RlZFZhbHVlcz17cHJvamVjdERlZmluaXRpb24ucHJpbWFyeUxhbmd1YWdlcyB8fCBbXX1cbiAgICAgICAgICAgICAgICAgIG9uU2VsZWN0aW9uQ2hhbmdlPXsodmFsdWVzKSA9PiBoYW5kbGVQcm9qZWN0QXJyYXlGaWVsZENoYW5nZSgncHJpbWFyeUxhbmd1YWdlcycsIHZhbHVlcyl9XG4gICAgICAgICAgICAgICAgICBtdWx0aVNlbGVjdD17dHJ1ZX1cbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiU2VsZWN0IHByb2dyYW1taW5nIGxhbmd1YWdlc1wiXG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlckFyPVwi2KfYrtiq2LEg2YTYutin2Kog2KfZhNio2LHZhdis2KlcIlxuICAgICAgICAgICAgICAgIC8+XG5cbiAgICAgICAgICAgICAgICB7Lyog2YbZiNi5INin2YTZhti02LEgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtZDpjb2wtc3Bhbi0yXCI+XG4gICAgICAgICAgICAgICAgICA8QWR2YW5jZWRPcHRpb25zU2VsZWN0b3JcbiAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCJEZXBsb3ltZW50IFR5cGVcIlxuICAgICAgICAgICAgICAgICAgICB0aXRsZUFyPVwi2YbZiNi5INin2YTZhti02LFcIlxuICAgICAgICAgICAgICAgICAgICBvcHRpb25zPXtERVBMT1lNRU5UX1RZUEVTfVxuICAgICAgICAgICAgICAgICAgICBzZWxlY3RlZFZhbHVlcz17cHJvamVjdERlZmluaXRpb24uZGVwbG95bWVudFR5cGUgPyBbcHJvamVjdERlZmluaXRpb24uZGVwbG95bWVudFR5cGVdIDogW119XG4gICAgICAgICAgICAgICAgICAgIG9uU2VsZWN0aW9uQ2hhbmdlPXsodmFsdWVzKSA9PiBoYW5kbGVQcm9qZWN0RmllbGRDaGFuZ2UoJ2RlcGxveW1lbnRUeXBlJywgdmFsdWVzWzBdIHx8ICcnKX1cbiAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJTZWxlY3QgZGVwbG95bWVudCB0eXBlXCJcbiAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXJBcj1cItin2K7YqtixINmG2YjYuSDYp9mE2YbYtNixXCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC02XCI+XG4gICAgICAgICAgICAgICAgey8qINmG2YXYtyDYp9mE2YfZhtiv2LPYqSDYp9mE2YXYudmF2KfYsdmK2KkgKi99XG4gICAgICAgICAgICAgICAgPEFkdmFuY2VkT3B0aW9uc1NlbGVjdG9yXG4gICAgICAgICAgICAgICAgICB0aXRsZT1cIkFyY2hpdGVjdHVyZSBQYXR0ZXJuXCJcbiAgICAgICAgICAgICAgICAgIHRpdGxlQXI9XCLZhtmF2Lcg2KfZhNmH2YbYr9iz2Kkg2KfZhNmF2LnZhdin2LHZitipXCJcbiAgICAgICAgICAgICAgICAgIG9wdGlvbnM9e0FSQ0hJVEVDVFVSRV9QQVRURVJOU31cbiAgICAgICAgICAgICAgICAgIHNlbGVjdGVkVmFsdWVzPXt0ZWNobmljYWxMYXllci5hcmNoaXRlY3R1cmVQYXR0ZXJuID8gW3RlY2huaWNhbExheWVyLmFyY2hpdGVjdHVyZVBhdHRlcm5dIDogW119XG4gICAgICAgICAgICAgICAgICBvblNlbGVjdGlvbkNoYW5nZT17KHZhbHVlcykgPT4gaGFuZGxlVGVjaG5pY2FsRmllbGRDaGFuZ2UoJ2FyY2hpdGVjdHVyZVBhdHRlcm4nLCB2YWx1ZXNbMF0gfHwgJycpfVxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJTZWxlY3QgYXJjaGl0ZWN0dXJlIHBhdHRlcm5cIlxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXJBcj1cItin2K7YqtixINmG2YXYtyDYp9mE2YfZhtiv2LPYqSDYp9mE2YXYudmF2KfYsdmK2KlcIlxuICAgICAgICAgICAgICAgIC8+XG5cbiAgICAgICAgICAgICAgICB7Lyog2KfYs9iq2LHYp9iq2YrYrNmK2Kkg2KfZhNiq2YjYs9i5ICovfVxuICAgICAgICAgICAgICAgIDxBZHZhbmNlZE9wdGlvbnNTZWxlY3RvclxuICAgICAgICAgICAgICAgICAgdGl0bGU9XCJTY2FsaW5nIFN0cmF0ZWd5XCJcbiAgICAgICAgICAgICAgICAgIHRpdGxlQXI9XCLYp9iz2KrYsdin2KrZitis2YrYqSDYp9mE2KrZiNiz2LlcIlxuICAgICAgICAgICAgICAgICAgb3B0aW9ucz17U0NBTElOR19TVFJBVEVHSUVTfVxuICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRWYWx1ZXM9e3RlY2huaWNhbExheWVyLnNjYWxpbmdTdHJhdGVneSA/IFt0ZWNobmljYWxMYXllci5zY2FsaW5nU3RyYXRlZ3ldIDogW119XG4gICAgICAgICAgICAgICAgICBvblNlbGVjdGlvbkNoYW5nZT17KHZhbHVlcykgPT4gaGFuZGxlVGVjaG5pY2FsRmllbGRDaGFuZ2UoJ3NjYWxpbmdTdHJhdGVneScsIHZhbHVlc1swXSB8fCAnJyl9XG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlNlbGVjdCBzY2FsaW5nIHN0cmF0ZWd5XCJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyQXI9XCLYp9iu2KrYsSDYp9iz2KrYsdin2KrZitis2YrYqSDYp9mE2KrZiNiz2LlcIlxuICAgICAgICAgICAgICAgIC8+XG5cbiAgICAgICAgICAgICAgICB7Lyog2YXYqti32YTYqNin2Kog2KfZhNij2YXYp9mGICovfVxuICAgICAgICAgICAgICAgIDxBZHZhbmNlZE9wdGlvbnNTZWxlY3RvclxuICAgICAgICAgICAgICAgICAgdGl0bGU9XCJTZWN1cml0eSBSZXF1aXJlbWVudHNcIlxuICAgICAgICAgICAgICAgICAgdGl0bGVBcj1cItmF2KrYt9mE2KjYp9iqINin2YTYo9mF2KfZhlwiXG4gICAgICAgICAgICAgICAgICBvcHRpb25zPXtTRUNVUklUWV9SRVFVSVJFTUVOVFN9XG4gICAgICAgICAgICAgICAgICBzZWxlY3RlZFZhbHVlcz17dGVjaG5pY2FsTGF5ZXIuc2VjdXJpdHlSZXF1aXJlbWVudHMgPyBbdGVjaG5pY2FsTGF5ZXIuc2VjdXJpdHlSZXF1aXJlbWVudHNdIDogW119XG4gICAgICAgICAgICAgICAgICBvblNlbGVjdGlvbkNoYW5nZT17KHZhbHVlcykgPT4gaGFuZGxlVGVjaG5pY2FsRmllbGRDaGFuZ2UoJ3NlY3VyaXR5UmVxdWlyZW1lbnRzJywgdmFsdWVzWzBdIHx8ICcnKX1cbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiU2VsZWN0IHNlY3VyaXR5IHJlcXVpcmVtZW50c1wiXG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlckFyPVwi2KfYrtiq2LEg2YXYqti32YTYqNin2Kog2KfZhNij2YXYp9mGXCJcbiAgICAgICAgICAgICAgICAvPlxuXG4gICAgICAgICAgICAgICAgey8qINij2YfYr9in2YEg2KfZhNij2K/Yp9ihICovfVxuICAgICAgICAgICAgICAgIDxBZHZhbmNlZE9wdGlvbnNTZWxlY3RvclxuICAgICAgICAgICAgICAgICAgdGl0bGU9XCJQZXJmb3JtYW5jZSBUYXJnZXRzXCJcbiAgICAgICAgICAgICAgICAgIHRpdGxlQXI9XCLYo9mH2K/Yp9mBINin2YTYo9iv2KfYoVwiXG4gICAgICAgICAgICAgICAgICBvcHRpb25zPXtQRVJGT1JNQU5DRV9UQVJHRVRTfVxuICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRWYWx1ZXM9e3RlY2huaWNhbExheWVyLnBlcmZvcm1hbmNlVGFyZ2V0cyA/IFt0ZWNobmljYWxMYXllci5wZXJmb3JtYW5jZVRhcmdldHNdIDogW119XG4gICAgICAgICAgICAgICAgICBvblNlbGVjdGlvbkNoYW5nZT17KHZhbHVlcykgPT4gaGFuZGxlVGVjaG5pY2FsRmllbGRDaGFuZ2UoJ3BlcmZvcm1hbmNlVGFyZ2V0cycsIHZhbHVlc1swXSB8fCAnJyl9XG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlNlbGVjdCBwZXJmb3JtYW5jZSB0YXJnZXRzXCJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyQXI9XCLYp9iu2KrYsSDYo9mH2K/Yp9mBINin2YTYo9iv2KfYoVwiXG4gICAgICAgICAgICAgICAgLz5cblxuICAgICAgICAgICAgICAgIHsvKiDYp9it2KrZitin2KzYp9iqINin2YTYqtmD2KfZhdmEICovfVxuICAgICAgICAgICAgICAgIDxBZHZhbmNlZE9wdGlvbnNTZWxlY3RvclxuICAgICAgICAgICAgICAgICAgdGl0bGU9XCJJbnRlZ3JhdGlvbiBOZWVkc1wiXG4gICAgICAgICAgICAgICAgICB0aXRsZUFyPVwi2KfYrdiq2YrYp9is2KfYqiDYp9mE2KrZg9in2YXZhFwiXG4gICAgICAgICAgICAgICAgICBvcHRpb25zPXtJTlRFR1JBVElPTl9ORUVEU31cbiAgICAgICAgICAgICAgICAgIHNlbGVjdGVkVmFsdWVzPXt0ZWNobmljYWxMYXllci5pbnRlZ3JhdGlvbk5lZWRzID8gW3RlY2huaWNhbExheWVyLmludGVncmF0aW9uTmVlZHNdIDogW119XG4gICAgICAgICAgICAgICAgICBvblNlbGVjdGlvbkNoYW5nZT17KHZhbHVlcykgPT4gaGFuZGxlVGVjaG5pY2FsRmllbGRDaGFuZ2UoJ2ludGVncmF0aW9uTmVlZHMnLCB2YWx1ZXNbMF0gfHwgJycpfVxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJTZWxlY3QgaW50ZWdyYXRpb24gbmVlZHNcIlxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXJBcj1cItin2K7YqtixINin2K3YqtmK2KfYrNin2Kog2KfZhNiq2YPYp9mF2YRcIlxuICAgICAgICAgICAgICAgIC8+XG5cbiAgICAgICAgICAgICAgICB7Lyog2KPYr9mI2KfYqiDYp9mE2YXYsdin2YLYqNipICovfVxuICAgICAgICAgICAgICAgIDxBZHZhbmNlZE9wdGlvbnNTZWxlY3RvclxuICAgICAgICAgICAgICAgICAgdGl0bGU9XCJNb25pdG9yaW5nIFRvb2xzXCJcbiAgICAgICAgICAgICAgICAgIHRpdGxlQXI9XCLYo9iv2YjYp9iqINin2YTZhdix2KfZgtio2KlcIlxuICAgICAgICAgICAgICAgICAgb3B0aW9ucz17TU9OSVRPUklOR19UT09MU31cbiAgICAgICAgICAgICAgICAgIHNlbGVjdGVkVmFsdWVzPXt0ZWNobmljYWxMYXllci5tb25pdG9yaW5nVG9vbHMgPyBbdGVjaG5pY2FsTGF5ZXIubW9uaXRvcmluZ1Rvb2xzXSA6IFtdfVxuICAgICAgICAgICAgICAgICAgb25TZWxlY3Rpb25DaGFuZ2U9eyh2YWx1ZXMpID0+IGhhbmRsZVRlY2huaWNhbEZpZWxkQ2hhbmdlKCdtb25pdG9yaW5nVG9vbHMnLCB2YWx1ZXNbMF0gfHwgJycpfVxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJTZWxlY3QgbW9uaXRvcmluZyB0b29sc1wiXG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlckFyPVwi2KfYrtiq2LEg2KPYr9mI2KfYqiDYp9mE2YXYsdin2YLYqNipXCJcbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHsvKiDZhti12YrYrdipINiw2YPZitipICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC02IHAtNCBiZy1ibHVlLTUwIGRhcms6YmctYmx1ZS05MDAvMzAgcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLWJsdWUtMjAwIGRhcms6Ym9yZGVyLWJsdWUtNzAwXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBnYXAtMlwiPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtbGdcIj7wn5KhPC9zcGFuPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtpc0FyYWJpYyA/ICd0ZXh0LXJpZ2h0JyA6ICd0ZXh0LWxlZnQnfT5cbiAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWJsdWUtOTAwIGRhcms6dGV4dC1ibHVlLTEwMCBtYi0xXCI+XG4gICAgICAgICAgICAgICAgICAgIHtpc0FyYWJpYyA/ICfZhti12YrYrdipINiw2YPZitipJyA6ICdTbWFydCBUaXAnfVxuICAgICAgICAgICAgICAgICAgPC9oND5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ibHVlLTcwMCBkYXJrOnRleHQtYmx1ZS0zMDBcIj5cbiAgICAgICAgICAgICAgICAgICAge2lzQXJhYmljIFxuICAgICAgICAgICAgICAgICAgICAgID8gJ9mD2YTZhdinINiy2KfYr9iqINiv2YLYqSDYp9iu2KrZitin2LHYp9iq2YPYjCDZg9in2YbYqiDYp9mC2KrYsdin2K3Yp9iqINin2YTYsNmD2KfYoSDYp9mE2KfYtdi32YbYp9i52Yog2KPZg9ir2LEg2KrYrti12LXYp9mLINmI2YHYp9im2K/YqSDZhNmF2LTYsdmI2LnZgy4nXG4gICAgICAgICAgICAgICAgICAgICAgOiAnVGhlIG1vcmUgcHJlY2lzZSB5b3VyIHNlbGVjdGlvbnMsIHRoZSBtb3JlIHNwZWNpYWxpemVkIGFuZCB1c2VmdWwgQUkgc3VnZ2VzdGlvbnMgd2lsbCBiZSBmb3IgeW91ciBwcm9qZWN0LidcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VDb250ZXh0U3RvcmUiLCJBZHZhbmNlZE9wdGlvbnNTZWxlY3RvciIsIlBST0pFQ1RfVFlQRVMiLCJUQVJHRVRfUExBVEZPUk1TIiwiUFJPR1JBTU1JTkdfTEFOR1VBR0VTIiwiQ09NUExFWElUWV9MRVZFTFMiLCJERVBMT1lNRU5UX1RZUEVTIiwiR0VPR1JBUEhJQ19SRUdJT05TIiwiUFJPSkVDVF9OQVRVUkUiLCJBUkNISVRFQ1RVUkVfUEFUVEVSTlMiLCJTQ0FMSU5HX1NUUkFURUdJRVMiLCJTRUNVUklUWV9SRVFVSVJFTUVOVFMiLCJQRVJGT1JNQU5DRV9UQVJHRVRTIiwiSU5URUdSQVRJT05fTkVFRFMiLCJNT05JVE9SSU5HX1RPT0xTIiwiQ2hldnJvbkRvd24iLCJDaGV2cm9uVXAiLCJTcGFya2xlcyIsIkFkdmFuY2VkT3B0aW9uc1BhbmVsIiwibW9kdWxlVHlwZSIsInByb2plY3REZWZpbml0aW9uIiwidGVjaG5pY2FsTGF5ZXIiLCJ1cGRhdGVQcm9qZWN0RGVmaW5pdGlvbiIsInVwZGF0ZVRlY2huaWNhbExheWVyIiwic2hvd0FkdmFuY2VkT3B0aW9ucyIsImN1cnJlbnRMYW5ndWFnZSIsImlzRXhwYW5kZWQiLCJzZXRJc0V4cGFuZGVkIiwiaXNBcmFiaWMiLCJoYW5kbGVQcm9qZWN0RmllbGRDaGFuZ2UiLCJmaWVsZCIsInZhbHVlIiwiaGFuZGxlUHJvamVjdEFycmF5RmllbGRDaGFuZ2UiLCJ2YWx1ZXMiLCJoYW5kbGVUZWNobmljYWxGaWVsZENoYW5nZSIsImdldFRpdGxlIiwiZ2V0SWNvbiIsImRpdiIsImNsYXNzTmFtZSIsIm9uQ2xpY2siLCJzcGFuIiwiaDMiLCJwIiwidGl0bGUiLCJ0aXRsZUFyIiwib3B0aW9ucyIsInNlbGVjdGVkVmFsdWVzIiwicHJvamVjdFR5cGUiLCJvblNlbGVjdGlvbkNoYW5nZSIsInBsYWNlaG9sZGVyIiwicGxhY2Vob2xkZXJBciIsInByb2plY3ROYXR1cmUiLCJnZW9ncmFwaGljUmVnaW9uIiwiY29tcGxleGl0eSIsInRhcmdldFBsYXRmb3JtcyIsIm11bHRpU2VsZWN0IiwicHJpbWFyeUxhbmd1YWdlcyIsImRlcGxveW1lbnRUeXBlIiwiYXJjaGl0ZWN0dXJlUGF0dGVybiIsInNjYWxpbmdTdHJhdGVneSIsInNlY3VyaXR5UmVxdWlyZW1lbnRzIiwicGVyZm9ybWFuY2VUYXJnZXRzIiwiaW50ZWdyYXRpb25OZWVkcyIsIm1vbml0b3JpbmdUb29scyIsImg0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AdvancedOptionsPanel.tsx\n"));

/***/ })

});