'use client';

import { useState } from 'react';
import { useContextStore } from '@/store/contextStore';

interface OutputPanelProps {
  moduleData: any;
  moduleName: string;
  moduleNameAr: string;
}

export default function OutputPanel({ moduleData, moduleName, moduleNameAr }: OutputPanelProps) {
  const { currentLanguage, outputFormat, setOutputFormat } = useContextStore();
  const [copied, setCopied] = useState(false);
  const isArabic = currentLanguage === 'ar';

  // تحويل البيانات إلى تنسيقات مختلفة
  const generateMarkdown = () => {
    const title = isArabic ? moduleNameAr : moduleName;
    let markdown = `# ${title}\n\n`;
    
    Object.entries(moduleData).forEach(([key, value]) => {
      if (value && typeof value === 'string' && value.trim()) {
        const formattedKey = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
        markdown += `## ${formattedKey}\n${value}\n\n`;
      }
    });
    
    return markdown;
  };

  const generateHTML = () => {
    const title = isArabic ? moduleNameAr : moduleName;
    let html = `<div class="module-output">\n  <h1>${title}</h1>\n`;
    
    Object.entries(moduleData).forEach(([key, value]) => {
      if (value && typeof value === 'string' && value.trim()) {
        const formattedKey = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
        html += `  <section>\n    <h2>${formattedKey}</h2>\n    <p>${value}</p>\n  </section>\n`;
      }
    });
    
    html += '</div>';
    return html;
  };

  const generateJSON = () => {
    const filteredData = Object.fromEntries(
      Object.entries(moduleData).filter(([_, value]) =>
        value && typeof value === 'string' && value.trim()
      )
    );

    return JSON.stringify({
      module: isArabic ? moduleNameAr : moduleName,
      data: filteredData,
      metadata: {
        timestamp: new Date().toISOString(),
        language: isArabic ? 'ar' : 'en',
        version: '1.0'
      }
    }, null, 2);
  };

  const generateYAML = () => {
    const filteredData = Object.fromEntries(
      Object.entries(moduleData).filter(([_, value]) =>
        value && typeof value === 'string' && value.trim()
      )
    );

    let yaml = `# ${isArabic ? moduleNameAr : moduleName}\n`;
    yaml += `# Generated: ${new Date().toISOString()}\n\n`;

    Object.entries(filteredData).forEach(([key, value]) => {
      const formattedKey = key.replace(/([A-Z])/g, '_$1').toLowerCase();
      yaml += `${formattedKey}: |\n`;
      const lines = String(value || '').split('\n');
      lines.forEach(line => {
        yaml += `  ${line}\n`;
      });
      yaml += '\n';
    });

    return yaml;
  };

  const getCurrentOutput = () => {
    switch (outputFormat) {
      case 'markdown': return generateMarkdown();
      case 'html': return generateHTML();
      case 'json': return generateJSON();
      case 'yaml': return generateYAML();
      default: return generateMarkdown();
    }
  };

  const handleCopyAll = async () => {
    const output = getCurrentOutput();
    await navigator.clipboard.writeText(output);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const handleDownload = () => {
    const output = getCurrentOutput();
    const extensions = {
      markdown: 'md',
      html: 'html',
      json: 'json',
      yaml: 'yml'
    };
    const extension = extensions[outputFormat as keyof typeof extensions] || 'txt';
    const filename = `${moduleName.toLowerCase().replace(/\s+/g, '-')}.${extension}`;

    const mimeTypes = {
      markdown: 'text/markdown',
      html: 'text/html',
      json: 'application/json',
      yaml: 'text/yaml'
    };
    const mimeType = mimeTypes[outputFormat as keyof typeof mimeTypes] || 'text/plain';

    const blob = new Blob([output], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const hasData = Object.values(moduleData).some(value => 
    value && typeof value === 'string' && value.trim()
  );

  return (
    <div className="space-y-4" dir={isArabic ? 'rtl' : 'ltr'}>
      {/* Format Selector and Action Buttons */}
      <div className="space-y-4">
        {/* أزرار التنسيق */}
        <div className={`flex items-center ${isArabic ? 'justify-end' : 'justify-start'}`}>
          <div className={`flex flex-wrap gap-3`}>
            {(['markdown', 'html', 'json', 'yaml'] as const).map((format) => (
              <button
                key={format}
                onClick={() => setOutputFormat(format as any)}
                className={`group relative px-6 py-3 text-sm font-medium rounded-xl transition-all duration-300 ease-in-out backdrop-blur-md border overflow-hidden ${
                  outputFormat === format
                    ? 'border-blue-400/50 bg-gradient-to-br from-blue-500/80 via-indigo-500/80 to-purple-600/80 text-white shadow-lg shadow-blue-500/25 scale-105'
                    : 'border-gray-300/50 dark:border-gray-600/50 bg-white/60 dark:bg-gray-800/60 text-gray-700 dark:text-gray-300 hover:bg-white/80 dark:hover:bg-gray-700/80 hover:border-blue-300/50 dark:hover:border-blue-500/50 hover:scale-105 hover:shadow-md'
                }`}
              >
                {/* تأثير الإضاءة */}
                <div className="absolute inset-0 bg-gradient-to-r from-white/20 via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                {/* المحتوى */}
                <span className="relative font-arabic">
                  {format.toUpperCase()}
                </span>

                {/* تأثير الشرارات للزر النشط */}
                {outputFormat === format && (
                  <div className="absolute top-0 left-0 w-full h-full">
                    <div className="absolute top-1 left-2 w-1 h-1 bg-white rounded-full opacity-0 group-hover:opacity-100 animate-ping"></div>
                    <div className="absolute top-2 right-3 w-1 h-1 bg-white rounded-full opacity-0 group-hover:opacity-100 animate-ping" style={{animationDelay: '0.2s'}}></div>
                    <div className="absolute bottom-1 left-4 w-1 h-1 bg-white rounded-full opacity-0 group-hover:opacity-100 animate-ping" style={{animationDelay: '0.4s'}}></div>
                  </div>
                )}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Output Display */}
      <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4 min-h-[300px]">
        {hasData ? (
          <pre className={`text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap font-mono overflow-x-auto ${
            isArabic ? 'text-right' : 'text-left'
          }`} dir={isArabic ? 'rtl' : 'ltr'}>
            {getCurrentOutput()}
          </pre>
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500 dark:text-gray-400">
            <div className="text-center" dir={isArabic ? 'rtl' : 'ltr'}>
              <span className="text-4xl mb-2 block">📝</span>
              <p>{isArabic ? 'ابدأ بالإجابة على الأسئلة لرؤية المخرجات' : 'Start answering questions to see outputs'}</p>
            </div>
          </div>
        )}
      </div>

      {/* Format Info */}
      <div className={`text-xs text-gray-500 dark:text-gray-400 ${isArabic ? 'text-right' : 'text-left'}`} dir={isArabic ? 'rtl' : 'ltr'}>
        {outputFormat === 'markdown' && (isArabic ? 'تنسيق Markdown - جاهز للاستخدام في المستندات' : 'Markdown format - Ready for documentation')}
        {outputFormat === 'html' && (isArabic ? 'تنسيق HTML - جاهز للمواقع الإلكترونية' : 'HTML format - Ready for websites')}
        {outputFormat === 'json' && (isArabic ? 'تنسيق JSON - جاهز للبرمجة والـ APIs' : 'JSON format - Ready for programming and APIs')}
        {outputFormat === 'yaml' && (isArabic ? 'تنسيق YAML - جاهز للتكوين والنشر' : 'YAML format - Ready for configuration and deployment')}
      </div>

      {/* أزرار النسخ والتحميل في أقصى أسفل المحتوى */}
      {hasData && (
        <div className={`flex gap-3 mt-4 ${isArabic ? 'justify-start' : 'justify-end'}`}>
          <button
            onClick={handleCopyAll}
            className="px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-all duration-200 hover:scale-105 shadow-sm hover:shadow-md font-arabic"
          >
            {copied ? (isArabic ? 'تم النسخ!' : 'Copied!') : (isArabic ? 'نسخ' : 'Copy')}
          </button>

          <button
            onClick={handleDownload}
            className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-all duration-200 hover:scale-105 shadow-sm hover:shadow-md font-arabic"
          >
            {isArabic ? 'تحميل' : 'Download'}
          </button>
        </div>
      )}
    </div>
  );
}
