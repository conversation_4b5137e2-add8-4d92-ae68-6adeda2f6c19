"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/settings/page",{

/***/ "(app-pages-browser)/./src/app/settings/page.tsx":
/*!***********************************!*\
  !*** ./src/app/settings/page.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SettingsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* harmony import */ var _lib_llmProviders__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/llmProviders */ \"(app-pages-browser)/./src/lib/llmProviders.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/test-tube.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ThemeToggle__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ThemeToggle */ \"(app-pages-browser)/./src/components/ThemeToggle.tsx\");\n/* harmony import */ var _components_LanguageToggle__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/LanguageToggle */ \"(app-pages-browser)/./src/components/LanguageToggle.tsx\");\n/* harmony import */ var _components_TestAIGeneration__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/TestAIGeneration */ \"(app-pages-browser)/./src/components/TestAIGeneration.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction SettingsPage() {\n    _s();\n    const { currentLanguage, apiSettings, showAdvancedOptions, setShowAdvancedOptions, addProvider, updateProvider, removeProvider, validateProvider, getProvider } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_3__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    const [showKeys, setShowKeys] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [validationStates, setValidationStates] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [showAddProvider, setShowAddProvider] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [selectedProviderId, setSelectedProviderId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [errorMessage, setErrorMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [expandedProviders, setExpandedProviders] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [selectedModels, setSelectedModels] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [newCustomModel, setNewCustomModel] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    // تهيئة النماذج المحددة عند تحميل الصفحة\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const configuredProviders = apiSettings.providers || [];\n        const initialModels = {};\n        configuredProviders.forEach((provider)=>{\n            initialModels[provider.id] = provider.selectedModels || [];\n        });\n        setSelectedModels(initialModels);\n    }, [\n        apiSettings.providers\n    ]);\n    const translations = {\n        title: isArabic ? \"إعدادات نماذج الذكاء الاصطناعي\" : \"LLM API Settings\",\n        subtitle: isArabic ? \"قم بتكوين مفاتيح واجهة برمجة التطبيقات ونماذج الذكاء الاصطناعي\" : \"Configure your API keys and AI models\",\n        providers: isArabic ? \"مقدمو خدمات الذكاء الاصطناعي\" : \"LLM Providers\",\n        addProvider: isArabic ? \"إضافة مقدم خدمة جديد\" : \"Add Provider\",\n        apiKey: isArabic ? \"مفتاح واجهة برمجة التطبيقات\" : \"API Key\",\n        baseUrl: isArabic ? \"الرابط الأساسي للخدمة\" : \"Base URL\",\n        testConnection: isArabic ? \"اختبار الاتصال\" : \"Test Connection\",\n        validating: isArabic ? \"جاري التحقق من الاتصال...\" : \"Validating...\",\n        valid: isArabic ? \"الاتصال صحيح\" : \"Valid\",\n        invalid: isArabic ? \"الاتصال غير صحيح\" : \"Invalid\",\n        error: isArabic ? \"حدث خطأ في الاتصال\" : \"Error\",\n        models: isArabic ? \"النماذج المتوفرة\" : \"Available Models\",\n        selectedModels: isArabic ? \"النماذج المختارة\" : \"Selected Models\",\n        addCustomModel: isArabic ? \"إضافة نموذج مخصص\" : \"Add Custom Model\",\n        customModelName: isArabic ? \"اسم النموذج المخصص\" : \"Custom Model Name\",\n        editModels: isArabic ? \"تحرير النماذج\" : \"Edit Models\",\n        saveModels: isArabic ? \"حفظ التغييرات\" : \"Save Models\",\n        noModelsSelected: isArabic ? \"لم يتم اختيار أي نماذج بعد\" : \"No models selected\",\n        cancel: isArabic ? \"إلغاء العملية\" : \"Cancel\",\n        add: isArabic ? \"إضافة\" : \"Add\",\n        backToHome: isArabic ? \"العودة إلى الصفحة الرئيسية\" : \"Back to Home\",\n        active: isArabic ? \"مفعل\" : \"Active\",\n        selectProvider: isArabic ? \"اختر مقدم الخدمة\" : \"Select Provider\",\n        noProvidersConfigured: isArabic ? \"لم يتم تكوين أي مقدم خدمة حتى الآن\" : \"No providers configured yet\",\n        providerAlreadyExists: isArabic ? \"مقدم الخدمة موجود مسبقاً\" : \"Provider already exists\",\n        pleaseSelectProvider: isArabic ? \"يرجى اختيار مقدم خدمة من القائمة\" : \"Please select a provider\",\n        providerNotFound: isArabic ? \"لم يتم العثور على مقدم الخدمة\" : \"Provider not found\",\n        errorAddingProvider: isArabic ? \"حدث خطأ أثناء إضافة مقدم الخدمة\" : \"Error adding provider\",\n        // إعدادات عامة\n        generalSettings: isArabic ? \"الإعدادات العامة\" : \"General Settings\",\n        advancedOptions: isArabic ? \"الخيارات المتقدمة\" : \"Advanced Options\",\n        showAdvancedOptions: isArabic ? \"إظهار الخيارات المتقدمة\" : \"Show Advanced Options\",\n        advancedOptionsDescription: isArabic ? \"عرض خيارات التخصيص المتقدمة في صفحات المشروع\" : \"Display advanced customization options in project pages\"\n    };\n    // دوال إدارة النماذج\n    const toggleModelSelection = (providerId, modelId)=>{\n        setSelectedModels((prev)=>{\n            const currentModels = prev[providerId] || [];\n            const isSelected = currentModels.includes(modelId);\n            return {\n                ...prev,\n                [providerId]: isSelected ? currentModels.filter((id)=>id !== modelId) : [\n                    ...currentModels,\n                    modelId\n                ]\n            };\n        });\n    };\n    const addCustomModel = (providerId)=>{\n        if (newCustomModel.trim()) {\n            setSelectedModels((prev)=>{\n                const currentModels = prev[providerId] || [];\n                return {\n                    ...prev,\n                    [providerId]: [\n                        ...currentModels,\n                        newCustomModel.trim()\n                    ]\n                };\n            });\n            setNewCustomModel(\"\");\n        }\n    };\n    const removeCustomModel = (providerId, modelId)=>{\n        setSelectedModels((prev)=>{\n            const currentModels = prev[providerId] || [];\n            return {\n                ...prev,\n                [providerId]: currentModels.filter((id)=>id !== modelId)\n            };\n        });\n    };\n    const saveProviderModels = (providerId)=>{\n        const models = selectedModels[providerId] || [];\n        updateProvider(providerId, {\n            selectedModels: models\n        });\n        setExpandedProviders((prev)=>({\n                ...prev,\n                [providerId]: false\n            }));\n    };\n    const handleAddProvider = async ()=>{\n        setErrorMessage(\"\");\n        if (!selectedProviderId) {\n            setErrorMessage(translations.pleaseSelectProvider);\n            return;\n        }\n        const providerTemplate = (0,_lib_llmProviders__WEBPACK_IMPORTED_MODULE_4__.getProviderById)(selectedProviderId);\n        if (!providerTemplate) {\n            setErrorMessage(translations.providerNotFound);\n            return;\n        }\n        const existingProvider = getProvider(selectedProviderId);\n        if (existingProvider) {\n            setErrorMessage(translations.providerAlreadyExists);\n            setShowAddProvider(false);\n            setSelectedProviderId(\"\");\n            return;\n        }\n        try {\n            const newProvider = {\n                id: selectedProviderId,\n                apiKey: \"\",\n                selectedModels: [],\n                isEnabled: false,\n                validationStatus: \"pending\",\n                priority: 1,\n                isBackup: false\n            };\n            addProvider(newProvider);\n            setShowAddProvider(false);\n            setSelectedProviderId(\"\");\n            setErrorMessage(\"\");\n        } catch (error) {\n            console.error(\"Error adding provider:\", error);\n            setErrorMessage(translations.errorAddingProvider);\n        }\n    };\n    const handleValidateProvider = async (providerId)=>{\n        setValidationStates((prev)=>({\n                ...prev,\n                [providerId]: {\n                    status: \"validating\"\n                }\n            }));\n        try {\n            const isValid = await validateProvider(providerId);\n            setValidationStates((prev)=>({\n                    ...prev,\n                    [providerId]: {\n                        status: isValid ? \"valid\" : \"invalid\",\n                        message: isValid ? translations.valid : translations.invalid,\n                        lastValidated: new Date()\n                    }\n                }));\n        } catch (error) {\n            setValidationStates((prev)=>({\n                    ...prev,\n                    [providerId]: {\n                        status: \"error\",\n                        message: error instanceof Error ? error.message : translations.error\n                    }\n                }));\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"validating\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"w-4 h-4 animate-spin text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 221,\n                    columnNumber: 16\n                }, this);\n            case \"valid\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"w-4 h-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 16\n                }, this);\n            case \"invalid\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"w-4 h-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 16\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"w-4 h-4 text-orange-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"w-4 h-4 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const configuredProviders = apiSettings.providers || [];\n    const availableProviders = _lib_llmProviders__WEBPACK_IMPORTED_MODULE_4__.LLM_PROVIDERS_DATABASE.filter((p)=>!configuredProviders.some((cp)=>cp.id === p.id));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        dir: isArabic ? \"rtl\" : \"ltr\",\n        className: \"jsx-2abf06658c86e3df\" + \" \" + \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"2abf06658c86e3df\",\n                children: '[dir=\"rtl\"].jsx-2abf06658c86e3df{font-family:\"Tajawal\",\"Arial\",sans-serif}[dir=\"rtl\"].jsx-2abf06658c86e3df *.jsx-2abf06658c86e3df{font-family:\"Tajawal\",\"Arial\",sans-serif;text-align:right}[dir=\"rtl\"].jsx-2abf06658c86e3df .page-container.jsx-2abf06658c86e3df{direction:rtl}[dir=\"rtl\"].jsx-2abf06658c86e3df .header-section.jsx-2abf06658c86e3df{direction:rtl}[dir=\"rtl\"].jsx-2abf06658c86e3df .header-content.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between}[dir=\"rtl\"].jsx-2abf06658c86e3df .header-left.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse}[dir=\"rtl\"].jsx-2abf06658c86e3df .header-right.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse}[dir=\"rtl\"].jsx-2abf06658c86e3df .section-header.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between}[dir=\"rtl\"].jsx-2abf06658c86e3df .provider-card.jsx-2abf06658c86e3df{direction:rtl}[dir=\"rtl\"].jsx-2abf06658c86e3df .provider-header.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between}[dir=\"rtl\"].jsx-2abf06658c86e3df .provider-info.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse;text-align:right}[dir=\"rtl\"].jsx-2abf06658c86e3df .provider-controls.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse}[dir=\"rtl\"].jsx-2abf06658c86e3df .form-section.jsx-2abf06658c86e3df{direction:rtl}[dir=\"rtl\"].jsx-2abf06658c86e3df .form-row.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse}[dir=\"rtl\"].jsx-2abf06658c86e3df .models-section.jsx-2abf06658c86e3df{direction:rtl}[dir=\"rtl\"].jsx-2abf06658c86e3df .models-header.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between}[dir=\"rtl\"].jsx-2abf06658c86e3df .model-item.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse}[dir=\"rtl\"].jsx-2abf06658c86e3df .model-tags.jsx-2abf06658c86e3df{-webkit-box-pack:end;-webkit-justify-content:flex-end;-moz-box-pack:end;-ms-flex-pack:end;justify-content:flex-end}[dir=\"rtl\"].jsx-2abf06658c86e3df input[type=\"text\"].jsx-2abf06658c86e3df,[dir=\"rtl\"].jsx-2abf06658c86e3df input[type=\"password\"].jsx-2abf06658c86e3df,[dir=\"rtl\"].jsx-2abf06658c86e3df select.jsx-2abf06658c86e3df,[dir=\"rtl\"].jsx-2abf06658c86e3df textarea.jsx-2abf06658c86e3df{text-align:right;direction:rtl;font-family:\"Tajawal\",\"Arial\",sans-serif}[dir=\"rtl\"].jsx-2abf06658c86e3df .button-group.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse}[dir=\"rtl\"].jsx-2abf06658c86e3df .button-with-icon.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse}[dir=\"rtl\"].jsx-2abf06658c86e3df .modal-content.jsx-2abf06658c86e3df{direction:rtl;text-align:right}[dir=\"rtl\"].jsx-2abf06658c86e3df .modal-buttons.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse}[dir=\"rtl\"].jsx-2abf06658c86e3df .text-content.jsx-2abf06658c86e3df{text-align:right;direction:rtl}[dir=\"rtl\"].jsx-2abf06658c86e3df .font-arabic.jsx-2abf06658c86e3df{font-family:\"Tajawal\",\"Arial\",sans-serif;text-align:right}'\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-2abf06658c86e3df\" + \" \" + \"header-section bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"header-content flex items-center justify-between h-16 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"header-left flex items-center gap-4 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        href: \"/\",\n                                        className: \"flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                        children: [\n                                            isArabic ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-5 h-5 rotate-180\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"font-arabic text-content\",\n                                                children: translations.backToHome\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex items-center gap-3 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"w-6 h-6 text-blue-600 dark:text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-content\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-xl font-bold text-gray-900 dark:text-white font-arabic\",\n                                                        children: translations.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm text-gray-600 dark:text-gray-400 font-arabic\",\n                                                        children: translations.subtitle\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"header-right flex items-center gap-3 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LanguageToggle__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeToggle__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 368,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 367,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 366,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-2abf06658c86e3df\" + \" \" + \"page-container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"p-6 border-b border-gray-200 dark:border-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-lg font-semibold text-gray-900 dark:text-white font-arabic text-content\",\n                                        children: translations.generalSettings\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 413,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"space-y-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex items-center justify-between \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex-1 \".concat(isArabic ? \"text-right ml-4\" : \"text-left mr-4\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm font-medium text-gray-900 dark:text-white font-arabic\",\n                                                            children: translations.showAdvancedOptions\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 423,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm text-gray-500 dark:text-gray-400 mt-1 font-arabic\",\n                                                            children: translations.advancedOptionsDescription\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 426,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowAdvancedOptions(!showAdvancedOptions),\n                                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 \".concat(showAdvancedOptions ? \"bg-blue-600\" : \"bg-gray-200 dark:bg-gray-700\", \" \").concat(isArabic ? \"transform scale-x-[-1]\" : \"\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"inline-block h-4 w-4 transform rounded-full bg-white transition-transform \".concat(showAdvancedOptions ? \"translate-x-6\" : \"translate-x-1\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 437,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 431,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 418,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 412,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"p-6 border-b border-gray-200 dark:border-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"section-header flex items-center justify-between \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-lg font-semibold text-gray-900 dark:text-white font-arabic text-content\",\n                                                children: translations.providers\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 453,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    setShowAddProvider(true);\n                                                    setErrorMessage(\"\");\n                                                    setSelectedProviderId(\"\");\n                                                },\n                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"button-with-icon flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-arabic \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 464,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-content\",\n                                                        children: translations.addProvider\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 465,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 452,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"p-6 space-y-4\",\n                                    children: configuredProviders.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-gray-500 dark:text-gray-400 font-arabic text-content\",\n                                                children: translations.noProvidersConfigured\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 17\n                                    }, this) : configuredProviders.map((provider)=>{\n                                        const providerInfo = (0,_lib_llmProviders__WEBPACK_IMPORTED_MODULE_4__.getProviderById)(provider.id);\n                                        const validationState = validationStates[provider.id];\n                                        if (!providerInfo) return null;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"provider-card border border-gray-200 dark:border-gray-600 rounded-lg p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"provider-header flex items-start justify-between mb-6 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"provider-info flex items-center gap-4 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-2xl\",\n                                                                    children: providerInfo.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 489,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"space-y-1 text-content\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"font-semibold text-gray-900 dark:text-white font-arabic\",\n                                                                            children: providerInfo.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 491,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm text-gray-600 dark:text-gray-400 font-arabic\",\n                                                                            children: providerInfo.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 494,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 490,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 488,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"provider-controls flex items-center gap-3 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                            children: [\n                                                                getStatusIcon((validationState === null || validationState === void 0 ? void 0 : validationState.status) || \"idle\"),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex items-center gap-2 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: provider.isEnabled,\n                                                                            onChange: (e)=>updateProvider(provider.id, {\n                                                                                    isEnabled: e.target.checked\n                                                                                }),\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 505,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm text-gray-600 dark:text-gray-400 font-arabic whitespace-nowrap text-content\",\n                                                                            children: translations.active\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 511,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 504,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>removeProvider(provider.id),\n                                                                    title: isArabic ? \"حذف مقدم الخدمة\" : \"Remove Provider\",\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"p-2 text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-colors\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 521,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 516,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 500,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 487,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"form-section space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"grid grid-cols-1 lg:grid-cols-2 gap-4 items-end\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex flex-col\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 font-arabic text-content\",\n                                                                            children: translations.apiKey\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 529,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"relative\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: showKeys[provider.id] ? \"text\" : \"password\",\n                                                                                    value: provider.apiKey,\n                                                                                    onChange: (e)=>updateProvider(provider.id, {\n                                                                                            apiKey: e.target.value\n                                                                                        }),\n                                                                                    placeholder: providerInfo.apiKeyPlaceholder,\n                                                                                    dir: isArabic ? \"rtl\" : \"ltr\",\n                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"w-full h-10 px-3 py-2 \".concat(isArabic ? \"pr-10 pl-3\" : \"pr-10 pl-3\", \" border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent font-arabic\")\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 533,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>setShowKeys((prev)=>({\n                                                                                                ...prev,\n                                                                                                [provider.id]: !prev[provider.id]\n                                                                                            })),\n                                                                                    title: isArabic ? showKeys[provider.id] ? \"إخفاء المفتاح\" : \"إظهار المفتاح\" : showKeys[provider.id] ? \"Hide key\" : \"Show key\",\n                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"absolute \".concat(isArabic ? \"left-3\" : \"right-3\", \" top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\"),\n                                                                                    children: showKeys[provider.id] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 546,\n                                                                                        columnNumber: 58\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 546,\n                                                                                        columnNumber: 91\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 541,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 532,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 528,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex flex-col\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 font-arabic text-content\",\n                                                                            children: translations.baseUrl\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 552,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: provider.baseUrl || providerInfo.baseUrl,\n                                                                            onChange: (e)=>updateProvider(provider.id, {\n                                                                                    baseUrl: e.target.value\n                                                                                }),\n                                                                            placeholder: providerInfo.baseUrl,\n                                                                            dir: isArabic ? \"rtl\" : \"ltr\",\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"w-full h-10 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent font-arabic\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 555,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 551,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 527,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"grid grid-cols-1 lg:grid-cols-2 gap-4 mt-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex \".concat(isArabic ? \"justify-start\" : \"justify-start\"),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleValidateProvider(provider.id),\n                                                                        disabled: !provider.apiKey || (validationState === null || validationState === void 0 ? void 0 : validationState.status) === \"validating\",\n                                                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"button-with-icon flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-arabic \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                className: \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 574,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-content\",\n                                                                                children: (validationState === null || validationState === void 0 ? void 0 : validationState.status) === \"validating\" ? translations.validating : translations.testConnection\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 575,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 569,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 568,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex \".concat(isArabic ? \"justify-end\" : \"justify-end\"),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm text-gray-600 dark:text-gray-400 font-arabic text-content\",\n                                                                        children: [\n                                                                            translations.models,\n                                                                            \": \",\n                                                                            providerInfo.models.length\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 583,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 582,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 566,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        (validationState === null || validationState === void 0 ? void 0 : validationState.message) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"mt-2 p-2 rounded text-sm text-content \".concat(validationState.status === \"valid\" ? \"bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300\" : \"bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300\"),\n                                                            children: validationState.message\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 590,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"models-section mt-4 border-t border-gray-200 dark:border-gray-600 pt-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"grid grid-cols-1 lg:grid-cols-2 gap-4 mb-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex \".concat(isArabic ? \"justify-start\" : \"justify-start\"),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm font-medium text-gray-900 dark:text-white font-arabic text-content\",\n                                                                                children: translations.selectedModels\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 604,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 603,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex \".concat(isArabic ? \"justify-end\" : \"justify-end\"),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>{\n                                                                                    setExpandedProviders((prev)=>({\n                                                                                            ...prev,\n                                                                                            [provider.id]: !prev[provider.id]\n                                                                                        }));\n                                                                                    if (!selectedModels[provider.id]) {\n                                                                                        setSelectedModels((prev)=>({\n                                                                                                ...prev,\n                                                                                                [provider.id]: provider.selectedModels || []\n                                                                                            }));\n                                                                                    }\n                                                                                },\n                                                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm text-blue-600 dark:text-blue-400 hover:underline font-arabic text-content\",\n                                                                                children: expandedProviders[provider.id] ? translations.saveModels : translations.editModels\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 611,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 610,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 601,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"mb-3\",\n                                                                    children: (provider.selectedModels || []).length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm text-gray-500 dark:text-gray-400 font-arabic text-content\",\n                                                                        children: translations.noModelsSelected\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 628,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"model-tags flex flex-wrap gap-2 \".concat(isArabic ? \"justify-end\" : \"justify-start\"),\n                                                                        children: (provider.selectedModels || []).map((modelId)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded text-xs font-arabic text-content\",\n                                                                                children: modelId\n                                                                            }, modelId, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 634,\n                                                                                columnNumber: 35\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 632,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 626,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                expandedProviders[provider.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"space-y-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-2abf06658c86e3df\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm font-medium text-gray-900 dark:text-white mb-2 font-arabic text-content\",\n                                                                                    children: translations.models\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 650,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"grid grid-cols-1 md:grid-cols-2 gap-2 max-h-40 overflow-y-auto\",\n                                                                                    children: providerInfo.models.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"model-item flex items-center gap-2 p-2 hover:bg-gray-100 dark:hover:bg-gray-600 rounded cursor-pointer \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                    type: \"checkbox\",\n                                                                                                    checked: (selectedModels[provider.id] || []).includes(model.id),\n                                                                                                    onChange: ()=>toggleModelSelection(provider.id, model.id),\n                                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"rounded border-gray-300 text-blue-600 focus:ring-blue-500 flex-shrink-0\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                    lineNumber: 659,\n                                                                                                    columnNumber: 39\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex-1 min-w-0 text-content\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm font-medium text-gray-900 dark:text-white truncate font-arabic\",\n                                                                                                            children: model.name\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                            lineNumber: 666,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-xs text-gray-500 dark:text-gray-400 truncate font-arabic\",\n                                                                                                            children: model.description\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                            lineNumber: 669,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                    lineNumber: 665,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, model.id, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 655,\n                                                                                            columnNumber: 37\n                                                                                        }, this))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 653,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 649,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-2abf06658c86e3df\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm font-medium text-gray-900 dark:text-white mb-2 font-arabic text-content\",\n                                                                                    children: translations.addCustomModel\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 680,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex gap-2 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"text\",\n                                                                                            value: newCustomModel,\n                                                                                            onChange: (e)=>setNewCustomModel(e.target.value),\n                                                                                            placeholder: translations.customModelName,\n                                                                                            onKeyDown: (e)=>e.key === \"Enter\" && addCustomModel(provider.id),\n                                                                                            dir: isArabic ? \"rtl\" : \"ltr\",\n                                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm font-arabic\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 684,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                            onClick: ()=>addCustomModel(provider.id),\n                                                                                            disabled: !newCustomModel.trim(),\n                                                                                            title: isArabic ? \"إضافة النموذج\" : \"Add Model\",\n                                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm flex-shrink-0\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                                className: \"w-4 h-4\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                lineNumber: 699,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 693,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 683,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 679,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        (selectedModels[provider.id] || []).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-2abf06658c86e3df\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm font-medium text-gray-900 dark:text-white mb-2 font-arabic text-content\",\n                                                                                    children: translations.selectedModels\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 707,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"model-tags flex flex-wrap gap-2 \".concat(isArabic ? \"justify-end\" : \"justify-start\"),\n                                                                                    children: (selectedModels[provider.id] || []).map((modelId)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex items-center gap-1 px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded text-xs \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"font-arabic text-content\",\n                                                                                                    children: modelId\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                    lineNumber: 716,\n                                                                                                    columnNumber: 41\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>removeCustomModel(provider.id, modelId),\n                                                                                                    title: isArabic ? \"حذف النموذج\" : \"Remove Model\",\n                                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-blue-600 dark:text-blue-400 hover:text-red-600 dark:hover:text-red-400 flex-shrink-0\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                                        className: \"w-3 h-3\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                        lineNumber: 722,\n                                                                                                        columnNumber: 43\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                    lineNumber: 717,\n                                                                                                    columnNumber: 41\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, modelId, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 712,\n                                                                                            columnNumber: 39\n                                                                                        }, this))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 710,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 706,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"button-group flex \".concat(isArabic ? \"justify-start\" : \"justify-end\"),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>saveProviderModels(provider.id),\n                                                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm font-arabic text-content\",\n                                                                                children: translations.saveModels\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 732,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 731,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 647,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 600,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 525,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, provider.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 486,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 470,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 450,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TestAIGeneration__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 751,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 409,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 408,\n                columnNumber: 7\n            }, this),\n            showAddProvider && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-2abf06658c86e3df\" + \" \" + \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    dir: isArabic ? \"rtl\" : \"ltr\",\n                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"modal-content bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-lg font-semibold text-gray-900 dark:text-white mb-4 font-arabic text-content\",\n                            children: translations.addProvider\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 759,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"space-y-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: selectedProviderId,\n                                onChange: (e)=>setSelectedProviderId(e.target.value),\n                                dir: isArabic ? \"rtl\" : \"ltr\",\n                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white font-arabic\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        className: \"jsx-2abf06658c86e3df\",\n                                        children: translations.selectProvider\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 770,\n                                        columnNumber: 17\n                                    }, this),\n                                    availableProviders.map((provider)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: provider.id,\n                                            className: \"jsx-2abf06658c86e3df\",\n                                            children: [\n                                                provider.icon,\n                                                \" \",\n                                                provider.name\n                                            ]\n                                        }, provider.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 772,\n                                            columnNumber: 19\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 764,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 763,\n                            columnNumber: 13\n                        }, this),\n                        errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex items-center gap-2 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-4 h-4 text-red-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 783,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm text-red-700 dark:text-red-300 font-arabic text-content\",\n                                        children: errorMessage\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 784,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 782,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 781,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"modal-buttons flex gap-3 mt-6 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        setShowAddProvider(false);\n                                        setErrorMessage(\"\");\n                                        setSelectedProviderId(\"\");\n                                    },\n                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors font-arabic text-content\",\n                                    children: translations.cancel\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 792,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleAddProvider,\n                                    disabled: !selectedProviderId,\n                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-arabic text-content\",\n                                    children: translations.add\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 802,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 791,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 758,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 757,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n        lineNumber: 239,\n        columnNumber: 5\n    }, this);\n}\n_s(SettingsPage, \"pKs+72xp9+Lvo6vc4bQUX4ox3H8=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_3__.useContextStore\n    ];\n});\n_c = SettingsPage;\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/settings/page.tsx\n"));

/***/ })

});