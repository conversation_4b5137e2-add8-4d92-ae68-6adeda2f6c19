"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/context-map/page",{

/***/ "(app-pages-browser)/./src/components/SmartFieldAssistant.tsx":
/*!************************************************!*\
  !*** ./src/components/SmartFieldAssistant.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SmartFieldAssistant; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Sparkles,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Sparkles,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Sparkles,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wand-sparkles.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction SmartFieldAssistant(param) {\n    let { fieldName, fieldValue, onValueChange, placeholder, context, className = \"\" } = param;\n    _s();\n    const { currentLanguage, getActiveProviders, getAllData } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore)();\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [generatedSuggestions, setGeneratedSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showSuggestions, setShowSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [copiedIndex, setCopiedIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeProviders, setActiveProviders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isArabic = currentLanguage === \"ar\";\n    // تجنب مشاكل الهيدريشن\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n        setActiveProviders(getActiveProviders());\n    }, [\n        getActiveProviders\n    ]);\n    // تحقق من وجود مقدم خدمة صالح\n    const hasValidProvider = mounted && activeProviders.some((p)=>p.apiKey && p.validationStatus === \"valid\" && p.selectedModels && p.selectedModels.length > 0);\n    const translations = {\n        generateWithAI: isArabic ? \"\\uD83D\\uDCC4 توليد بالذكاء الاصطناعي\" : \"\\uD83D\\uDCC4 Generate with AI\",\n        generating: isArabic ? \"جاري التوليد...\" : \"Generating...\",\n        suggestions: isArabic ? \"اقتراحات ذكية\" : \"Smart Suggestions\",\n        useThis: isArabic ? \"استخدام هذا\" : \"Use This\",\n        copy: isArabic ? \"نسخ\" : \"Copy\",\n        copied: isArabic ? \"تم النسخ\" : \"Copied\",\n        noProviders: isArabic ? \"يرجى إعداد مقدم خدمة AI وتحديد النماذج في صفحة الإعدادات أولاً\" : \"Please configure an AI provider and select models in Settings first\",\n        error: isArabic ? \"حدث خطأ أثناء التوليد\" : \"Error occurred during generation\",\n        tryAgain: isArabic ? \"حاول مرة أخرى\" : \"Try Again\",\n        regenerate: isArabic ? \"إعادة توليد\" : \"Regenerate\",\n        fastGeneration: isArabic ? \"توليد سريع (محسّن)\" : \"Fast Generation (Optimized)\",\n        timeout: isArabic ? \"انتهت مهلة الطلب - حاول مرة أخرى\" : \"Request timeout - try again\"\n    };\n    const generateSuggestions = async ()=>{\n        if (!hasValidProvider) {\n            console.warn(\"No valid provider available:\", {\n                activeProviders,\n                hasValidProvider\n            });\n            alert(translations.noProviders);\n            return;\n        }\n        setIsGenerating(true);\n        try {\n            const allContext = getAllData();\n            const provider = activeProviders.find((p)=>p.apiKey && p.validationStatus === \"valid\");\n            console.log(\"Using provider:\", provider === null || provider === void 0 ? void 0 : provider.name, \"with model:\", provider === null || provider === void 0 ? void 0 : provider.selectedModels[0]);\n            if (!provider) {\n                throw new Error(\"No valid provider found\");\n            }\n            // إنشاء prompt ذكي بناءً على السياق والحقل\n            const prompt = createSmartPrompt(fieldName, fieldValue, allContext, isArabic);\n            console.log(\"Generated prompt:\", prompt);\n            const requestBody = {\n                providerId: provider.id,\n                apiKey: provider.apiKey,\n                model: provider.selectedModels[0] || \"gpt-3.5-turbo\",\n                messages: [\n                    {\n                        role: \"user\",\n                        content: prompt\n                    }\n                ],\n                context: allContext,\n                fieldName,\n                language: currentLanguage,\n                temperature: 0.7,\n                maxTokens: 200 // تقليل maxTokens بشكل كبير للسرعة\n            };\n            console.log(\"Sending request to API:\", requestBody);\n            // إضافة timeout للطلب من جانب العميل\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>{\n                controller.abort();\n            }, 35000); // 35 ثانية timeout\n            const response = await fetch(\"/api/llm/generate\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(requestBody),\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            console.log(\"API Response status:\", response.status);\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error(\"API Error:\", errorText);\n                throw new Error(\"API Error: \".concat(response.status, \" - \").concat(errorText));\n            }\n            const result = await response.json();\n            console.log(\"API Result:\", result);\n            if (result.success) {\n                // استخدام أول اقتراح مباشرة في خانة الكتابة\n                const suggestions = parseSuggestions(result.content);\n                console.log(\"Parsed suggestions:\", suggestions);\n                if (suggestions.length > 0) {\n                    // وضع أول اقتراح في خانة الكتابة\n                    onValueChange(suggestions[0]);\n                    // حفظ باقي الاقتراحات للاستخدام لاحقاً\n                    setGeneratedSuggestions(suggestions);\n                } else {\n                    const errorMsg = isArabic ? \"لم يتم العثور على اقتراحات مناسبة\" : \"No suitable suggestions found\";\n                    onValueChange(errorMsg);\n                }\n            } else {\n                throw new Error(result.error || \"Generation failed\");\n            }\n        } catch (error) {\n            console.error(\"Generation error:\", error);\n            let errorMessage = translations.error;\n            if (error instanceof Error) {\n                if (error.name === \"AbortError\") {\n                    errorMessage = translations.timeout;\n                } else if (error.message.includes(\"timeout\")) {\n                    errorMessage = translations.timeout;\n                } else {\n                    errorMessage = \"\".concat(translations.error, \": \").concat(error.message);\n                }\n            }\n            onValueChange(errorMessage);\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const createSmartPrompt = (fieldName, currentValue, context, isArabic)=>{\n        // استخراج الخيارات المتقدمة من السياق\n        const projectDef = (context === null || context === void 0 ? void 0 : context.projectDefinition) || {};\n        const advancedContext = {\n            projectType: projectDef.projectType,\n            projectNature: projectDef.projectNature,\n            geographicRegion: projectDef.geographicRegion,\n            targetPlatforms: projectDef.targetPlatforms || [],\n            primaryLanguages: projectDef.primaryLanguages || [],\n            complexity: projectDef.complexity,\n            deploymentType: projectDef.deploymentType\n        };\n        // إنشاء سياق متقدم للـ prompt مع التركيز على الخيارات الجديدة\n        const contextString = Object.entries(advancedContext).filter((param)=>{\n            let [_, value] = param;\n            return value && (Array.isArray(value) ? value.length > 0 : true);\n        }).map((param)=>{\n            let [key, value] = param;\n            return \"\".concat(key, \": \").concat(Array.isArray(value) ? value.join(\", \") : value);\n        }).join(\", \");\n        // تحسين الـ prompts لتكون أكثر تفصيلاً وذكاءً\n        const fieldPrompts = {\n            // Project Definition Module\n            name: {\n                ar: \"بناءً على طبيعة المشروع والمنطقة الجغرافية المستهدفة، اقترح 3 أسماء إبداعية ومناسبة للمشروع\",\n                en: \"Based on the project nature and target geographic region, suggest 3 creative and suitable project names\"\n            },\n            purpose: {\n                ar: \"اكتب 3 أوصاف مختلفة ومفصلة لغرض المشروع، مع مراعاة طبيعة المشروع والمنطقة الجغرافية\",\n                en: \"Write 3 different and detailed project purpose descriptions, considering project nature and geographic region\"\n            },\n            targetUsers: {\n                ar: \"حدد 3 مجموعات مختلفة من المستخدمين المستهدفين بناءً على طبيعة المشروع والمنطقة الجغرافية\",\n                en: \"Define 3 different target user groups based on project nature and geographic region\"\n            },\n            goals: {\n                ar: \"اقترح 3 أهداف محددة وقابلة للقياس للمشروع\",\n                en: \"Suggest 3 specific and measurable project goals\"\n            },\n            scope: {\n                ar: \"حدد 3 نطاقات مختلفة للمشروع (صغير، متوسط، كبير)\",\n                en: \"Define 3 different project scopes (small, medium, large)\"\n            },\n            timeline: {\n                ar: \"اقترح 3 جداول زمنية مختلفة للمشروع\",\n                en: \"Suggest 3 different project timelines\"\n            },\n            // Context Map Module\n            timeContext: {\n                ar: \"حدد 3 سياقات زمنية مختلفة مناسبة للمشروع\",\n                en: \"Define 3 different time contexts suitable for the project\"\n            },\n            language: {\n                ar: \"اقترح 3 استراتيجيات لغوية للمشروع\",\n                en: \"Suggest 3 language strategies for the project\"\n            },\n            location: {\n                ar: \"حدد 3 مواقع جغرافية مستهدفة للمشروع\",\n                en: \"Define 3 target geographical locations for the project\"\n            },\n            culturalContext: {\n                ar: \"اقترح 3 اعتبارات ثقافية مهمة للمشروع\",\n                en: \"Suggest 3 important cultural considerations for the project\"\n            },\n            // Emotional Tone Module\n            personality: {\n                ar: \"اقترح 3 شخصيات مختلفة للمشروع تناسب المستخدمين المستهدفين\",\n                en: \"Suggest 3 different project personalities that suit the target users\"\n            },\n            communicationStyle: {\n                ar: \"حدد 3 أساليب تواصل مختلفة مناسبة للمشروع\",\n                en: \"Define 3 different communication styles suitable for the project\"\n            },\n            // Technical Layer Module\n            programmingLanguages: {\n                ar: \"اقترح 3 لغات برمجة مناسبة للمشروع مع التبرير\",\n                en: \"Suggest 3 suitable programming languages for the project with justification\"\n            },\n            frameworks: {\n                ar: \"حدد 3 إطارات عمل تقنية مناسبة للمشروع\",\n                en: \"Define 3 technical frameworks suitable for the project\"\n            }\n        };\n        const fieldPrompt = fieldPrompts[fieldName];\n        const basePrompt = fieldPrompt ? isArabic ? fieldPrompt.ar : fieldPrompt.en : isArabic ? \"اقترح محتوى ذكي ومناسب لـ \".concat(fieldName) : \"Suggest smart and suitable content for \".concat(fieldName);\n        // بناء سياق أكثر ذكاءً\n        const contextInfo = buildIntelligentContext(context, fieldName, isArabic);\n        const instructions = isArabic ? \"قدم 3 اقتراحات مرقمة ومفصلة، كل اقتراح في سطر منفصل. اجعل كل اقتراح متماسكاً مع السياق العام للمشروع.\" : \"Provide 3 numbered and detailed suggestions, each on a separate line. Make each suggestion coherent with the overall project context.\";\n        return \"\".concat(contextInfo, \"\\n\").concat(basePrompt, \"\\n\").concat(instructions);\n    };\n    // وظيفة لبناء سياق ذكي\n    const buildIntelligentContext = (context, fieldName, isArabic)=>{\n        var _context_projectDefinition, _context_projectDefinition1, _context_projectDefinition2;\n        const contextParts = [];\n        const projectDef = (context === null || context === void 0 ? void 0 : context.projectDefinition) || {};\n        // أولوية للخيارات المتقدمة الجديدة - طبيعة المشروع والمنطقة الجغرافية\n        if (projectDef.projectNature) {\n            contextParts.push(isArabic ? \"طبيعة المشروع: \".concat(projectDef.projectNature) : \"Project Nature: \".concat(projectDef.projectNature));\n        }\n        if (projectDef.geographicRegion) {\n            contextParts.push(isArabic ? \"المنطقة الجغرافية: \".concat(projectDef.geographicRegion) : \"Geographic Region: \".concat(projectDef.geographicRegion));\n        }\n        // معلومات المشروع الأساسية\n        if (context === null || context === void 0 ? void 0 : (_context_projectDefinition = context.projectDefinition) === null || _context_projectDefinition === void 0 ? void 0 : _context_projectDefinition.name) {\n            contextParts.push(isArabic ? \"اسم المشروع: \".concat(context.projectDefinition.name) : \"Project Name: \".concat(context.projectDefinition.name));\n        }\n        if (context === null || context === void 0 ? void 0 : (_context_projectDefinition1 = context.projectDefinition) === null || _context_projectDefinition1 === void 0 ? void 0 : _context_projectDefinition1.purpose) {\n            contextParts.push(isArabic ? \"الغرض: \".concat(context.projectDefinition.purpose.substring(0, 100), \"...\") : \"Purpose: \".concat(context.projectDefinition.purpose.substring(0, 100), \"...\"));\n        }\n        if (context === null || context === void 0 ? void 0 : (_context_projectDefinition2 = context.projectDefinition) === null || _context_projectDefinition2 === void 0 ? void 0 : _context_projectDefinition2.targetUsers) {\n            contextParts.push(isArabic ? \"المستخدمون المستهدفون: \".concat(context.projectDefinition.targetUsers.substring(0, 80), \"...\") : \"Target Users: \".concat(context.projectDefinition.targetUsers.substring(0, 80), \"...\"));\n        }\n        // باقي الخيارات المتقدمة\n        if (projectDef.projectType) {\n            contextParts.push(isArabic ? \"نوع المشروع: \".concat(projectDef.projectType) : \"Project Type: \".concat(projectDef.projectType));\n        }\n        if (projectDef.targetPlatforms && projectDef.targetPlatforms.length > 0) {\n            contextParts.push(isArabic ? \"المنصات المستهدفة: \".concat(projectDef.targetPlatforms.join(\", \")) : \"Target Platforms: \".concat(projectDef.targetPlatforms.join(\", \")));\n        }\n        if (projectDef.primaryLanguages && projectDef.primaryLanguages.length > 0) {\n            contextParts.push(isArabic ? \"لغات البرمجة: \".concat(projectDef.primaryLanguages.join(\", \")) : \"Programming Languages: \".concat(projectDef.primaryLanguages.join(\", \")));\n        }\n        if (projectDef.complexity) {\n            contextParts.push(isArabic ? \"مستوى التعقيد: \".concat(projectDef.complexity) : \"Complexity Level: \".concat(projectDef.complexity));\n        }\n        if (projectDef.teamSize) {\n            contextParts.push(isArabic ? \"حجم الفريق: \".concat(projectDef.teamSize) : \"Team Size: \".concat(projectDef.teamSize));\n        }\n        if (projectDef.budget) {\n            contextParts.push(isArabic ? \"نطاق الميزانية: \".concat(projectDef.budget) : \"Budget Range: \".concat(projectDef.budget));\n        }\n        // سياق إضافي حسب المجال\n        if (fieldName.includes(\"technical\") || fieldName.includes(\"programming\") || fieldName.includes(\"frameworks\")) {\n            var _context_technicalLayer;\n            if (context === null || context === void 0 ? void 0 : (_context_technicalLayer = context.technicalLayer) === null || _context_technicalLayer === void 0 ? void 0 : _context_technicalLayer.programmingLanguages) {\n                contextParts.push(isArabic ? \"التقنيات المستخدمة: \".concat(context.technicalLayer.programmingLanguages.substring(0, 60), \"...\") : \"Technologies: \".concat(context.technicalLayer.programmingLanguages.substring(0, 60), \"...\"));\n            }\n        }\n        if (fieldName.includes(\"emotional\") || fieldName.includes(\"personality\") || fieldName.includes(\"communication\")) {\n            var _context_emotionalTone;\n            if (context === null || context === void 0 ? void 0 : (_context_emotionalTone = context.emotionalTone) === null || _context_emotionalTone === void 0 ? void 0 : _context_emotionalTone.personality) {\n                contextParts.push(isArabic ? \"الشخصية المطلوبة: \".concat(context.emotionalTone.personality.substring(0, 60), \"...\") : \"Required Personality: \".concat(context.emotionalTone.personality.substring(0, 60), \"...\"));\n            }\n        }\n        return contextParts.length > 0 ? (isArabic ? \"السياق الحالي:\\n\" : \"Current Context:\\n\") + contextParts.join(\"\\n\") : isArabic ? \"مشروع جديد\" : \"New Project\";\n    };\n    const parseSuggestions = (content)=>{\n        // تقسيم المحتوى إلى اقتراحات منفصلة\n        const lines = content.split(\"\\n\").filter((line)=>line.trim());\n        const suggestions = [];\n        for (const line of lines){\n            // البحث عن الأسطر المرقمة أو التي تبدأ برقم\n            if (/^\\d+[.\\-\\)]\\s*/.test(line.trim()) || /^[•\\-\\*]\\s*/.test(line.trim())) {\n                const cleaned = line.replace(/^\\d+[.\\-\\)]\\s*/, \"\").replace(/^[•\\-\\*]\\s*/, \"\").trim();\n                if (cleaned && cleaned.length > 10) {\n                    suggestions.push(cleaned);\n                }\n            } else if (line.trim().length > 20 && !line.includes(\":\") && suggestions.length < 3) {\n                suggestions.push(line.trim());\n            }\n        }\n        // إذا لم نجد اقتراحات مرقمة، نقسم النص إلى جمل\n        if (suggestions.length === 0) {\n            const sentences = content.split(/[.!?]+/).filter((s)=>s.trim().length > 20);\n            return sentences.slice(0, 3).map((s)=>s.trim());\n        }\n        return suggestions.slice(0, 3);\n    };\n    const copyToClipboard = async (text, index)=>{\n        try {\n            await navigator.clipboard.writeText(text);\n            setCopiedIndex(index);\n            setTimeout(()=>setCopiedIndex(null), 2000);\n        } catch (error) {\n            console.error(\"Failed to copy:\", error);\n        }\n    };\n    const regenerateContent = async ()=>{\n        if (generatedSuggestions.length > 1) {\n            // استخدام الاقتراح التالي إذا كان متوفراً\n            const currentIndex = generatedSuggestions.findIndex((s)=>s === fieldValue);\n            const nextIndex = (currentIndex + 1) % generatedSuggestions.length;\n            onValueChange(generatedSuggestions[nextIndex]);\n        } else {\n            // توليد محتوى جديد\n            await generateSuggestions();\n        }\n    };\n    // تجنب مشاكل الهيدريشن\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-2 \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative flex items-center gap-2 px-4 py-2.5 rounded-lg font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-blue-500/80 to-indigo-600/80 text-white shadow-md opacity-50 font-arabic\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 431,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: [\n                            \"\\uD83D\\uDCC4 \",\n                            isArabic ? \"توليد بالذكاء الاصطناعي\" : \"Generate with AI\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 432,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                lineNumber: 430,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n            lineNumber: 429,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center gap-2 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: generateSuggestions,\n                disabled: isGenerating || !hasValidProvider,\n                className: \"relative flex items-center gap-2 px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group \".concat(hasValidProvider ? \"bg-gradient-to-br from-purple-500/80 via-blue-500/80 to-indigo-600/80 hover:from-purple-600/90 hover:via-blue-600/90 hover:to-indigo-700/90 text-white shadow-lg hover:shadow-xl hover:shadow-purple-500/25 hover:scale-105 active:scale-95\" : \"bg-gray-200/50 dark:bg-gray-700/50 text-gray-400 dark:text-gray-500 cursor-not-allowed border-gray-300/30 dark:border-gray-600/30\", \" \").concat(isGenerating ? \"animate-pulse scale-105\" : \"\", \" \").concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                title: hasValidProvider ? isArabic ? \"توليد سريع محسّن - أقل من 5 ثواني\" : \"Fast optimized generation - under 5 seconds\" : translations.noProviders,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 447,\n                        columnNumber: 9\n                    }, this),\n                    hasValidProvider && !isGenerating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 -skew-x-12 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 group-hover:animate-shimmer\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 451,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex items-center gap-2 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                        children: [\n                            isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-4 h-4 animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-4 h-4 group-hover:rotate-12 transition-transform duration-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 458,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-arabic font-medium\",\n                                children: isGenerating ? translations.generating : translations.fastGeneration\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 454,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                lineNumber: 440,\n                columnNumber: 7\n            }, this),\n            fieldValue && generatedSuggestions.length > 0 && !isGenerating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: regenerateContent,\n                className: \"relative flex items-center gap-2 px-3 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-orange-500/80 to-red-500/80 hover:from-orange-600/90 hover:to-red-600/90 text-white shadow-lg hover:shadow-xl hover:shadow-orange-500/25 hover:scale-105 active:scale-95 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                title: isArabic ? \"إعادة توليد\" : \"Regenerate\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 473,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex items-center gap-1 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-4 h-4 group-hover:rotate-180 transition-transform duration-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 475,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-arabic font-medium\",\n                                children: isArabic ? \"إعادة توليد\" : \"Regenerate\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 476,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 474,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                lineNumber: 468,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n        lineNumber: 439,\n        columnNumber: 5\n    }, this);\n}\n_s(SmartFieldAssistant, \"ZrnkxJH8z8I5/fadLS+VIi/I8fM=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore\n    ];\n});\n_c = SmartFieldAssistant;\nvar _c;\n$RefreshReg$(_c, \"SmartFieldAssistant\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/SmartFieldAssistant.tsx\n"));

/***/ })

});