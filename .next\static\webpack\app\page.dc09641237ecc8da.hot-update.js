"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/store/contextStore.ts":
/*!***********************************!*\
  !*** ./src/store/contextStore.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useContextStore: function() { return /* binding */ useContextStore; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n\n\n// القيم الافتراضية\nconst initialState = {\n    projectDefinition: {\n        name: \"\",\n        purpose: \"\",\n        targetUsers: \"\",\n        goals: \"\",\n        scope: \"\",\n        timeline: \"\",\n        projectType: \"\",\n        targetPlatforms: [],\n        primaryLanguages: [],\n        complexity: \"\",\n        budget: \"\",\n        teamSize: \"\",\n        deploymentType: \"\"\n    },\n    contextMap: {\n        timeContext: \"\",\n        language: \"\",\n        location: \"\",\n        culturalContext: \"\",\n        behavioralAspects: \"\",\n        environmentalFactors: \"\"\n    },\n    emotionalTone: {\n        personality: \"\",\n        communicationStyle: \"\",\n        userExperience: \"\",\n        brandVoice: \"\",\n        emotionalIntelligence: \"\",\n        interactionFlow: \"\"\n    },\n    technicalLayer: {\n        programmingLanguages: \"\",\n        frameworks: \"\",\n        llmModels: \"\",\n        databases: \"\",\n        apis: \"\",\n        infrastructure: \"\",\n        architecturePattern: \"\",\n        scalingStrategy: \"\",\n        securityRequirements: \"\",\n        performanceTargets: \"\",\n        integrationNeeds: \"\",\n        monitoringTools: \"\"\n    },\n    legalRisk: {\n        privacyConcerns: \"\",\n        dataProtection: \"\",\n        compliance: \"\",\n        risks: \"\",\n        mitigation: \"\",\n        ethicalConsiderations: \"\"\n    },\n    currentLanguage: \"ar\",\n    outputFormat: \"markdown\",\n    showAdvancedOptions: true,\n    apiSettings: {\n        providers: [],\n        globalSettings: {\n            temperature: 0.7,\n            topP: 0.9,\n            maxTokens: 1000,\n            timeout: 30000\n        },\n        // Legacy support\n        openaiApiKey: \"\",\n        openrouterApiKey: \"\",\n        customModels: []\n    }\n};\n// إنشاء المتجر مع التخزين المستمر\nconst useContextStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        ...initialState,\n        updateProjectDefinition: (data)=>set((state)=>({\n                    projectDefinition: {\n                        ...state.projectDefinition,\n                        ...data\n                    }\n                })),\n        updateContextMap: (data)=>set((state)=>({\n                    contextMap: {\n                        ...state.contextMap,\n                        ...data\n                    }\n                })),\n        updateEmotionalTone: (data)=>set((state)=>({\n                    emotionalTone: {\n                        ...state.emotionalTone,\n                        ...data\n                    }\n                })),\n        updateTechnicalLayer: (data)=>set((state)=>({\n                    technicalLayer: {\n                        ...state.technicalLayer,\n                        ...data\n                    }\n                })),\n        updateLegalRisk: (data)=>set((state)=>({\n                    legalRisk: {\n                        ...state.legalRisk,\n                        ...data\n                    }\n                })),\n        setLanguage: (lang)=>set({\n                currentLanguage: lang\n            }),\n        setOutputFormat: (format)=>set({\n                outputFormat: format\n            }),\n        setShowAdvancedOptions: (show)=>set({\n                showAdvancedOptions: show\n            }),\n        setApiSettings: (settings)=>set({\n                apiSettings: {\n                    ...settings,\n                    providers: settings.providers || []\n                }\n            }),\n        // LLM Provider management functions\n        addProvider: (provider)=>set((state)=>{\n                const existingProviders = state.apiSettings.providers || [];\n                // تحقق من عدم وجود مقدم خدمة بنفس المعرف\n                if (existingProviders.find((p)=>p.id === provider.id)) {\n                    console.warn(\"Provider with id \".concat(provider.id, \" already exists\"));\n                    return state; // لا تضيف مقدم خدمة مكرر\n                }\n                // إضافة القيم الافتراضية للميزات المتقدمة\n                const enhancedProvider = {\n                    ...provider,\n                    priority: provider.priority || 5,\n                    isBackup: provider.isBackup || false,\n                    maxRequestsPerMinute: provider.maxRequestsPerMinute || 60,\n                    timeout: provider.timeout || 30,\n                    retryAttempts: provider.retryAttempts || 3,\n                    stats: provider.stats || {\n                        totalRequests: 0,\n                        successfulRequests: 0,\n                        failedRequests: 0,\n                        averageResponseTime: 0,\n                        totalTokensUsed: 0,\n                        totalCost: 0\n                    }\n                };\n                return {\n                    apiSettings: {\n                        ...state.apiSettings,\n                        providers: [\n                            ...existingProviders,\n                            enhancedProvider\n                        ]\n                    }\n                };\n            }),\n        updateProvider: (providerId, updates)=>set((state)=>({\n                    apiSettings: {\n                        ...state.apiSettings,\n                        providers: (state.apiSettings.providers || []).map((p)=>p.id === providerId ? {\n                                ...p,\n                                ...updates\n                            } : p)\n                    }\n                })),\n        removeProvider: (providerId)=>set((state)=>({\n                    apiSettings: {\n                        ...state.apiSettings,\n                        providers: (state.apiSettings.providers || []).filter((p)=>p.id !== providerId),\n                        defaultProvider: state.apiSettings.defaultProvider === providerId ? undefined : state.apiSettings.defaultProvider\n                    }\n                })),\n        validateProvider: async (providerId)=>{\n            const state = get();\n            const provider = (state.apiSettings.providers || []).find((p)=>p.id === providerId);\n            if (!provider) return false;\n            try {\n                // Update status to pending\n                state.updateProvider(providerId, {\n                    validationStatus: \"pending\",\n                    errorMessage: undefined\n                });\n                // Call validation API\n                const response = await fetch(\"/api/llm/validate\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        providerId: provider.id,\n                        apiKey: provider.apiKey,\n                        baseUrl: provider.baseUrl\n                    })\n                });\n                const result = await response.json();\n                if (result.valid) {\n                    state.updateProvider(providerId, {\n                        validationStatus: \"valid\",\n                        lastValidated: new Date(),\n                        errorMessage: undefined,\n                        isEnabled: true // تفعيل المقدم تلقائياً عند نجاح التحقق\n                    });\n                    return true;\n                } else {\n                    state.updateProvider(providerId, {\n                        validationStatus: \"invalid\",\n                        errorMessage: result.error || \"Validation failed\"\n                    });\n                    return false;\n                }\n            } catch (error) {\n                state.updateProvider(providerId, {\n                    validationStatus: \"error\",\n                    errorMessage: error instanceof Error ? error.message : \"Unknown error\"\n                });\n                return false;\n            }\n        },\n        getProvider: (providerId)=>{\n            const state = get();\n            return (state.apiSettings.providers || []).find((p)=>p.id === providerId);\n        },\n        getActiveProviders: ()=>{\n            const state = get();\n            return (state.apiSettings.providers || []).filter((p)=>p.isEnabled);\n        },\n        setDefaultProvider: (providerId)=>set((state)=>({\n                    apiSettings: {\n                        ...state.apiSettings,\n                        defaultProvider: providerId\n                    }\n                })),\n        // ميزات متقدمة للمزودين\n        getProvidersByPriority: ()=>{\n            const state = get();\n            return (state.apiSettings.providers || []).filter((p)=>p.isEnabled).sort((a, b)=>(b.priority || 5) - (a.priority || 5));\n        },\n        getBackupProviders: ()=>{\n            const state = get();\n            return (state.apiSettings.providers || []).filter((p)=>p.isEnabled && p.isBackup);\n        },\n        updateProviderStats: (id, stats)=>set((state)=>{\n                const providers = state.apiSettings.providers || [];\n                const providerIndex = providers.findIndex((p)=>p.id === id);\n                if (providerIndex !== -1) {\n                    const updatedProviders = [\n                        ...providers\n                    ];\n                    updatedProviders[providerIndex] = {\n                        ...updatedProviders[providerIndex],\n                        stats: {\n                            ...updatedProviders[providerIndex].stats,\n                            ...stats,\n                            lastUsed: new Date()\n                        }\n                    };\n                    return {\n                        apiSettings: {\n                            ...state.apiSettings,\n                            providers: updatedProviders\n                        }\n                    };\n                }\n                return state;\n            }),\n        getBestProvider: function() {\n            let criteria = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"reliability\";\n            const state = get();\n            const activeProviders = (state.apiSettings.providers || []).filter((p)=>p.isEnabled && !p.isBackup);\n            if (activeProviders.length === 0) return undefined;\n            switch(criteria){\n                case \"speed\":\n                    return activeProviders.reduce((best, current)=>{\n                        var _best_stats, _current_stats;\n                        const bestSpeed = ((_best_stats = best.stats) === null || _best_stats === void 0 ? void 0 : _best_stats.averageResponseTime) || Infinity;\n                        const currentSpeed = ((_current_stats = current.stats) === null || _current_stats === void 0 ? void 0 : _current_stats.averageResponseTime) || Infinity;\n                        return currentSpeed < bestSpeed ? current : best;\n                    });\n                case \"cost\":\n                    return activeProviders.reduce((best, current)=>{\n                        const bestCost = best.costPerToken || Infinity;\n                        const currentCost = current.costPerToken || Infinity;\n                        return currentCost < bestCost ? current : best;\n                    });\n                case \"reliability\":\n                default:\n                    return activeProviders.reduce((best, current)=>{\n                        const bestReliability = best.stats ? best.stats.successfulRequests / (best.stats.totalRequests || 1) : 0;\n                        const currentReliability = current.stats ? current.stats.successfulRequests / (current.stats.totalRequests || 1) : 0;\n                        return currentReliability > bestReliability ? current : best;\n                    });\n            }\n        },\n        resetAll: ()=>set(initialState),\n        // مسح جميع الإجابات فقط (الاحتفاظ بالإعدادات)\n        clearAllAnswers: ()=>set((state)=>({\n                    ...state,\n                    projectDefinition: {\n                        name: \"\",\n                        purpose: \"\",\n                        targetUsers: \"\",\n                        goals: \"\",\n                        scope: \"\",\n                        timeline: \"\"\n                    },\n                    contextMap: {\n                        timeContext: \"\",\n                        language: \"\",\n                        location: \"\",\n                        culturalContext: \"\",\n                        behavioralAspects: \"\",\n                        environmentalFactors: \"\"\n                    },\n                    emotionalTone: {\n                        personality: \"\",\n                        communicationStyle: \"\",\n                        userExperience: \"\",\n                        brandVoice: \"\",\n                        emotionalIntelligence: \"\",\n                        interactionFlow: \"\"\n                    },\n                    technicalLayer: {\n                        programmingLanguages: \"\",\n                        frameworks: \"\",\n                        llmModels: \"\",\n                        databases: \"\",\n                        apis: \"\",\n                        infrastructure: \"\"\n                    },\n                    legalRisk: {\n                        privacyConcerns: \"\",\n                        dataProtection: \"\",\n                        compliance: \"\",\n                        risks: \"\",\n                        mitigation: \"\",\n                        ethicalConsiderations: \"\"\n                    }\n                })),\n        getModuleData: (module)=>{\n            const state = get();\n            switch(module){\n                case \"project\":\n                    return state.projectDefinition;\n                case \"context\":\n                    return state.contextMap;\n                case \"emotional\":\n                    return state.emotionalTone;\n                case \"technical\":\n                    return state.technicalLayer;\n                case \"legal\":\n                    return state.legalRisk;\n                default:\n                    return {};\n            }\n        },\n        getAllData: ()=>{\n            const state = get();\n            return {\n                projectDefinition: state.projectDefinition,\n                contextMap: state.contextMap,\n                emotionalTone: state.emotionalTone,\n                technicalLayer: state.technicalLayer,\n                legalRisk: state.legalRisk\n            };\n        }\n    }), {\n    name: \"contextkit-storage\",\n    version: 1,\n    skipHydration: true\n}));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/contextStore.ts\n"));

/***/ })

});