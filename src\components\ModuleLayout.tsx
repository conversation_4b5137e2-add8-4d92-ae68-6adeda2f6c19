'use client';

import { ReactNode } from 'react';
import Header from './Header';
import ProgressIndicator from './ProgressIndicator';
import AutoSaveIndicator from './AutoSaveIndicator';
import { useContextStore } from '@/store/contextStore';

interface ModuleLayoutProps {
  title: string;
  titleAr: string;
  subtitle: string;
  subtitleAr: string;
  emoji: string;
  moduleKey?: string;
  backLink?: {
    href: string;
    label: string;
    labelAr: string;
  };
  nextLink?: {
    href: string;
    label: string;
    labelAr: string;
  };
  children: ReactNode;
  rightPanel: ReactNode;
}

export default function ModuleLayout({
  title,
  titleAr,
  subtitle,
  subtitleAr,
  emoji,
  moduleKey,
  backLink,
  nextLink,
  children,
  rightPanel
}: ModuleLayoutProps) {
  const { currentLanguage } = useContextStore();
  const isArabic = currentLanguage === 'ar';

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800" dir={isArabic ? 'rtl' : 'ltr'}>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <Header
          title={isArabic ? titleAr : title}
          subtitle={isArabic ? subtitleAr : subtitle}
          emoji={emoji}
          backLink={backLink ? {
            href: backLink.href,
            label: isArabic ? backLink.labelAr : backLink.label
          } : undefined}
        />

        {/* Progress Indicator */}
        <ProgressIndicator currentModule={moduleKey} />

        {/* Main Content - Two Panel Layout */}
        <div className="grid lg:grid-cols-2 gap-8 max-w-7xl mx-auto">
          {/* Left Panel - Questions (Always on left) */}
          <div className="space-y-6 lg:order-1">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
              <div className="flex items-center justify-center mb-4">
                {isArabic ? (
                  <>
                    <h2 className="text-xl font-semibold text-gray-900 dark:text-white text-center ml-3">
                      الأسئلة الذكية
                    </h2>
                    <span className="text-2xl">✍️</span>
                  </>
                ) : (
                  <>
                    <span className="text-2xl mr-3">✍️</span>
                    <h2 className="text-xl font-semibold text-gray-900 dark:text-white text-center">
                      Smart Questions
                    </h2>
                  </>
                )}
              </div>
              {children}
            </div>
          </div>

          {/* Right Panel - Outputs (Always on right) */}
          <div className="space-y-6 lg:order-2">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 sticky top-8">
              <div className="flex items-center justify-center mb-4">
                {isArabic ? (
                  <>
                    <h2 className="text-xl font-semibold text-gray-900 dark:text-white text-center ml-3">
                      المخرجات المجمّعة
                    </h2>
                    <span className="text-2xl">📄</span>
                  </>
                ) : (
                  <>
                    <span className="text-2xl mr-3">📄</span>
                    <h2 className="text-xl font-semibold text-gray-900 dark:text-white text-center">
                      Generated Outputs
                    </h2>
                  </>
                )}
              </div>
              {rightPanel}
            </div>
          </div>
        </div>

        {/* Navigation */}
        {(backLink || nextLink) && (
          <div className="flex justify-between items-center mt-12 max-w-7xl mx-auto gap-6">
            {isArabic ? (
              // Arabic layout: Next button on the right, Back button on the left
              <>
                {nextLink && (
                  <a
                    href={nextLink.href}
                    className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-all duration-200 hover:scale-105 shadow-sm hover:shadow-md font-medium"
                  >
                    {nextLink.labelAr}
                  </a>
                )}

                {backLink ? (
                  <a
                    href={backLink.href}
                    className="px-6 py-3 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500 transition-all duration-200 hover:scale-105 shadow-sm hover:shadow-md font-medium"
                  >
                    {backLink.labelAr}
                  </a>
                ) : (
                  <div></div>
                )}
              </>
            ) : (
              // English layout: Back button on the left, Next button on the right
              <>
                {backLink ? (
                  <a
                    href={backLink.href}
                    className="px-6 py-3 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500 transition-all duration-200 hover:scale-105 shadow-sm hover:shadow-md font-medium"
                  >
                    {backLink.label}
                  </a>
                ) : (
                  <div></div>
                )}

                {nextLink && (
                  <a
                    href={nextLink.href}
                    className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-all duration-200 hover:scale-105 shadow-sm hover:shadow-md font-medium"
                  >
                    {nextLink.label}
                  </a>
                )}
              </>
            )}
          </div>
        )}

        {/* Auto-save Indicator */}
        <AutoSaveIndicator />
      </div>
    </div>
  );
}
