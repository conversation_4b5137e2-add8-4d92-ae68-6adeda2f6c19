"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/llm/generate/route";
exports.ids = ["app/api/llm/generate/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fllm%2Fgenerate%2Froute&page=%2Fapi%2Fllm%2Fgenerate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fllm%2Fgenerate%2Froute.ts&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fllm%2Fgenerate%2Froute&page=%2Fapi%2Fllm%2Fgenerate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fllm%2Fgenerate%2Froute.ts&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_faiss_Desktop_ContextKit_src_app_api_llm_generate_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/llm/generate/route.ts */ \"(rsc)/./src/app/api/llm/generate/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/llm/generate/route\",\n        pathname: \"/api/llm/generate\",\n        filename: \"route\",\n        bundlePath: \"app/api/llm/generate/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\api\\\\llm\\\\generate\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_faiss_Desktop_ContextKit_src_app_api_llm_generate_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/llm/generate/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fllm%2Fgenerate%2Froute&page=%2Fapi%2Fllm%2Fgenerate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fllm%2Fgenerate%2Froute.ts&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/llm/generate/route.ts":
/*!*******************************************!*\
  !*** ./src/app/api/llm/generate/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_llmProviders__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/llmProviders */ \"(rsc)/./src/lib/llmProviders.ts\");\n\n\n/**\n * API للتوليد الذكي باستخدام مقدمي خدمات LLM المختلفين\n */ async function POST(request) {\n    try {\n        const { providerId, apiKey, model, messages, context, fieldName, language = \"ar\", temperature = 0.7, maxTokens = 1000, baseUrl } = await request.json();\n        if (!providerId || !apiKey || !messages) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Provider ID, API key, and messages are required\"\n            }, {\n                status: 400\n            });\n        }\n        const provider = (0,_lib_llmProviders__WEBPACK_IMPORTED_MODULE_1__.getProviderById)(providerId);\n        if (!provider) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unknown provider\"\n            }, {\n                status: 400\n            });\n        }\n        const finalBaseUrl = baseUrl || provider.baseUrl;\n        const headers = {\n            \"Content-Type\": \"application/json\",\n            ...(0,_lib_llmProviders__WEBPACK_IMPORTED_MODULE_1__.getProviderHeaders)(providerId)\n        };\n        // إنشاء system prompt ذكي بناءً على السياق\n        const systemPrompt = createSmartSystemPrompt(context, fieldName, language);\n        const finalMessages = [\n            {\n                role: \"system\",\n                content: systemPrompt\n            },\n            ...messages\n        ];\n        let response;\n        switch(providerId){\n            case \"openai\":\n            case \"openrouter\":\n            case \"deepseek\":\n            case \"groq\":\n            case \"mistral\":\n                response = await generateOpenAICompatible(finalBaseUrl, apiKey, headers, model, finalMessages, temperature, maxTokens);\n                break;\n            case \"anthropic\":\n                response = await generateAnthropic(finalBaseUrl, apiKey, headers, model, finalMessages, temperature, maxTokens);\n                break;\n            case \"google\":\n                response = await generateGoogle(finalBaseUrl, apiKey, headers, model, finalMessages, temperature, maxTokens);\n                break;\n            case \"cohere\":\n                response = await generateCohere(finalBaseUrl, apiKey, headers, model, finalMessages, temperature, maxTokens);\n                break;\n            default:\n                response = await generateOpenAICompatible(finalBaseUrl, apiKey, headers, model, finalMessages, temperature, maxTokens);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response);\n    } catch (error) {\n        console.error(\"Generation error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\nfunction createSmartSystemPrompt(context, fieldName, language) {\n    const isArabic = language === \"ar\";\n    const basePrompt = isArabic ? `أنت مساعد ذكي متخصص في مساعدة المستخدمين في بناء سياق منظم ومفصل للمشاريع التقنية والإبداعية.` : `You are an AI assistant specialized in helping users build structured and detailed context for technical and creative projects.`;\n    // تقليل حجم السياق - أخذ المعلومات المهمة فقط\n    const relevantContext = extractRelevantContext(context, fieldName);\n    const contextInfo = isArabic ? `معلومات المشروع: ${relevantContext}` : `Project info: ${relevantContext}`;\n    const fieldGuidance = getFieldGuidance(fieldName, isArabic);\n    const instructions = isArabic ? `\nتعليمات:\n1. قدم إجابة مختصرة ومفيدة (2-3 جمل)\n2. استخدم المعلومات المتوفرة لتحسين إجابتك\n3. اجعل الإجابة عملية وقابلة للتطبيق\n4. استخدم اللغة العربية الواضحة\n` : `\nInstructions:\n1. Provide a concise and helpful response (2-3 sentences)\n2. Use available information to improve your answer\n3. Make the response practical and actionable\n4. Use clear language\n`;\n    return `${basePrompt}\\n\\n${contextInfo}\\n\\n${fieldGuidance}\\n\\n${instructions}`;\n}\nfunction extractRelevantContext(context, fieldName) {\n    if (!context) return \"\";\n    const contextParts = [];\n    // تحليل المرحلة الحالية من المشروع\n    const projectStage = analyzeProjectStage(context);\n    contextParts.push(`Project Stage: ${projectStage}`);\n    // معلومات المشروع الأساسية\n    if (context?.projectDefinition) {\n        const pd = context.projectDefinition;\n        if (pd.name) contextParts.push(`Name: ${pd.name}`);\n        if (pd.purpose) contextParts.push(`Purpose: ${pd.purpose.substring(0, 120)}...`);\n        if (pd.targetUsers) contextParts.push(`Users: ${pd.targetUsers.substring(0, 80)}...`);\n        if (pd.goals) contextParts.push(`Goals: ${pd.goals.substring(0, 80)}...`);\n    }\n    // سياق تقني إذا كان الحقل تقنياً\n    if (isFieldTechnical(fieldName) && context?.technicalLayer) {\n        const tl = context.technicalLayer;\n        if (tl.programmingLanguages) contextParts.push(`Tech: ${tl.programmingLanguages.substring(0, 60)}...`);\n        if (tl.frameworks) contextParts.push(`Frameworks: ${tl.frameworks.substring(0, 60)}...`);\n    }\n    // سياق عاطفي إذا كان الحقل متعلقاً بالتجربة\n    if (isFieldEmotional(fieldName) && context?.emotionalTone) {\n        const et = context.emotionalTone;\n        if (et.personality) contextParts.push(`Personality: ${et.personality.substring(0, 60)}...`);\n        if (et.communicationStyle) contextParts.push(`Style: ${et.communicationStyle.substring(0, 60)}...`);\n    }\n    return contextParts.length > 0 ? contextParts.join(\" | \") : \"New Project\";\n}\nfunction analyzeProjectStage(context) {\n    let completedSections = 0;\n    let totalSections = 5;\n    if (context?.projectDefinition && Object.values(context.projectDefinition).some((v)=>v)) completedSections++;\n    if (context?.contextMap && Object.values(context.contextMap).some((v)=>v)) completedSections++;\n    if (context?.emotionalTone && Object.values(context.emotionalTone).some((v)=>v)) completedSections++;\n    if (context?.technicalLayer && Object.values(context.technicalLayer).some((v)=>v)) completedSections++;\n    if (context?.legalRisk && Object.values(context.legalRisk).some((v)=>v)) completedSections++;\n    const percentage = completedSections / totalSections * 100;\n    if (percentage < 20) return \"Initial Planning\";\n    else if (percentage < 50) return \"Context Building\";\n    else if (percentage < 80) return \"Detail Definition\";\n    else return \"Final Review\";\n}\nfunction isFieldTechnical(fieldName) {\n    return [\n        \"programmingLanguages\",\n        \"frameworks\",\n        \"llmModels\",\n        \"databases\",\n        \"apis\",\n        \"infrastructure\"\n    ].includes(fieldName);\n}\nfunction isFieldEmotional(fieldName) {\n    return [\n        \"personality\",\n        \"communicationStyle\",\n        \"userExperience\",\n        \"brandVoice\",\n        \"emotionalIntelligence\",\n        \"interactionFlow\"\n    ].includes(fieldName);\n}\nfunction getFieldGuidance(fieldName, isArabic) {\n    const fieldGuidanceMap = {\n        name: {\n            ar: \"المجال المطلوب: اسم المشروع - قدم اقتراحات لأسماء إبداعية ومناسبة للمشروع\",\n            en: \"Required field: Project name - Provide suggestions for creative and suitable project names\"\n        },\n        purpose: {\n            ar: \"المجال المطلوب: الغرض من المشروع - اشرح الهدف الرئيسي والقيمة المضافة\",\n            en: \"Required field: Project purpose - Explain the main goal and added value\"\n        },\n        targetUsers: {\n            ar: \"المجال المطلوب: المستخدمون المستهدفون - حدد الجمهور المستهدف بدقة\",\n            en: \"Required field: Target users - Define the target audience precisely\"\n        },\n        goals: {\n            ar: \"المجال المطلوب: الأهداف - حدد أهداف واضحة وقابلة للقياس\",\n            en: \"Required field: Goals - Define clear and measurable objectives\"\n        },\n        scope: {\n            ar: \"المجال المطلوب: نطاق المشروع - حدد حدود وإمكانيات المشروع\",\n            en: \"Required field: Project scope - Define project boundaries and capabilities\"\n        },\n        timeline: {\n            ar: \"المجال المطلوب: الجدول الزمني - اقترح خطة زمنية واقعية\",\n            en: \"Required field: Timeline - Suggest a realistic time plan\"\n        },\n        programmingLanguages: {\n            ar: \"المجال المطلوب: لغات البرمجة - اقترح أفضل لغات البرمجة للمشروع\",\n            en: \"Required field: Programming languages - Suggest the best programming languages for the project\"\n        },\n        frameworks: {\n            ar: \"المجال المطلوب: الأطر التقنية - اقترح أفضل الأطر والمكتبات\",\n            en: \"Required field: Frameworks - Suggest the best frameworks and libraries\"\n        },\n        databases: {\n            ar: \"المجال المطلوب: قواعد البيانات - اقترح أنسب قواعد البيانات\",\n            en: \"Required field: Databases - Suggest the most suitable databases\"\n        }\n    };\n    const guidance = fieldGuidanceMap[fieldName];\n    if (guidance) {\n        return isArabic ? guidance.ar : guidance.en;\n    }\n    return isArabic ? `المجال المطلوب: ${fieldName} - قدم محتوى مفيد ومناسب لهذا المجال` : `Required field: ${fieldName} - Provide helpful and appropriate content for this field`;\n}\nasync function generateOpenAICompatible(baseUrl, apiKey, headers, model, messages, temperature, maxTokens) {\n    try {\n        // إضافة timeout للطلب\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), 30000); // 30 ثانية timeout\n        const response = await fetch(`${baseUrl}/chat/completions`, {\n            method: \"POST\",\n            headers: {\n                ...headers,\n                \"Authorization\": `Bearer ${apiKey}`\n            },\n            body: JSON.stringify({\n                model,\n                messages,\n                temperature,\n                max_tokens: Math.min(maxTokens, 300),\n                stream: false // تأكد من عدم استخدام streaming\n            }),\n            signal: controller.signal\n        });\n        clearTimeout(timeoutId);\n        if (!response.ok) {\n            const error = await response.text();\n            throw new Error(`API error: ${response.status} - ${error}`);\n        }\n        const data = await response.json();\n        const content = data.choices?.[0]?.message?.content || \"\";\n        return {\n            success: true,\n            content,\n            usage: data.usage\n        };\n    } catch (error) {\n        if (error instanceof Error && error.name === \"AbortError\") {\n            return {\n                success: false,\n                error: \"Request timeout - please try again\"\n            };\n        }\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        };\n    }\n}\nasync function generateAnthropic(baseUrl, apiKey, headers, model, messages, temperature, maxTokens) {\n    try {\n        // Convert OpenAI format to Anthropic format\n        const anthropicMessages = messages.filter((m)=>m.role !== \"system\");\n        const systemMessage = messages.find((m)=>m.role === \"system\")?.content || \"\";\n        const response = await fetch(`${baseUrl}/messages`, {\n            method: \"POST\",\n            headers: {\n                ...headers,\n                \"x-api-key\": apiKey\n            },\n            body: JSON.stringify({\n                model,\n                max_tokens: maxTokens,\n                temperature,\n                system: systemMessage,\n                messages: anthropicMessages\n            })\n        });\n        if (!response.ok) {\n            const error = await response.text();\n            throw new Error(`API error: ${response.status} - ${error}`);\n        }\n        const data = await response.json();\n        const content = data.content?.[0]?.text || \"\";\n        return {\n            success: true,\n            content,\n            usage: data.usage\n        };\n    } catch (error) {\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        };\n    }\n}\nasync function generateGoogle(baseUrl, apiKey, headers, model, messages, temperature, maxTokens) {\n    try {\n        // إضافة timeout\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), 30000);\n        // Convert to Google format\n        const contents = messages.map((msg)=>({\n                role: msg.role === \"assistant\" ? \"model\" : \"user\",\n                parts: [\n                    {\n                        text: msg.content\n                    }\n                ]\n            }));\n        const response = await fetch(`${baseUrl}/models/${model}:generateContent?key=${apiKey}`, {\n            method: \"POST\",\n            headers,\n            body: JSON.stringify({\n                contents,\n                generationConfig: {\n                    temperature,\n                    maxOutputTokens: Math.min(maxTokens, 300),\n                    candidateCount: 1 // طلب مرشح واحد فقط للسرعة\n                }\n            }),\n            signal: controller.signal\n        });\n        clearTimeout(timeoutId);\n        if (!response.ok) {\n            const error = await response.text();\n            throw new Error(`API error: ${response.status} - ${error}`);\n        }\n        const data = await response.json();\n        const content = data.candidates?.[0]?.content?.parts?.[0]?.text || \"\";\n        return {\n            success: true,\n            content,\n            usage: data.usageMetadata\n        };\n    } catch (error) {\n        if (error instanceof Error && error.name === \"AbortError\") {\n            return {\n                success: false,\n                error: \"Request timeout - please try again\"\n            };\n        }\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        };\n    }\n}\nasync function generateCohere(baseUrl, apiKey, headers, model, messages, temperature, maxTokens) {\n    try {\n        // إضافة timeout للطلب\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), 30000);\n        // تحويل messages إلى تنسيق Cohere\n        const lastMessage = messages[messages.length - 1];\n        const chatHistory = messages.slice(0, -1).map((msg)=>({\n                role: msg.role === \"assistant\" ? \"CHATBOT\" : \"USER\",\n                message: msg.content\n            }));\n        const response = await fetch(`${baseUrl}/chat`, {\n            method: \"POST\",\n            headers: {\n                ...headers,\n                \"Authorization\": `Bearer ${apiKey}`\n            },\n            body: JSON.stringify({\n                model,\n                message: lastMessage.content,\n                chat_history: chatHistory,\n                temperature,\n                max_tokens: Math.min(maxTokens, 300),\n                stream: false,\n                connectors: []\n            }),\n            signal: controller.signal\n        });\n        clearTimeout(timeoutId);\n        if (!response.ok) {\n            const error = await response.text();\n            throw new Error(`API error: ${response.status} - ${error}`);\n        }\n        const data = await response.json();\n        const content = data.text || \"\";\n        return {\n            success: true,\n            content,\n            usage: {\n                input_tokens: data.meta?.tokens?.input_tokens || 0,\n                output_tokens: data.meta?.tokens?.output_tokens || 0\n            }\n        };\n    } catch (error) {\n        if (error instanceof Error && error.name === \"AbortError\") {\n            return {\n                success: false,\n                error: \"Request timeout - please try again\"\n            };\n        }\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : \"Cohere generation failed\"\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/llm/generate/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/llmProviders.ts":
/*!*********************************!*\
  !*** ./src/lib/llmProviders.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LLM_PROVIDERS_DATABASE: () => (/* binding */ LLM_PROVIDERS_DATABASE),\n/* harmony export */   getActiveProviders: () => (/* binding */ getActiveProviders),\n/* harmony export */   getModelById: () => (/* binding */ getModelById),\n/* harmony export */   getProviderBaseUrl: () => (/* binding */ getProviderBaseUrl),\n/* harmony export */   getProviderById: () => (/* binding */ getProviderById),\n/* harmony export */   getProviderHeaders: () => (/* binding */ getProviderHeaders),\n/* harmony export */   searchProviders: () => (/* binding */ searchProviders)\n/* harmony export */ });\n/**\n * قاعدة بيانات شاملة لمقدمي خدمات LLM\n * تحتوي على معلومات كاملة عن كل مقدم خدمة مع Base URLs والنماذج\n */ const LLM_PROVIDERS_DATABASE = [\n    {\n        id: \"openai\",\n        name: \"OpenAI\",\n        icon: \"\\uD83E\\uDD16\",\n        description: \"GPT models from OpenAI - Industry leading language models\",\n        baseUrl: \"https://api.openai.com/v1\",\n        apiKeyPlaceholder: \"sk-...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 128000,\n        models: [\n            {\n                id: \"gpt-4o\",\n                name: \"GPT-4o\",\n                description: \"Most advanced multimodal model with vision capabilities\",\n                contextLength: 128000,\n                pricing: \"$5/1M input, $15/1M output\",\n                inputPrice: 5,\n                outputPrice: 15\n            },\n            {\n                id: \"gpt-4o-mini\",\n                name: \"GPT-4o Mini\",\n                description: \"Faster and more affordable version of GPT-4o\",\n                contextLength: 128000,\n                pricing: \"$0.15/1M input, $0.6/1M output\",\n                inputPrice: 0.15,\n                outputPrice: 0.6\n            },\n            {\n                id: \"gpt-4-turbo\",\n                name: \"GPT-4 Turbo\",\n                description: \"High performance model with latest knowledge\",\n                contextLength: 128000,\n                pricing: \"$10/1M input, $30/1M output\",\n                inputPrice: 10,\n                outputPrice: 30\n            },\n            {\n                id: \"gpt-4\",\n                name: \"GPT-4\",\n                description: \"Original GPT-4 model with strong reasoning\",\n                contextLength: 8192,\n                pricing: \"$30/1M input, $60/1M output\",\n                inputPrice: 30,\n                outputPrice: 60\n            },\n            {\n                id: \"gpt-3.5-turbo\",\n                name: \"GPT-3.5 Turbo\",\n                description: \"Fast and efficient for most tasks\",\n                contextLength: 16385,\n                pricing: \"$0.5/1M input, $1.5/1M output\",\n                inputPrice: 0.5,\n                outputPrice: 1.5\n            },\n            {\n                id: \"gpt-3.5-turbo-instruct\",\n                name: \"GPT-3.5 Turbo Instruct\",\n                description: \"Instruction-following variant\",\n                contextLength: 4096,\n                pricing: \"$1.5/1M input, $2/1M output\",\n                inputPrice: 1.5,\n                outputPrice: 2\n            }\n        ]\n    },\n    {\n        id: \"anthropic\",\n        name: \"Anthropic\",\n        icon: \"\\uD83E\\uDDE0\",\n        description: \"Claude models from Anthropic - Advanced reasoning capabilities\",\n        baseUrl: \"https://api.anthropic.com/v1\",\n        apiKeyPlaceholder: \"sk-ant-...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 200000,\n        headers: {\n            \"anthropic-version\": \"2023-06-01\"\n        },\n        models: [\n            {\n                id: \"claude-3-5-sonnet-20241022\",\n                name: \"Claude 3.5 Sonnet\",\n                description: \"Most intelligent model\",\n                contextLength: 200000,\n                pricing: \"$3/1M input, $15/1M output\",\n                inputPrice: 3,\n                outputPrice: 15\n            },\n            {\n                id: \"claude-3-5-haiku-20241022\",\n                name: \"Claude 3.5 Haiku\",\n                description: \"Fastest model\",\n                contextLength: 200000,\n                pricing: \"$0.25/1M input, $1.25/1M output\",\n                inputPrice: 0.25,\n                outputPrice: 1.25\n            },\n            {\n                id: \"claude-3-opus-20240229\",\n                name: \"Claude 3 Opus\",\n                description: \"Most powerful model\",\n                contextLength: 200000,\n                pricing: \"$15/1M input, $75/1M output\",\n                inputPrice: 15,\n                outputPrice: 75\n            }\n        ]\n    },\n    {\n        id: \"google\",\n        name: \"Google AI\",\n        icon: \"\\uD83D\\uDD0D\",\n        description: \"Gemini models from Google - Multimodal AI capabilities\",\n        baseUrl: \"https://generativelanguage.googleapis.com/v1beta\",\n        apiKeyPlaceholder: \"AIza...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 2000000,\n        models: [\n            {\n                id: \"gemini-1.5-pro\",\n                name: \"Gemini 1.5 Pro\",\n                description: \"Most advanced model with 2M context window\",\n                contextLength: 2000000,\n                pricing: \"$1.25/1M input, $5/1M output\",\n                inputPrice: 1.25,\n                outputPrice: 5\n            },\n            {\n                id: \"gemini-1.5-flash\",\n                name: \"Gemini 1.5 Flash\",\n                description: \"Fast and efficient with 1M context\",\n                contextLength: 1000000,\n                pricing: \"$0.075/1M input, $0.3/1M output\",\n                inputPrice: 0.075,\n                outputPrice: 0.3\n            },\n            {\n                id: \"gemini-1.5-flash-8b\",\n                name: \"Gemini 1.5 Flash 8B\",\n                description: \"Smaller, faster model for simple tasks\",\n                contextLength: 1000000,\n                pricing: \"$0.0375/1M input, $0.15/1M output\",\n                inputPrice: 0.0375,\n                outputPrice: 0.15\n            },\n            {\n                id: \"gemini-pro\",\n                name: \"Gemini Pro\",\n                description: \"Balanced performance for general use\",\n                contextLength: 32768,\n                pricing: \"$0.5/1M input, $1.5/1M output\",\n                inputPrice: 0.5,\n                outputPrice: 1.5\n            },\n            {\n                id: \"gemini-pro-vision\",\n                name: \"Gemini Pro Vision\",\n                description: \"Multimodal model with vision capabilities\",\n                contextLength: 16000,\n                pricing: \"$0.25/1M input, $0.5/1M output\",\n                inputPrice: 0.25,\n                outputPrice: 0.5\n            }\n        ]\n    },\n    {\n        id: \"openrouter\",\n        name: \"OpenRouter\",\n        icon: \"\\uD83D\\uDD00\",\n        description: \"Access to multiple models via OpenRouter - One API for all models\",\n        baseUrl: \"https://openrouter.ai/api/v1\",\n        apiKeyPlaceholder: \"sk-or-...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 200000,\n        headers: {\n            \"HTTP-Referer\": process.env.NEXT_PUBLIC_SITE_URL || \"http://localhost:3000\",\n            \"X-Title\": \"ContextKit\"\n        },\n        models: [\n            {\n                id: \"openai/gpt-4o\",\n                name: \"GPT-4o (via OpenRouter)\",\n                description: \"OpenAI GPT-4o through OpenRouter\",\n                contextLength: 128000,\n                pricing: \"Variable pricing\"\n            },\n            {\n                id: \"anthropic/claude-3.5-sonnet\",\n                name: \"Claude 3.5 Sonnet (via OpenRouter)\",\n                description: \"Anthropic Claude through OpenRouter\",\n                contextLength: 200000,\n                pricing: \"Variable pricing\"\n            },\n            {\n                id: \"google/gemini-pro-1.5\",\n                name: \"Gemini Pro 1.5 (via OpenRouter)\",\n                description: \"Google Gemini through OpenRouter\",\n                contextLength: 1000000,\n                pricing: \"Variable pricing\"\n            },\n            {\n                id: \"meta-llama/llama-3.1-405b-instruct\",\n                name: \"Llama 3.1 405B (via OpenRouter)\",\n                description: \"Meta Llama through OpenRouter\",\n                contextLength: 131072,\n                pricing: \"Variable pricing\"\n            }\n        ]\n    },\n    {\n        id: \"deepseek\",\n        name: \"DeepSeek\",\n        icon: \"\\uD83C\\uDF0A\",\n        description: \"DeepSeek models - Efficient and cost-effective AI\",\n        baseUrl: \"https://api.deepseek.com/v1\",\n        apiKeyPlaceholder: \"sk-...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 32768,\n        models: [\n            {\n                id: \"deepseek-chat\",\n                name: \"DeepSeek Chat\",\n                description: \"General purpose conversational AI\",\n                contextLength: 32768,\n                pricing: \"$0.14/1M input, $0.28/1M output\",\n                inputPrice: 0.14,\n                outputPrice: 0.28\n            },\n            {\n                id: \"deepseek-coder\",\n                name: \"DeepSeek Coder\",\n                description: \"Specialized for code generation\",\n                contextLength: 16384,\n                pricing: \"$0.14/1M input, $0.28/1M output\",\n                inputPrice: 0.14,\n                outputPrice: 0.28\n            }\n        ]\n    },\n    {\n        id: \"mistral\",\n        name: \"Mistral AI\",\n        icon: \"\\uD83C\\uDF1F\",\n        description: \"Mistral AI - Advanced European AI models with multilingual capabilities\",\n        baseUrl: \"https://api.mistral.ai/v1\",\n        apiKeyPlaceholder: \"mistral_...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 128000,\n        models: [\n            {\n                id: \"mistral-large-latest\",\n                name: \"Mistral Large\",\n                description: \"Most advanced model with superior reasoning\",\n                contextLength: 128000,\n                pricing: \"$2/1M input, $6/1M output\",\n                inputPrice: 2,\n                outputPrice: 6\n            },\n            {\n                id: \"mistral-medium-latest\",\n                name: \"Mistral Medium\",\n                description: \"Balanced performance and cost\",\n                contextLength: 32000,\n                pricing: \"$2.7/1M input, $8.1/1M output\",\n                inputPrice: 2.7,\n                outputPrice: 8.1\n            },\n            {\n                id: \"mistral-small-latest\",\n                name: \"Mistral Small\",\n                description: \"Fast and efficient for simple tasks\",\n                contextLength: 32000,\n                pricing: \"$0.2/1M input, $0.6/1M output\",\n                inputPrice: 0.2,\n                outputPrice: 0.6\n            },\n            {\n                id: \"open-mistral-7b\",\n                name: \"Open Mistral 7B\",\n                description: \"Open source model, fast and efficient\",\n                contextLength: 32000,\n                pricing: \"$0.25/1M input, $0.25/1M output\",\n                inputPrice: 0.25,\n                outputPrice: 0.25\n            },\n            {\n                id: \"open-mixtral-8x7b\",\n                name: \"Open Mixtral 8x7B\",\n                description: \"Mixture of experts model\",\n                contextLength: 32000,\n                pricing: \"$0.7/1M input, $0.7/1M output\",\n                inputPrice: 0.7,\n                outputPrice: 0.7\n            },\n            {\n                id: \"open-mixtral-8x22b\",\n                name: \"Open Mixtral 8x22B\",\n                description: \"Larger mixture of experts model\",\n                contextLength: 64000,\n                pricing: \"$2/1M input, $6/1M output\",\n                inputPrice: 2,\n                outputPrice: 6\n            },\n            {\n                id: \"mistral-embed\",\n                name: \"Mistral Embed\",\n                description: \"Embedding model for semantic search\",\n                contextLength: 8192,\n                pricing: \"$0.1/1M tokens\",\n                inputPrice: 0.1,\n                outputPrice: 0\n            }\n        ]\n    },\n    {\n        id: \"cohere\",\n        name: \"Cohere\",\n        icon: \"\\uD83E\\uDDEE\",\n        description: \"Cohere - Enterprise-grade language models with strong multilingual support\",\n        baseUrl: \"https://api.cohere.ai/v1\",\n        apiKeyPlaceholder: \"co_...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 128000,\n        models: [\n            {\n                id: \"command-r-plus\",\n                name: \"Command R+\",\n                description: \"Most advanced model for complex reasoning and RAG\",\n                contextLength: 128000,\n                pricing: \"$3/1M input, $15/1M output\",\n                inputPrice: 3,\n                outputPrice: 15\n            },\n            {\n                id: \"command-r\",\n                name: \"Command R\",\n                description: \"Balanced model for general use and RAG\",\n                contextLength: 128000,\n                pricing: \"$0.5/1M input, $1.5/1M output\",\n                inputPrice: 0.5,\n                outputPrice: 1.5\n            },\n            {\n                id: \"command\",\n                name: \"Command\",\n                description: \"Versatile model for various tasks\",\n                contextLength: 4096,\n                pricing: \"$1/1M input, $2/1M output\",\n                inputPrice: 1,\n                outputPrice: 2\n            },\n            {\n                id: \"command-light\",\n                name: \"Command Light\",\n                description: \"Fast and efficient for simple tasks\",\n                contextLength: 4096,\n                pricing: \"$0.3/1M input, $0.6/1M output\",\n                inputPrice: 0.3,\n                outputPrice: 0.6\n            },\n            {\n                id: \"command-nightly\",\n                name: \"Command Nightly\",\n                description: \"Latest experimental features\",\n                contextLength: 4096,\n                pricing: \"$1/1M input, $2/1M output\",\n                inputPrice: 1,\n                outputPrice: 2\n            }\n        ]\n    },\n    {\n        id: \"groq\",\n        name: \"Groq\",\n        icon: \"⚡\",\n        description: \"Groq - Ultra-fast inference with GroqChip technology\",\n        baseUrl: \"https://api.groq.com/openai/v1\",\n        apiKeyPlaceholder: \"gsk_...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 32768,\n        models: [\n            {\n                id: \"llama-3.1-70b-versatile\",\n                name: \"Llama 3.1 70B\",\n                description: \"Meta Llama 3.1 70B on Groq\",\n                contextLength: 131072,\n                pricing: \"$0.59/1M input, $0.79/1M output\",\n                inputPrice: 0.59,\n                outputPrice: 0.79\n            },\n            {\n                id: \"llama-3.1-8b-instant\",\n                name: \"Llama 3.1 8B\",\n                description: \"Meta Llama 3.1 8B on Groq\",\n                contextLength: 131072,\n                pricing: \"$0.05/1M input, $0.08/1M output\",\n                inputPrice: 0.05,\n                outputPrice: 0.08\n            },\n            {\n                id: \"mixtral-8x7b-32768\",\n                name: \"Mixtral 8x7B\",\n                description: \"Mistral Mixtral 8x7B on Groq\",\n                contextLength: 32768,\n                pricing: \"$0.24/1M input, $0.24/1M output\",\n                inputPrice: 0.24,\n                outputPrice: 0.24\n            }\n        ]\n    }\n];\n/**\n * الحصول على مقدم خدمة بواسطة ID\n */ function getProviderById(id) {\n    return LLM_PROVIDERS_DATABASE.find((provider)=>provider.id === id);\n}\n/**\n * الحصول على جميع مقدمي الخدمة النشطين\n */ function getActiveProviders() {\n    return LLM_PROVIDERS_DATABASE.filter((provider)=>provider.isActive);\n}\n/**\n * الحصول على نموذج بواسطة provider ID و model ID\n */ function getModelById(providerId, modelId) {\n    const provider = getProviderById(providerId);\n    return provider?.models.find((model)=>model.id === modelId);\n}\n/**\n * البحث عن مقدمي الخدمة\n */ function searchProviders(query) {\n    const lowercaseQuery = query.toLowerCase();\n    return LLM_PROVIDERS_DATABASE.filter((provider)=>provider.name.toLowerCase().includes(lowercaseQuery) || provider.description.toLowerCase().includes(lowercaseQuery) || provider.models.some((model)=>model.name.toLowerCase().includes(lowercaseQuery) || model.description.toLowerCase().includes(lowercaseQuery)));\n}\n/**\n * تحديد Base URL التلقائي لمقدم الخدمة\n */ function getProviderBaseUrl(providerId) {\n    const provider = getProviderById(providerId);\n    return provider?.baseUrl || \"\";\n}\n/**\n * الحصول على Headers المطلوبة لمقدم الخدمة\n */ function getProviderHeaders(providerId) {\n    const provider = getProviderById(providerId);\n    const baseHeaders = provider?.headers || {};\n    // إضافة headers خاصة لكل مزود\n    switch(providerId){\n        case \"anthropic\":\n            return {\n                ...baseHeaders,\n                \"anthropic-version\": \"2023-06-01\"\n            };\n        case \"cohere\":\n            return {\n                ...baseHeaders,\n                \"Cohere-Version\": \"2022-12-06\"\n            };\n        case \"mistral\":\n            return {\n                ...baseHeaders,\n                \"User-Agent\": \"ContextKit/1.0\"\n            };\n        default:\n            return baseHeaders;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/llmProviders.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fllm%2Fgenerate%2Froute&page=%2Fapi%2Fllm%2Fgenerate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fllm%2Fgenerate%2Froute.ts&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();