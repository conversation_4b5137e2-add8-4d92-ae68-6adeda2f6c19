// دوال تصدير خارطة الطريق

interface RoadmapPhase {
  id: string;
  title: string;
  titleAr: string;
  description: string;
  descriptionAr: string;
  duration: string;
  durationAr: string;
  tasks: RoadmapTask[];
  priority: 'high' | 'medium' | 'low';
  dependencies?: string[];
}

interface RoadmapTask {
  id: string;
  title: string;
  titleAr: string;
  description: string;
  descriptionAr: string;
  estimatedHours: number;
  skills: string[];
  priority: 'high' | 'medium' | 'low';
}

interface ProjectData {
  projectDefinition: any;
  contextMap: any;
  emotionalTone: any;
  technicalLayer: any;
  legalRisk: any;
}

export const exportRoadmapAsText = (
  phases: RoadmapPhase[], 
  projectData: ProjectData, 
  isArabic: boolean = false
): string => {
  const title = isArabic ? 'خارطة طريق المشروع' : 'Project Roadmap';
  const projectName = projectData.projectDefinition.name || (isArabic ? 'مشروع غير محدد' : 'Unnamed Project');
  
  let content = `# ${title}\n\n`;
  content += `**${isArabic ? 'اسم المشروع' : 'Project Name'}:** ${projectName}\n`;
  content += `**${isArabic ? 'تاريخ الإنشاء' : 'Generated Date'}:** ${new Date().toLocaleDateString(isArabic ? 'ar-SA' : 'en-US')}\n\n`;

  // ملخص المشروع
  content += `## ${isArabic ? 'ملخص المشروع' : 'Project Summary'}\n\n`;
  
  if (projectData.projectDefinition.purpose) {
    content += `**${isArabic ? 'الهدف' : 'Purpose'}:** ${projectData.projectDefinition.purpose}\n\n`;
  }
  
  if (projectData.projectDefinition.targetUsers) {
    content += `**${isArabic ? 'المستخدمون المستهدفون' : 'Target Users'}:** ${projectData.projectDefinition.targetUsers}\n\n`;
  }

  // التفاصيل التقنية
  if (projectData.projectDefinition.projectType) {
    content += `**${isArabic ? 'نوع المشروع' : 'Project Type'}:** ${projectData.projectDefinition.projectType}\n`;
  }
  
  if (projectData.projectDefinition.primaryLanguages?.length > 0) {
    content += `**${isArabic ? 'لغات البرمجة' : 'Programming Languages'}:** ${projectData.projectDefinition.primaryLanguages.join(', ')}\n`;
  }
  
  if (projectData.projectDefinition.targetPlatforms?.length > 0) {
    content += `**${isArabic ? 'المنصات المستهدفة' : 'Target Platforms'}:** ${projectData.projectDefinition.targetPlatforms.join(', ')}\n`;
  }

  content += '\n';

  // حساب التقديرات الإجمالية
  let totalHours = 0;
  phases.forEach(phase => {
    phase.tasks.forEach(task => {
      totalHours += task.estimatedHours;
    });
  });

  content += `## ${isArabic ? 'التقديرات الإجمالية' : 'Overall Estimates'}\n\n`;
  content += `- **${isArabic ? 'عدد المراحل' : 'Total Phases'}:** ${phases.length}\n`;
  content += `- **${isArabic ? 'إجمالي الساعات المقدرة' : 'Total Estimated Hours'}:** ${totalHours}\n`;
  content += `- **${isArabic ? 'المدة المتوقعة' : 'Expected Duration'}:** ${Math.ceil(totalHours / 40)} ${isArabic ? 'أسابيع' : 'weeks'}\n\n`;

  // المراحل التفصيلية
  content += `## ${isArabic ? 'المراحل التفصيلية' : 'Detailed Phases'}\n\n`;

  phases.forEach((phase, index) => {
    content += `### ${index + 1}. ${isArabic ? phase.titleAr : phase.title}\n\n`;
    content += `**${isArabic ? 'الوصف' : 'Description'}:** ${isArabic ? phase.descriptionAr : phase.description}\n`;
    content += `**${isArabic ? 'المدة المقدرة' : 'Estimated Duration'}:** ${isArabic ? phase.durationAr : phase.duration}\n`;
    content += `**${isArabic ? 'الأولوية' : 'Priority'}:** ${phase.priority}\n\n`;

    if (phase.dependencies && phase.dependencies.length > 0) {
      content += `**${isArabic ? 'يعتمد على' : 'Dependencies'}:** ${phase.dependencies.join(', ')}\n\n`;
    }

    content += `#### ${isArabic ? 'المهام' : 'Tasks'}:\n\n`;
    
    phase.tasks.forEach((task, taskIndex) => {
      content += `${taskIndex + 1}. **${isArabic ? task.titleAr : task.title}**\n`;
      content += `   - ${isArabic ? task.descriptionAr : task.description}\n`;
      content += `   - ${isArabic ? 'الساعات المقدرة' : 'Estimated Hours'}: ${task.estimatedHours}\n`;
      content += `   - ${isArabic ? 'المهارات المطلوبة' : 'Required Skills'}: ${task.skills.join(', ')}\n`;
      content += `   - ${isArabic ? 'الأولوية' : 'Priority'}: ${task.priority}\n\n`;
    });

    content += '\n';
  });

  // التوصيات
  content += `## ${isArabic ? 'التوصيات' : 'Recommendations'}\n\n`;
  
  if (projectData.projectDefinition.complexity === 'enterprise') {
    content += `- ${isArabic ? 'نظراً لطبيعة المشروع المؤسسية، يُنصح بتخصيص وقت إضافي لمراجعة الأمان والجودة' : 'Given the enterprise nature of the project, allocate additional time for security and quality reviews'}\n`;
  }
  
  if (projectData.projectDefinition.teamSize === 'solo') {
    content += `- ${isArabic ? 'كمطور منفرد، فكر في استخدام أدوات التطوير السريع والمكتبات الجاهزة' : 'As a solo developer, consider using rapid development tools and ready-made libraries'}\n`;
  }
  
  if (projectData.technicalLayer.scalingStrategy) {
    content += `- ${isArabic ? 'تأكد من تطبيق استراتيجية التوسع المحددة منذ البداية' : 'Ensure the specified scaling strategy is implemented from the beginning'}\n`;
  }

  content += `\n---\n\n`;
  content += `${isArabic ? 'تم إنشاء هذه الخارطة بواسطة Craftery - منصة بناء الأفكار المدعومة بالذكاء الاصطناعي' : 'Generated by Craftery - AI-powered Idea Builder Platform'}\n`;

  return content;
};

export const exportRoadmapAsJSON = (
  phases: RoadmapPhase[], 
  projectData: ProjectData
): string => {
  const roadmapData = {
    metadata: {
      projectName: projectData.projectDefinition.name || 'Unnamed Project',
      generatedDate: new Date().toISOString(),
      version: '1.0',
      generator: 'Craftery AI-powered Idea Builder'
    },
    projectSummary: {
      purpose: projectData.projectDefinition.purpose,
      targetUsers: projectData.projectDefinition.targetUsers,
      goals: projectData.projectDefinition.goals,
      projectType: projectData.projectDefinition.projectType,
      complexity: projectData.projectDefinition.complexity,
      targetPlatforms: projectData.projectDefinition.targetPlatforms,
      primaryLanguages: projectData.projectDefinition.primaryLanguages,
      teamSize: projectData.projectDefinition.teamSize,
      budget: projectData.projectDefinition.budget,
      deploymentType: projectData.projectDefinition.deploymentType
    },
    technicalDetails: {
      architecturePattern: projectData.technicalLayer.architecturePattern,
      scalingStrategy: projectData.technicalLayer.scalingStrategy,
      securityRequirements: projectData.technicalLayer.securityRequirements,
      performanceTargets: projectData.technicalLayer.performanceTargets,
      integrationNeeds: projectData.technicalLayer.integrationNeeds,
      monitoringTools: projectData.technicalLayer.monitoringTools
    },
    estimates: {
      totalPhases: phases.length,
      totalHours: phases.reduce((total, phase) => 
        total + phase.tasks.reduce((phaseTotal, task) => phaseTotal + task.estimatedHours, 0), 0
      ),
      estimatedWeeks: Math.ceil(
        phases.reduce((total, phase) => 
          total + phase.tasks.reduce((phaseTotal, task) => phaseTotal + task.estimatedHours, 0), 0
        ) / 40
      )
    },
    phases: phases.map((phase, index) => ({
      order: index + 1,
      id: phase.id,
      title: {
        en: phase.title,
        ar: phase.titleAr
      },
      description: {
        en: phase.description,
        ar: phase.descriptionAr
      },
      duration: {
        en: phase.duration,
        ar: phase.durationAr
      },
      priority: phase.priority,
      dependencies: phase.dependencies || [],
      tasks: phase.tasks.map((task, taskIndex) => ({
        order: taskIndex + 1,
        id: task.id,
        title: {
          en: task.title,
          ar: task.titleAr
        },
        description: {
          en: task.description,
          ar: task.descriptionAr
        },
        estimatedHours: task.estimatedHours,
        skills: task.skills,
        priority: task.priority
      }))
    }))
  };

  return JSON.stringify(roadmapData, null, 2);
};

export const downloadRoadmap = (content: string, filename: string, type: 'text' | 'json' = 'text') => {
  const mimeType = type === 'json' ? 'application/json' : 'text/plain';
  const blob = new Blob([content], { type: `${mimeType};charset=utf-8` });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

export const copyToClipboard = async (content: string): Promise<boolean> => {
  try {
    await navigator.clipboard.writeText(content);
    return true;
  } catch (err) {
    // Fallback for older browsers
    const textArea = document.createElement('textarea');
    textArea.value = content;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
      document.execCommand('copy');
      document.body.removeChild(textArea);
      return true;
    } catch (err) {
      document.body.removeChild(textArea);
      return false;
    }
  }
};
