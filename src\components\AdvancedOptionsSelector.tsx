'use client';

import { useState } from 'react';
import { useContextStore } from '@/store/contextStore';
import { ProjectOption } from '@/lib/projectOptions';
import { ChevronDown, ChevronUp, Check } from 'lucide-react';

interface AdvancedOptionsSelectorProps {
  title: string;
  titleAr: string;
  options: ProjectOption[];
  selectedValues: string[];
  onSelectionChange: (values: string[]) => void;
  multiSelect?: boolean;
  placeholder?: string;
  placeholderAr?: string;
}

export default function AdvancedOptionsSelector({
  title,
  titleAr,
  options,
  selectedValues,
  onSelectionChange,
  multiSelect = false,
  placeholder,
  placeholderAr
}: AdvancedOptionsSelectorProps) {
  const { currentLanguage } = useContextStore();
  const [isOpen, setIsOpen] = useState(false);
  const isArabic = currentLanguage === 'ar';

  const handleOptionClick = (optionId: string) => {
    if (multiSelect) {
      const newValues = selectedValues.includes(optionId)
        ? selectedValues.filter(id => id !== optionId)
        : [...selectedValues, optionId];
      onSelectionChange(newValues);
    } else {
      onSelectionChange([optionId]);
      setIsOpen(false);
    }
  };

  const getDisplayText = () => {
    if (selectedValues.length === 0) {
      return isArabic ? (placeholderAr || 'اختر خيار...') : (placeholder || 'Select option...');
    }

    if (selectedValues.length === 1) {
      const option = options.find(opt => opt.id === selectedValues[0]);
      return option ? (isArabic ? option.labelAr : option.label) : '';
    }

    if (multiSelect && selectedValues.length > 1) {
      return isArabic 
        ? `${selectedValues.length} خيارات محددة`
        : `${selectedValues.length} options selected`;
    }

    return '';
  };

  return (
    <div className="space-y-2">
      <label className={`block text-sm font-medium text-gray-700 dark:text-gray-300 ${isArabic ? 'text-right' : 'text-left'}`}>
        {isArabic ? titleAr : title}
      </label>
      
      <div className="relative">
        <button
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          className={`
            w-full px-4 py-3 text-left bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 
            rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
            hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200
            ${isArabic ? 'text-right' : 'text-left'}
          `}
        >
          <div className="flex items-center justify-between">
            <span className={`text-gray-900 dark:text-gray-100 ${selectedValues.length === 0 ? 'text-gray-500 dark:text-gray-400' : ''}`}>
              {getDisplayText()}
            </span>
            {isOpen ? (
              <ChevronUp className="h-5 w-5 text-gray-400" />
            ) : (
              <ChevronDown className="h-5 w-5 text-gray-400" />
            )}
          </div>
        </button>

        {isOpen && (
          <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-60 overflow-auto">
            {options.map((option) => (
              <div
                key={option.id}
                onClick={() => handleOptionClick(option.id)}
                className={`
                  px-4 py-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 
                  transition-colors duration-150 border-b border-gray-100 dark:border-gray-700 last:border-b-0
                  ${selectedValues.includes(option.id) ? 'bg-blue-50 dark:bg-blue-900/20' : ''}
                  ${isArabic ? 'text-right' : 'text-left'}
                `}
              >
                <div className="flex items-center justify-between">
                  <div className={`flex-1 ${isArabic ? 'text-right' : 'text-left'}`}>
                    <div className="flex items-center gap-2">
                      {option.icon && <span className="text-lg">{option.icon}</span>}
                      <div>
                        <div className="font-medium text-gray-900 dark:text-gray-100">
                          {isArabic ? option.labelAr : option.label}
                        </div>
                        {(option.description || option.descriptionAr) && (
                          <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                            {isArabic ? option.descriptionAr : option.description}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  {selectedValues.includes(option.id) && (
                    <Check className="h-5 w-5 text-blue-600 dark:text-blue-400 flex-shrink-0" />
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
