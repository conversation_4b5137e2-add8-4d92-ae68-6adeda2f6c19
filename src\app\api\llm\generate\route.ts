import { NextRequest, NextResponse } from 'next/server';
import { getProviderById, getProviderHeaders } from '@/lib/llmProviders';

/**
 * API للتوليد الذكي باستخدام مقدمي خدمات LLM المختلفين
 */
export async function POST(request: NextRequest) {
  try {
    const { 
      providerId, 
      apiKey, 
      model, 
      messages, 
      context, 
      fieldName, 
      language = 'ar',
      temperature = 0.7,
      maxTokens = 1000,
      baseUrl 
    } = await request.json();

    if (!providerId || !apiKey || !messages) {
      return NextResponse.json(
        { error: 'Provider ID, API key, and messages are required' },
        { status: 400 }
      );
    }

    const provider = getProviderById(providerId);
    if (!provider) {
      return NextResponse.json(
        { error: 'Unknown provider' },
        { status: 400 }
      );
    }

    const finalBaseUrl = baseUrl || provider.baseUrl;
    const headers = {
      'Content-Type': 'application/json',
      ...getProviderHeaders(providerId)
    };

    // إنشاء system prompt ذكي بناءً على السياق
    const systemPrompt = createSmartSystemPrompt(context, fieldName, language);
    const finalMessages = [
      { role: 'system', content: systemPrompt },
      ...messages
    ];

    let response;

    switch (providerId) {
      case 'openai':
      case 'openrouter':
      case 'deepseek':
      case 'groq':
      case 'mistral':
        response = await generateOpenAICompatible(
          finalBaseUrl,
          apiKey,
          headers,
          model,
          finalMessages,
          temperature,
          maxTokens
        );
        break;
      case 'anthropic':
        response = await generateAnthropic(
          finalBaseUrl,
          apiKey,
          headers,
          model,
          finalMessages,
          temperature,
          maxTokens
        );
        break;
      case 'google':
        response = await generateGoogle(
          finalBaseUrl,
          apiKey,
          headers,
          model,
          finalMessages,
          temperature,
          maxTokens
        );
        break;
      case 'cohere':
        response = await generateCohere(
          finalBaseUrl,
          apiKey,
          headers,
          model,
          finalMessages,
          temperature,
          maxTokens
        );
        break;
      default:
        response = await generateOpenAICompatible(
          finalBaseUrl,
          apiKey,
          headers,
          model,
          finalMessages,
          temperature,
          maxTokens
        );
    }

    return NextResponse.json(response);
  } catch (error) {
    console.error('Generation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

function createSmartSystemPrompt(context: any, fieldName: string, language: string): string {
  const isArabic = language === 'ar';

  const basePrompt = isArabic
    ? `أنت مساعد ذكي متخصص في مساعدة المستخدمين في بناء سياق منظم ومفصل للمشاريع التقنية والإبداعية.`
    : `You are an AI assistant specialized in helping users build structured and detailed context for technical and creative projects.`;

  // تقليل حجم السياق - أخذ المعلومات المهمة فقط
  const relevantContext = extractRelevantContext(context, fieldName);
  const contextInfo = isArabic
    ? `معلومات المشروع: ${relevantContext}`
    : `Project info: ${relevantContext}`;

  const fieldGuidance = getFieldGuidance(fieldName, isArabic);

  const instructions = isArabic
    ? `
تعليمات:
1. قدم إجابة مختصرة ومفيدة (2-3 جمل)
2. استخدم المعلومات المتوفرة لتحسين إجابتك
3. اجعل الإجابة عملية وقابلة للتطبيق
4. استخدم اللغة العربية الواضحة
`
    : `
Instructions:
1. Provide a concise and helpful response (2-3 sentences)
2. Use available information to improve your answer
3. Make the response practical and actionable
4. Use clear language
`;

  return `${basePrompt}\n\n${contextInfo}\n\n${fieldGuidance}\n\n${instructions}`;
}

function extractRelevantContext(context: any, fieldName: string): string {
  if (!context) return '';

  const contextParts: string[] = [];

  // تحليل المرحلة الحالية من المشروع
  const projectStage = analyzeProjectStage(context);
  contextParts.push(`Project Stage: ${projectStage}`);

  // معلومات المشروع الأساسية
  if (context?.projectDefinition) {
    const pd = context.projectDefinition;
    if (pd.name) contextParts.push(`Name: ${pd.name}`);
    if (pd.purpose) contextParts.push(`Purpose: ${pd.purpose.substring(0, 120)}...`);
    if (pd.targetUsers) contextParts.push(`Users: ${pd.targetUsers.substring(0, 80)}...`);
    if (pd.goals) contextParts.push(`Goals: ${pd.goals.substring(0, 80)}...`);

    // خيارات التخصيص المتقدمة
    if (pd.projectType) contextParts.push(`Type: ${pd.projectType}`);
    if (pd.projectNature) contextParts.push(`Nature: ${pd.projectNature}`);
    if (pd.geographicRegion) contextParts.push(`Region: ${pd.geographicRegion}`);
    if (pd.primaryLanguages && pd.primaryLanguages.length > 0) {
      contextParts.push(`Tech: ${pd.primaryLanguages.join(', ')}`);
    }
    if (pd.complexity) contextParts.push(`Complexity: ${pd.complexity}`);
    if (pd.deploymentType) contextParts.push(`Deployment: ${pd.deploymentType}`);
  }

  // سياق تقني إذا كان الحقل تقنياً
  if (isFieldTechnical(fieldName) && context?.technicalLayer) {
    const tl = context.technicalLayer;
    if (tl.programmingLanguages) contextParts.push(`Tech: ${tl.programmingLanguages.substring(0, 60)}...`);
    if (tl.frameworks) contextParts.push(`Frameworks: ${tl.frameworks.substring(0, 60)}...`);
  }

  // سياق عاطفي إذا كان الحقل متعلقاً بالتجربة
  if (isFieldEmotional(fieldName) && context?.emotionalTone) {
    const et = context.emotionalTone;
    if (et.personality) contextParts.push(`Personality: ${et.personality.substring(0, 60)}...`);
    if (et.communicationStyle) contextParts.push(`Style: ${et.communicationStyle.substring(0, 60)}...`);
  }

  return contextParts.length > 0 ? contextParts.join(' | ') : 'New Project';
}

function analyzeProjectStage(context: any): string {
  let completedSections = 0;
  let totalSections = 5;

  if (context?.projectDefinition && Object.values(context.projectDefinition).some(v => v)) completedSections++;
  if (context?.contextMap && Object.values(context.contextMap).some(v => v)) completedSections++;
  if (context?.emotionalTone && Object.values(context.emotionalTone).some(v => v)) completedSections++;
  if (context?.technicalLayer && Object.values(context.technicalLayer).some(v => v)) completedSections++;
  if (context?.legalRisk && Object.values(context.legalRisk).some(v => v)) completedSections++;

  const percentage = (completedSections / totalSections) * 100;

  if (percentage < 20) return 'Initial Planning';
  else if (percentage < 50) return 'Context Building';
  else if (percentage < 80) return 'Detail Definition';
  else return 'Final Review';
}

function isFieldTechnical(fieldName: string): boolean {
  return ['programmingLanguages', 'frameworks', 'llmModels', 'databases', 'apis', 'infrastructure'].includes(fieldName);
}

function isFieldEmotional(fieldName: string): boolean {
  return ['personality', 'communicationStyle', 'userExperience', 'brandVoice', 'emotionalIntelligence', 'interactionFlow'].includes(fieldName);
}

function getFieldGuidance(fieldName: string, isArabic: boolean): string {
  const fieldGuidanceMap: Record<string, { ar: string; en: string }> = {
    name: {
      ar: 'المجال المطلوب: اسم المشروع - قدم اقتراحات لأسماء إبداعية ومناسبة للمشروع',
      en: 'Required field: Project name - Provide suggestions for creative and suitable project names'
    },
    purpose: {
      ar: 'المجال المطلوب: الغرض من المشروع - اشرح الهدف الرئيسي والقيمة المضافة',
      en: 'Required field: Project purpose - Explain the main goal and added value'
    },
    targetUsers: {
      ar: 'المجال المطلوب: المستخدمون المستهدفون - حدد الجمهور المستهدف بدقة',
      en: 'Required field: Target users - Define the target audience precisely'
    },
    goals: {
      ar: 'المجال المطلوب: الأهداف - حدد أهداف واضحة وقابلة للقياس',
      en: 'Required field: Goals - Define clear and measurable objectives'
    },
    scope: {
      ar: 'المجال المطلوب: نطاق المشروع - حدد حدود وإمكانيات المشروع',
      en: 'Required field: Project scope - Define project boundaries and capabilities'
    },
    timeline: {
      ar: 'المجال المطلوب: الجدول الزمني - اقترح خطة زمنية واقعية',
      en: 'Required field: Timeline - Suggest a realistic time plan'
    },
    programmingLanguages: {
      ar: 'المجال المطلوب: لغات البرمجة - اقترح أفضل لغات البرمجة للمشروع',
      en: 'Required field: Programming languages - Suggest the best programming languages for the project'
    },
    frameworks: {
      ar: 'المجال المطلوب: الأطر التقنية - اقترح أفضل الأطر والمكتبات',
      en: 'Required field: Frameworks - Suggest the best frameworks and libraries'
    },
    databases: {
      ar: 'المجال المطلوب: قواعد البيانات - اقترح أنسب قواعد البيانات',
      en: 'Required field: Databases - Suggest the most suitable databases'
    }
  };

  const guidance = fieldGuidanceMap[fieldName];
  if (guidance) {
    return isArabic ? guidance.ar : guidance.en;
  }

  return isArabic 
    ? `المجال المطلوب: ${fieldName} - قدم محتوى مفيد ومناسب لهذا المجال`
    : `Required field: ${fieldName} - Provide helpful and appropriate content for this field`;
}

async function generateOpenAICompatible(
  baseUrl: string,
  apiKey: string,
  headers: Record<string, string>,
  model: string,
  messages: any[],
  temperature: number,
  maxTokens: number
) {
  try {
    // إضافة timeout للطلب
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 ثانية timeout

    const response = await fetch(`${baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        ...headers,
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model,
        messages,
        temperature,
        max_tokens: Math.min(maxTokens, 300), // تقليل maxTokens للسرعة
        stream: false // تأكد من عدم استخدام streaming
      }),
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`API error: ${response.status} - ${error}`);
    }

    const data = await response.json();
    const content = data.choices?.[0]?.message?.content || '';

    return {
      success: true,
      content,
      usage: data.usage
    };
  } catch (error) {
    if (error instanceof Error && error.name === 'AbortError') {
      return {
        success: false,
        error: 'Request timeout - please try again'
      };
    }
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

async function generateAnthropic(
  baseUrl: string,
  apiKey: string,
  headers: Record<string, string>,
  model: string,
  messages: any[],
  temperature: number,
  maxTokens: number
) {
  try {
    // Convert OpenAI format to Anthropic format
    const anthropicMessages = messages.filter(m => m.role !== 'system');
    const systemMessage = messages.find(m => m.role === 'system')?.content || '';

    const response = await fetch(`${baseUrl}/messages`, {
      method: 'POST',
      headers: {
        ...headers,
        'x-api-key': apiKey
      },
      body: JSON.stringify({
        model,
        max_tokens: maxTokens,
        temperature,
        system: systemMessage,
        messages: anthropicMessages
      })
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`API error: ${response.status} - ${error}`);
    }

    const data = await response.json();
    const content = data.content?.[0]?.text || '';

    return {
      success: true,
      content,
      usage: data.usage
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

async function generateGoogle(
  baseUrl: string,
  apiKey: string,
  headers: Record<string, string>,
  model: string,
  messages: any[],
  temperature: number,
  maxTokens: number
) {
  try {
    // إضافة timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000);

    // Convert to Google format
    const contents = messages.map(msg => ({
      role: msg.role === 'assistant' ? 'model' : 'user',
      parts: [{ text: msg.content }]
    }));

    const response = await fetch(`${baseUrl}/models/${model}:generateContent?key=${apiKey}`, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        contents,
        generationConfig: {
          temperature,
          maxOutputTokens: Math.min(maxTokens, 300), // تقليل الحد الأقصى
          candidateCount: 1 // طلب مرشح واحد فقط للسرعة
        }
      }),
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`API error: ${response.status} - ${error}`);
    }

    const data = await response.json();
    const content = data.candidates?.[0]?.content?.parts?.[0]?.text || '';

    return {
      success: true,
      content,
      usage: data.usageMetadata
    };
  } catch (error) {
    if (error instanceof Error && error.name === 'AbortError') {
      return {
        success: false,
        error: 'Request timeout - please try again'
      };
    }
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

async function generateCohere(
  baseUrl: string,
  apiKey: string,
  headers: Record<string, string>,
  model: string,
  messages: any[],
  temperature: number,
  maxTokens: number
) {
  try {
    // إضافة timeout للطلب
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000);

    // تحويل messages إلى تنسيق Cohere
    const lastMessage = messages[messages.length - 1];
    const chatHistory = messages.slice(0, -1).map(msg => ({
      role: msg.role === 'assistant' ? 'CHATBOT' : 'USER',
      message: msg.content
    }));

    const response = await fetch(`${baseUrl}/chat`, {
      method: 'POST',
      headers: {
        ...headers,
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model,
        message: lastMessage.content,
        chat_history: chatHistory,
        temperature,
        max_tokens: Math.min(maxTokens, 300),
        stream: false,
        connectors: []
      }),
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`API error: ${response.status} - ${error}`);
    }

    const data = await response.json();
    const content = data.text || '';

    return {
      success: true,
      content,
      usage: {
        input_tokens: data.meta?.tokens?.input_tokens || 0,
        output_tokens: data.meta?.tokens?.output_tokens || 0
      }
    };
  } catch (error) {
    if (error instanceof Error && error.name === 'AbortError') {
      return {
        success: false,
        error: 'Request timeout - please try again'
      };
    }
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Cohere generation failed'
    };
  }
}
