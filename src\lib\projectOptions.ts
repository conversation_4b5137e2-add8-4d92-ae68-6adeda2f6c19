// خيارات التخصيص المتقدمة للمشاريع

export interface ProjectOption {
  id: string;
  label: string;
  labelAr: string;
  description?: string;
  descriptionAr?: string;
  icon?: string;
}

// أنواع المشاريع
export const PROJECT_TYPES: ProjectOption[] = [
  {
    id: 'web-app',
    label: 'Web Application',
    labelAr: 'تطبيق ويب',
    description: 'Browser-based application accessible via web browsers',
    descriptionAr: 'تطبيق يعمل في المتصفح ويمكن الوصول إليه عبر متصفحات الويب',
    icon: '🌐'
  },
  {
    id: 'mobile-app',
    label: 'Mobile Application',
    labelAr: 'تطبيق جوال',
    description: 'Native or cross-platform mobile application',
    descriptionAr: 'تطبيق جوال أصلي أو متعدد المنصات',
    icon: '📱'
  },
  {
    id: 'desktop-app',
    label: 'Desktop Application',
    labelAr: 'تطبيق سطح المكتب',
    description: 'Native desktop application for Windows, macOS, or Linux',
    descriptionAr: 'تطبيق سطح مكتب أصلي لـ Windows أو macOS أو Linux',
    icon: '💻'
  },
  {
    id: 'api-service',
    label: 'API/Microservice',
    labelAr: 'خدمة API/مايكروسيرفس',
    description: 'Backend API or microservice architecture',
    descriptionAr: 'خدمة API خلفية أو هندسة مايكروسيرفس',
    icon: '🔌'
  },
  {
    id: 'ai-model',
    label: 'AI/ML Model',
    labelAr: 'نموذج ذكاء اصطناعي',
    description: 'Machine learning model or AI system',
    descriptionAr: 'نموذج تعلم آلي أو نظام ذكاء اصطناعي',
    icon: '🤖'
  },
  {
    id: 'chatbot',
    label: 'Chatbot/Virtual Assistant',
    labelAr: 'شات بوت/مساعد افتراضي',
    description: 'Conversational AI or chatbot system',
    descriptionAr: 'نظام ذكاء اصطناعي محادثة أو شات بوت',
    icon: '💬'
  },
  {
    id: 'data-analytics',
    label: 'Data Analytics Platform',
    labelAr: 'منصة تحليل البيانات',
    description: 'Data processing and analytics solution',
    descriptionAr: 'حل معالجة وتحليل البيانات',
    icon: '📊'
  },
  {
    id: 'iot-system',
    label: 'IoT System',
    labelAr: 'نظام إنترنت الأشياء',
    description: 'Internet of Things connected system',
    descriptionAr: 'نظام متصل بإنترنت الأشياء',
    icon: '🌐'
  }
];

// المنصات المستهدفة
export const TARGET_PLATFORMS: ProjectOption[] = [
  {
    id: 'web-browsers',
    label: 'Web Browsers',
    labelAr: 'متصفحات الويب',
    description: 'Chrome, Firefox, Safari, Edge',
    descriptionAr: 'كروم، فايرفوكس، سفاري، إيدج',
    icon: '🌐'
  },
  {
    id: 'android',
    label: 'Android',
    labelAr: 'أندرويد',
    description: 'Android mobile devices',
    descriptionAr: 'أجهزة أندرويد المحمولة',
    icon: '🤖'
  },
  {
    id: 'ios',
    label: 'iOS',
    labelAr: 'iOS',
    description: 'iPhone and iPad devices',
    descriptionAr: 'أجهزة آيفون وآيباد',
    icon: '🍎'
  },
  {
    id: 'windows',
    label: 'Windows',
    labelAr: 'ويندوز',
    description: 'Windows desktop and server',
    descriptionAr: 'سطح مكتب وخادم ويندوز',
    icon: '🪟'
  },
  {
    id: 'macos',
    label: 'macOS',
    labelAr: 'macOS',
    description: 'Apple macOS desktop',
    descriptionAr: 'سطح مكتب آبل macOS',
    icon: '🍎'
  },
  {
    id: 'linux',
    label: 'Linux',
    labelAr: 'لينكس',
    description: 'Linux distributions',
    descriptionAr: 'توزيعات لينكس',
    icon: '🐧'
  },
  {
    id: 'cloud',
    label: 'Cloud Platforms',
    labelAr: 'المنصات السحابية',
    description: 'AWS, Azure, Google Cloud',
    descriptionAr: 'AWS، Azure، Google Cloud',
    icon: '☁️'
  },
  {
    id: 'embedded',
    label: 'Embedded Systems',
    labelAr: 'الأنظمة المدمجة',
    description: 'IoT devices, microcontrollers',
    descriptionAr: 'أجهزة إنترنت الأشياء، المتحكمات الدقيقة',
    icon: '🔧'
  }
];

// لغات البرمجة الأساسية
export const PROGRAMMING_LANGUAGES: ProjectOption[] = [
  {
    id: 'javascript',
    label: 'JavaScript/TypeScript',
    labelAr: 'جافا سكريبت/تايب سكريبت',
    description: 'Modern web development',
    descriptionAr: 'تطوير الويب الحديث',
    icon: '🟨'
  },
  {
    id: 'nextjs-tailwind-shadcn',
    label: 'Next.js + Tailwind + shadcn/ui',
    labelAr: 'Next.js + Tailwind + shadcn/ui',
    description: 'Modern React framework with styling and UI components',
    descriptionAr: 'إطار عمل React حديث مع التصميم ومكونات واجهة المستخدم',
    icon: '⚡'
  },
  {
    id: 'python',
    label: 'Python',
    labelAr: 'بايثون',
    description: 'AI/ML, backend, data science',
    descriptionAr: 'ذكاء اصطناعي، خلفية، علوم البيانات',
    icon: '🐍'
  },
  {
    id: 'java',
    label: 'Java',
    labelAr: 'جافا',
    description: 'Enterprise applications, Android',
    descriptionAr: 'تطبيقات المؤسسات، أندرويد',
    icon: '☕'
  },
  {
    id: 'csharp',
    label: 'C#',
    labelAr: 'سي شارب',
    description: '.NET ecosystem, Windows apps',
    descriptionAr: 'نظام .NET، تطبيقات ويندوز',
    icon: '🔷'
  },
  {
    id: 'swift',
    label: 'Swift',
    labelAr: 'سويفت',
    description: 'iOS and macOS development',
    descriptionAr: 'تطوير iOS و macOS',
    icon: '🦉'
  },
  {
    id: 'kotlin',
    label: 'Kotlin',
    labelAr: 'كوتلن',
    description: 'Android development, JVM',
    descriptionAr: 'تطوير أندرويد، JVM',
    icon: '🟣'
  },
  {
    id: 'rust',
    label: 'Rust',
    labelAr: 'رست',
    description: 'System programming, performance',
    descriptionAr: 'برمجة الأنظمة، الأداء',
    icon: '🦀'
  },
  {
    id: 'go',
    label: 'Go',
    labelAr: 'جو',
    description: 'Backend services, microservices',
    descriptionAr: 'خدمات خلفية، مايكروسيرفس',
    icon: '🐹'
  },
  {
    id: 'cpp',
    label: 'C++',
    labelAr: 'سي++',
    description: 'High-performance applications',
    descriptionAr: 'تطبيقات عالية الأداء',
    icon: '⚡'
  },
  {
    id: 'php',
    label: 'PHP',
    labelAr: 'PHP',
    description: 'Web development, server-side',
    descriptionAr: 'تطوير الويب، جانب الخادم',
    icon: '🐘'
  },
  {
    id: 'ruby',
    label: 'Ruby',
    labelAr: 'روبي',
    description: 'Web applications, rapid development',
    descriptionAr: 'تطبيقات الويب، التطوير السريع',
    icon: '💎'
  },
  {
    id: 'dart',
    label: 'Dart/Flutter',
    labelAr: 'دارت/فلاتر',
    description: 'Cross-platform mobile apps',
    descriptionAr: 'تطبيقات جوال متعددة المنصات',
    icon: '🎯'
  }
];

// مستويات التعقيد
export const COMPLEXITY_LEVELS: ProjectOption[] = [
  {
    id: 'simple',
    label: 'Simple',
    labelAr: 'بسيط',
    description: 'Basic functionality, minimal features',
    descriptionAr: 'وظائف أساسية، ميزات قليلة',
    icon: '🟢'
  },
  {
    id: 'moderate',
    label: 'Moderate',
    labelAr: 'متوسط',
    description: 'Standard features, some integrations',
    descriptionAr: 'ميزات قياسية، بعض التكاملات',
    icon: '🟡'
  },
  {
    id: 'complex',
    label: 'Complex',
    labelAr: 'معقد',
    description: 'Advanced features, multiple integrations',
    descriptionAr: 'ميزات متقدمة، تكاملات متعددة',
    icon: '🟠'
  },
  {
    id: 'enterprise',
    label: 'Enterprise',
    labelAr: 'مؤسسي',
    description: 'Large-scale, high availability, security',
    descriptionAr: 'واسع النطاق، توفر عالي، أمان',
    icon: '🔴'
  }
];

// نطاقات الميزانية
export const BUDGET_RANGES: ProjectOption[] = [
  {
    id: 'startup',
    label: 'Startup Budget',
    labelAr: 'ميزانية ناشئة',
    description: 'Under $10K',
    descriptionAr: 'أقل من 10 آلاف دولار',
    icon: '💰'
  },
  {
    id: 'small',
    label: 'Small Project',
    labelAr: 'مشروع صغير',
    description: '$10K - $50K',
    descriptionAr: '10-50 ألف دولار',
    icon: '💵'
  },
  {
    id: 'medium',
    label: 'Medium Project',
    labelAr: 'مشروع متوسط',
    description: '$50K - $200K',
    descriptionAr: '50-200 ألف دولار',
    icon: '💸'
  },
  {
    id: 'large',
    label: 'Large Project',
    labelAr: 'مشروع كبير',
    description: '$200K - $1M',
    descriptionAr: '200 ألف - مليون دولار',
    icon: '💎'
  },
  {
    id: 'enterprise',
    label: 'Enterprise',
    labelAr: 'مؤسسي',
    description: '$1M+',
    descriptionAr: 'أكثر من مليون دولار',
    icon: '🏦'
  }
];

// أحجام الفريق
export const TEAM_SIZES: ProjectOption[] = [
  {
    id: 'solo',
    label: 'Solo Developer',
    labelAr: 'مطور منفرد',
    description: '1 person',
    descriptionAr: 'شخص واحد',
    icon: '👤'
  },
  {
    id: 'small',
    label: 'Small Team',
    labelAr: 'فريق صغير',
    description: '2-5 people',
    descriptionAr: '2-5 أشخاص',
    icon: '👥'
  },
  {
    id: 'medium',
    label: 'Medium Team',
    labelAr: 'فريق متوسط',
    description: '6-15 people',
    descriptionAr: '6-15 شخص',
    icon: '👨‍👩‍👧‍👦'
  },
  {
    id: 'large',
    label: 'Large Team',
    labelAr: 'فريق كبير',
    description: '16-50 people',
    descriptionAr: '16-50 شخص',
    icon: '🏢'
  },
  {
    id: 'enterprise',
    label: 'Enterprise Team',
    labelAr: 'فريق مؤسسي',
    description: '50+ people',
    descriptionAr: 'أكثر من 50 شخص',
    icon: '🏭'
  }
];

// أنواع النشر
export const DEPLOYMENT_TYPES: ProjectOption[] = [
  {
    id: 'cloud',
    label: 'Cloud Deployment',
    labelAr: 'نشر سحابي',
    description: 'AWS, Azure, Google Cloud, Vercel',
    descriptionAr: 'AWS، Azure، Google Cloud، Vercel',
    icon: '☁️'
  },
  {
    id: 'on-premise',
    label: 'On-Premise',
    labelAr: 'محلي',
    description: 'Self-hosted infrastructure',
    descriptionAr: 'بنية تحتية ذاتية الاستضافة',
    icon: '🏢'
  },
  {
    id: 'hybrid',
    label: 'Hybrid',
    labelAr: 'هجين',
    description: 'Mix of cloud and on-premise',
    descriptionAr: 'مزيج من السحابي والمحلي',
    icon: '🔄'
  },
  {
    id: 'edge',
    label: 'Edge Computing',
    labelAr: 'حوسبة الحافة',
    description: 'Distributed edge deployment',
    descriptionAr: 'نشر موزع على الحافة',
    icon: '🌐'
  },
  {
    id: 'mobile-stores',
    label: 'App Stores',
    labelAr: 'متاجر التطبيقات',
    description: 'Google Play, App Store',
    descriptionAr: 'جوجل بلاي، آب ستور',
    icon: '📱'
  }
];

// المناطق الجغرافية والدول
export const GEOGRAPHIC_REGIONS: ProjectOption[] = [
  {
    id: 'morocco',
    label: 'Morocco',
    labelAr: 'المغرب',
    description: 'Kingdom of Morocco - North Africa',
    descriptionAr: 'المملكة المغربية - شمال أفريقيا',
    icon: '🇲🇦'
  },
  {
    id: 'middle-east',
    label: 'Middle East',
    labelAr: 'الشرق الأوسط',
    description: 'Middle Eastern countries and regions',
    descriptionAr: 'دول ومناطق الشرق الأوسط',
    icon: '🕌'
  },
  {
    id: 'north-africa',
    label: 'North Africa',
    labelAr: 'شمال أفريقيا',
    description: 'Northern African countries',
    descriptionAr: 'دول شمال أفريقيا',
    icon: '🏜️'
  },
  {
    id: 'africa',
    label: 'Africa',
    labelAr: 'أفريقيا',
    description: 'African continent',
    descriptionAr: 'القارة الأفريقية',
    icon: '🌍'
  },
  {
    id: 'arab-world',
    label: 'Arab World',
    labelAr: 'العالم العربي',
    description: 'Arabic-speaking countries and regions',
    descriptionAr: 'الدول والمناطق الناطقة بالعربية',
    icon: '🌙'
  },
  {
    id: 'europe',
    label: 'Europe',
    labelAr: 'أوروبا',
    description: 'European countries',
    descriptionAr: 'الدول الأوروبية',
    icon: '🇪🇺'
  },
  {
    id: 'north-america',
    label: 'North America',
    labelAr: 'أمريكا الشمالية',
    description: 'United States, Canada, and Mexico',
    descriptionAr: 'الولايات المتحدة وكندا والمكسيك',
    icon: '🌎'
  },
  {
    id: 'asia',
    label: 'Asia',
    labelAr: 'آسيا',
    description: 'Asian countries and regions',
    descriptionAr: 'الدول والمناطق الآسيوية',
    icon: '🌏'
  },
  {
    id: 'global',
    label: 'Global/Worldwide',
    labelAr: 'عالمي/في جميع أنحاء العالم',
    description: 'Worldwide coverage and availability',
    descriptionAr: 'تغطية وتوفر عالمي',
    icon: '🌐'
  }
];

// طبيعة المشروع ونوع الأعمال
export const PROJECT_NATURE: ProjectOption[] = [
  {
    id: 'graduation-clothing',
    label: 'Graduation Clothing',
    labelAr: 'لباس التخرج',
    description: 'Academic graduation gowns, caps, and accessories',
    descriptionAr: 'أثواب التخرج الأكاديمية والقبعات والإكسسوارات',
    icon: '🎓'
  },
  {
    id: 'fashion-retail',
    label: 'Fashion & Retail',
    labelAr: 'الأزياء والتجارة',
    description: 'Clothing, fashion, and retail business',
    descriptionAr: 'الملابس والأزياء وأعمال التجارة',
    icon: '👗'
  },
  {
    id: 'education',
    label: 'Education',
    labelAr: 'التعليم',
    description: 'Educational services and platforms',
    descriptionAr: 'الخدمات والمنصات التعليمية',
    icon: '📚'
  },
  {
    id: 'healthcare',
    label: 'Healthcare',
    labelAr: 'الرعاية الصحية',
    description: 'Medical and healthcare services',
    descriptionAr: 'الخدمات الطبية والرعاية الصحية',
    icon: '🏥'
  },
  {
    id: 'fintech',
    label: 'Financial Technology',
    labelAr: 'التكنولوجيا المالية',
    description: 'Banking, payments, and financial services',
    descriptionAr: 'الخدمات المصرفية والمدفوعات والخدمات المالية',
    icon: '💳'
  },
  {
    id: 'ecommerce',
    label: 'E-commerce',
    labelAr: 'التجارة الإلكترونية',
    description: 'Online shopping and marketplace platforms',
    descriptionAr: 'منصات التسوق الإلكتروني والأسواق',
    icon: '🛒'
  },
  {
    id: 'food-beverage',
    label: 'Food & Beverage',
    labelAr: 'الطعام والشراب',
    description: 'Restaurant, food delivery, and culinary services',
    descriptionAr: 'المطاعم وتوصيل الطعام والخدمات الطهوية',
    icon: '🍽️'
  },
  {
    id: 'travel-tourism',
    label: 'Travel & Tourism',
    labelAr: 'السفر والسياحة',
    description: 'Travel booking, tourism, and hospitality',
    descriptionAr: 'حجز السفر والسياحة والضيافة',
    icon: '✈️'
  },
  {
    id: 'real-estate',
    label: 'Real Estate',
    labelAr: 'العقارات',
    description: 'Property management and real estate services',
    descriptionAr: 'إدارة الممتلكات وخدمات العقارات',
    icon: '🏠'
  },
  {
    id: 'entertainment',
    label: 'Entertainment & Media',
    labelAr: 'الترفيه والإعلام',
    description: 'Gaming, streaming, and media content',
    descriptionAr: 'الألعاب والبث والمحتوى الإعلامي',
    icon: '🎬'
  },
  {
    id: 'logistics',
    label: 'Logistics & Transportation',
    labelAr: 'اللوجستيات والنقل',
    description: 'Shipping, delivery, and transportation services',
    descriptionAr: 'خدمات الشحن والتوصيل والنقل',
    icon: '🚚'
  },
  {
    id: 'agriculture',
    label: 'Agriculture & Farming',
    labelAr: 'الزراعة والفلاحة',
    description: 'Agricultural technology and farming solutions',
    descriptionAr: 'التكنولوجيا الزراعية وحلول الفلاحة',
    icon: '🌾'
  },
  {
    id: 'manufacturing',
    label: 'Manufacturing',
    labelAr: 'التصنيع',
    description: 'Industrial manufacturing and production',
    descriptionAr: 'التصنيع الصناعي والإنتاج',
    icon: '🏭'
  },
  {
    id: 'consulting',
    label: 'Consulting & Services',
    labelAr: 'الاستشارات والخدمات',
    description: 'Professional consulting and business services',
    descriptionAr: 'الاستشارات المهنية وخدمات الأعمال',
    icon: '💼'
  },
  {
    id: 'nonprofit',
    label: 'Non-Profit & NGO',
    labelAr: 'غير ربحي ومنظمات',
    description: 'Non-profit organizations and social causes',
    descriptionAr: 'المنظمات غير الربحية والقضايا الاجتماعية',
    icon: '🤝'
  },
  {
    id: 'other',
    label: 'Other',
    labelAr: 'أخرى',
    description: 'Other business types and industries',
    descriptionAr: 'أنواع أعمال وصناعات أخرى',
    icon: '📋'
  }
];
