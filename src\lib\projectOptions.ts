// خيارات التخصيص المتقدمة للمشاريع

export interface ProjectOption {
  id: string;
  label: string;
  labelAr: string;
  description?: string;
  descriptionAr?: string;
  icon?: string;
}

// أنواع المشاريع
export const PROJECT_TYPES: ProjectOption[] = [
  {
    id: 'web-app',
    label: 'Web Application',
    labelAr: 'تطبيق ويب',
    description: 'Browser-based application accessible via web browsers',
    descriptionAr: 'تطبيق يعمل في المتصفح ويمكن الوصول إليه عبر متصفحات الويب',
    icon: '🌐'
  },
  {
    id: 'mobile-app',
    label: 'Mobile Application',
    labelAr: 'تطبيق جوال',
    description: 'Native or cross-platform mobile application',
    descriptionAr: 'تطبيق جوال أصلي أو متعدد المنصات',
    icon: '📱'
  },
  {
    id: 'desktop-app',
    label: 'Desktop Application',
    labelAr: 'تطبيق سطح المكتب',
    description: 'Native desktop application for Windows, macOS, or Linux',
    descriptionAr: 'تطبيق سطح مكتب أصلي لـ Windows أو macOS أو Linux',
    icon: '💻'
  },
  {
    id: 'api-service',
    label: 'API/Microservice',
    labelAr: 'خدمة API/مايكروسيرفس',
    description: 'Backend API or microservice architecture',
    descriptionAr: 'خدمة API خلفية أو هندسة مايكروسيرفس',
    icon: '🔌'
  },
  {
    id: 'ai-model',
    label: 'AI/ML Model',
    labelAr: 'نموذج ذكاء اصطناعي',
    description: 'Machine learning model or AI system',
    descriptionAr: 'نموذج تعلم آلي أو نظام ذكاء اصطناعي',
    icon: '🤖'
  },
  {
    id: 'chatbot',
    label: 'Chatbot/Virtual Assistant',
    labelAr: 'شات بوت/مساعد افتراضي',
    description: 'Conversational AI or chatbot system',
    descriptionAr: 'نظام ذكاء اصطناعي محادثة أو شات بوت',
    icon: '💬'
  },
  {
    id: 'data-analytics',
    label: 'Data Analytics Platform',
    labelAr: 'منصة تحليل البيانات',
    description: 'Data processing and analytics solution',
    descriptionAr: 'حل معالجة وتحليل البيانات',
    icon: '📊'
  },
  {
    id: 'iot-system',
    label: 'IoT System',
    labelAr: 'نظام إنترنت الأشياء',
    description: 'Internet of Things connected system',
    descriptionAr: 'نظام متصل بإنترنت الأشياء',
    icon: '🌐'
  }
];

// المنصات المستهدفة
export const TARGET_PLATFORMS: ProjectOption[] = [
  {
    id: 'web-browsers',
    label: 'Web Browsers',
    labelAr: 'متصفحات الويب',
    description: 'Chrome, Firefox, Safari, Edge',
    descriptionAr: 'كروم، فايرفوكس، سفاري، إيدج',
    icon: '🌐'
  },
  {
    id: 'android',
    label: 'Android',
    labelAr: 'أندرويد',
    description: 'Android mobile devices',
    descriptionAr: 'أجهزة أندرويد المحمولة',
    icon: '🤖'
  },
  {
    id: 'ios',
    label: 'iOS',
    labelAr: 'iOS',
    description: 'iPhone and iPad devices',
    descriptionAr: 'أجهزة آيفون وآيباد',
    icon: '🍎'
  },
  {
    id: 'windows',
    label: 'Windows',
    labelAr: 'ويندوز',
    description: 'Windows desktop and server',
    descriptionAr: 'سطح مكتب وخادم ويندوز',
    icon: '🪟'
  },
  {
    id: 'macos',
    label: 'macOS',
    labelAr: 'macOS',
    description: 'Apple macOS desktop',
    descriptionAr: 'سطح مكتب آبل macOS',
    icon: '🍎'
  },
  {
    id: 'linux',
    label: 'Linux',
    labelAr: 'لينكس',
    description: 'Linux distributions',
    descriptionAr: 'توزيعات لينكس',
    icon: '🐧'
  },
  {
    id: 'cloud',
    label: 'Cloud Platforms',
    labelAr: 'المنصات السحابية',
    description: 'AWS, Azure, Google Cloud',
    descriptionAr: 'AWS، Azure، Google Cloud',
    icon: '☁️'
  },
  {
    id: 'embedded',
    label: 'Embedded Systems',
    labelAr: 'الأنظمة المدمجة',
    description: 'IoT devices, microcontrollers',
    descriptionAr: 'أجهزة إنترنت الأشياء، المتحكمات الدقيقة',
    icon: '🔧'
  }
];

// لغات البرمجة الأساسية
export const PROGRAMMING_LANGUAGES: ProjectOption[] = [
  {
    id: 'javascript',
    label: 'JavaScript/TypeScript',
    labelAr: 'جافا سكريبت/تايب سكريبت',
    description: 'Modern web development',
    descriptionAr: 'تطوير الويب الحديث',
    icon: '🟨'
  },
  {
    id: 'python',
    label: 'Python',
    labelAr: 'بايثون',
    description: 'AI/ML, backend, data science',
    descriptionAr: 'ذكاء اصطناعي، خلفية، علوم البيانات',
    icon: '🐍'
  },
  {
    id: 'java',
    label: 'Java',
    labelAr: 'جافا',
    description: 'Enterprise applications, Android',
    descriptionAr: 'تطبيقات المؤسسات، أندرويد',
    icon: '☕'
  },
  {
    id: 'csharp',
    label: 'C#',
    labelAr: 'سي شارب',
    description: '.NET ecosystem, Windows apps',
    descriptionAr: 'نظام .NET، تطبيقات ويندوز',
    icon: '🔷'
  },
  {
    id: 'swift',
    label: 'Swift',
    labelAr: 'سويفت',
    description: 'iOS and macOS development',
    descriptionAr: 'تطوير iOS و macOS',
    icon: '🦉'
  },
  {
    id: 'kotlin',
    label: 'Kotlin',
    labelAr: 'كوتلن',
    description: 'Android development, JVM',
    descriptionAr: 'تطوير أندرويد، JVM',
    icon: '🟣'
  },
  {
    id: 'rust',
    label: 'Rust',
    labelAr: 'رست',
    description: 'System programming, performance',
    descriptionAr: 'برمجة الأنظمة، الأداء',
    icon: '🦀'
  },
  {
    id: 'go',
    label: 'Go',
    labelAr: 'جو',
    description: 'Backend services, microservices',
    descriptionAr: 'خدمات خلفية، مايكروسيرفس',
    icon: '🐹'
  },
  {
    id: 'cpp',
    label: 'C++',
    labelAr: 'سي++',
    description: 'High-performance applications',
    descriptionAr: 'تطبيقات عالية الأداء',
    icon: '⚡'
  },
  {
    id: 'php',
    label: 'PHP',
    labelAr: 'PHP',
    description: 'Web development, server-side',
    descriptionAr: 'تطوير الويب، جانب الخادم',
    icon: '🐘'
  },
  {
    id: 'ruby',
    label: 'Ruby',
    labelAr: 'روبي',
    description: 'Web applications, rapid development',
    descriptionAr: 'تطبيقات الويب، التطوير السريع',
    icon: '💎'
  },
  {
    id: 'dart',
    label: 'Dart/Flutter',
    labelAr: 'دارت/فلاتر',
    description: 'Cross-platform mobile apps',
    descriptionAr: 'تطبيقات جوال متعددة المنصات',
    icon: '🎯'
  }
];

// مستويات التعقيد
export const COMPLEXITY_LEVELS: ProjectOption[] = [
  {
    id: 'simple',
    label: 'Simple',
    labelAr: 'بسيط',
    description: 'Basic functionality, minimal features',
    descriptionAr: 'وظائف أساسية، ميزات قليلة',
    icon: '🟢'
  },
  {
    id: 'moderate',
    label: 'Moderate',
    labelAr: 'متوسط',
    description: 'Standard features, some integrations',
    descriptionAr: 'ميزات قياسية، بعض التكاملات',
    icon: '🟡'
  },
  {
    id: 'complex',
    label: 'Complex',
    labelAr: 'معقد',
    description: 'Advanced features, multiple integrations',
    descriptionAr: 'ميزات متقدمة، تكاملات متعددة',
    icon: '🟠'
  },
  {
    id: 'enterprise',
    label: 'Enterprise',
    labelAr: 'مؤسسي',
    description: 'Large-scale, high availability, security',
    descriptionAr: 'واسع النطاق، توفر عالي، أمان',
    icon: '🔴'
  }
];

// نطاقات الميزانية
export const BUDGET_RANGES: ProjectOption[] = [
  {
    id: 'startup',
    label: 'Startup Budget',
    labelAr: 'ميزانية ناشئة',
    description: 'Under $10K',
    descriptionAr: 'أقل من 10 آلاف دولار',
    icon: '💰'
  },
  {
    id: 'small',
    label: 'Small Project',
    labelAr: 'مشروع صغير',
    description: '$10K - $50K',
    descriptionAr: '10-50 ألف دولار',
    icon: '💵'
  },
  {
    id: 'medium',
    label: 'Medium Project',
    labelAr: 'مشروع متوسط',
    description: '$50K - $200K',
    descriptionAr: '50-200 ألف دولار',
    icon: '💸'
  },
  {
    id: 'large',
    label: 'Large Project',
    labelAr: 'مشروع كبير',
    description: '$200K - $1M',
    descriptionAr: '200 ألف - مليون دولار',
    icon: '💎'
  },
  {
    id: 'enterprise',
    label: 'Enterprise',
    labelAr: 'مؤسسي',
    description: '$1M+',
    descriptionAr: 'أكثر من مليون دولار',
    icon: '🏦'
  }
];

// أحجام الفريق
export const TEAM_SIZES: ProjectOption[] = [
  {
    id: 'solo',
    label: 'Solo Developer',
    labelAr: 'مطور منفرد',
    description: '1 person',
    descriptionAr: 'شخص واحد',
    icon: '👤'
  },
  {
    id: 'small',
    label: 'Small Team',
    labelAr: 'فريق صغير',
    description: '2-5 people',
    descriptionAr: '2-5 أشخاص',
    icon: '👥'
  },
  {
    id: 'medium',
    label: 'Medium Team',
    labelAr: 'فريق متوسط',
    description: '6-15 people',
    descriptionAr: '6-15 شخص',
    icon: '👨‍👩‍👧‍👦'
  },
  {
    id: 'large',
    label: 'Large Team',
    labelAr: 'فريق كبير',
    description: '16-50 people',
    descriptionAr: '16-50 شخص',
    icon: '🏢'
  },
  {
    id: 'enterprise',
    label: 'Enterprise Team',
    labelAr: 'فريق مؤسسي',
    description: '50+ people',
    descriptionAr: 'أكثر من 50 شخص',
    icon: '🏭'
  }
];

// أنواع النشر
export const DEPLOYMENT_TYPES: ProjectOption[] = [
  {
    id: 'cloud',
    label: 'Cloud Deployment',
    labelAr: 'نشر سحابي',
    description: 'AWS, Azure, Google Cloud, Vercel',
    descriptionAr: 'AWS، Azure، Google Cloud، Vercel',
    icon: '☁️'
  },
  {
    id: 'on-premise',
    label: 'On-Premise',
    labelAr: 'محلي',
    description: 'Self-hosted infrastructure',
    descriptionAr: 'بنية تحتية ذاتية الاستضافة',
    icon: '🏢'
  },
  {
    id: 'hybrid',
    label: 'Hybrid',
    labelAr: 'هجين',
    description: 'Mix of cloud and on-premise',
    descriptionAr: 'مزيج من السحابي والمحلي',
    icon: '🔄'
  },
  {
    id: 'edge',
    label: 'Edge Computing',
    labelAr: 'حوسبة الحافة',
    description: 'Distributed edge deployment',
    descriptionAr: 'نشر موزع على الحافة',
    icon: '🌐'
  },
  {
    id: 'mobile-stores',
    label: 'App Stores',
    labelAr: 'متاجر التطبيقات',
    description: 'Google Play, App Store',
    descriptionAr: 'جوجل بلاي، آب ستور',
    icon: '📱'
  }
];
