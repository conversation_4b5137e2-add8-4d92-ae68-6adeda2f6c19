// خيارات تقنية متقدمة للطبقة التقنية

import { ProjectOption } from './projectOptions';

// أنماط الهندسة المعمارية
export const ARCHITECTURE_PATTERNS: ProjectOption[] = [
  {
    id: 'monolithic',
    label: 'Monolithic',
    labelAr: 'أحادي الكتلة',
    description: 'Single deployable unit',
    descriptionAr: 'وحدة نشر واحدة',
    icon: '🏗️'
  },
  {
    id: 'microservices',
    label: 'Microservices',
    labelAr: 'مايكروسيرفس',
    description: 'Distributed services architecture',
    descriptionAr: 'هندسة خدمات موزعة',
    icon: '🔧'
  },
  {
    id: 'serverless',
    label: 'Serverless',
    labelAr: 'بلا خادم',
    description: 'Function-as-a-Service (FaaS)',
    descriptionAr: 'الوظيفة كخدمة',
    icon: '⚡'
  },
  {
    id: 'event-driven',
    label: 'Event-Driven',
    labelAr: 'مدفوع بالأحداث',
    description: 'Event-based communication',
    descriptionAr: 'تواصل قائم على الأحداث',
    icon: '📡'
  },
  {
    id: 'layered',
    label: 'Layered Architecture',
    labelAr: 'هندسة طبقية',
    description: 'Traditional layered approach',
    descriptionAr: 'نهج طبقي تقليدي',
    icon: '📚'
  },
  {
    id: 'hexagonal',
    label: 'Hexagonal (Ports & Adapters)',
    labelAr: 'سداسية (المنافذ والمحولات)',
    description: 'Clean architecture pattern',
    descriptionAr: 'نمط هندسة نظيفة',
    icon: '⬡'
  }
];

// استراتيجيات التوسع
export const SCALING_STRATEGIES: ProjectOption[] = [
  {
    id: 'horizontal',
    label: 'Horizontal Scaling',
    labelAr: 'توسع أفقي',
    description: 'Scale out with more instances',
    descriptionAr: 'توسع بمزيد من النسخ',
    icon: '↔️'
  },
  {
    id: 'vertical',
    label: 'Vertical Scaling',
    labelAr: 'توسع عمودي',
    description: 'Scale up with more resources',
    descriptionAr: 'توسع بمزيد من الموارد',
    icon: '↕️'
  },
  {
    id: 'auto-scaling',
    label: 'Auto Scaling',
    labelAr: 'توسع تلقائي',
    description: 'Dynamic scaling based on demand',
    descriptionAr: 'توسع ديناميكي حسب الطلب',
    icon: '🔄'
  },
  {
    id: 'load-balancing',
    label: 'Load Balancing',
    labelAr: 'توزيع الأحمال',
    description: 'Distribute traffic across instances',
    descriptionAr: 'توزيع الحركة عبر النسخ',
    icon: '⚖️'
  },
  {
    id: 'caching',
    label: 'Caching Strategy',
    labelAr: 'استراتيجية التخزين المؤقت',
    description: 'Memory and distributed caching',
    descriptionAr: 'تخزين مؤقت في الذاكرة وموزع',
    icon: '💾'
  }
];

// متطلبات الأمان
export const SECURITY_REQUIREMENTS: ProjectOption[] = [
  {
    id: 'authentication',
    label: 'Authentication & Authorization',
    labelAr: 'المصادقة والتخويل',
    description: 'User identity and access control',
    descriptionAr: 'هوية المستخدم والتحكم في الوصول',
    icon: '🔐'
  },
  {
    id: 'encryption',
    label: 'Data Encryption',
    labelAr: 'تشفير البيانات',
    description: 'At rest and in transit',
    descriptionAr: 'في الراحة وأثناء النقل',
    icon: '🔒'
  },
  {
    id: 'api-security',
    label: 'API Security',
    labelAr: 'أمان API',
    description: 'Rate limiting, validation, CORS',
    descriptionAr: 'تحديد المعدل، التحقق، CORS',
    icon: '🛡️'
  },
  {
    id: 'compliance',
    label: 'Compliance Standards',
    labelAr: 'معايير الامتثال',
    description: 'GDPR, HIPAA, SOC2, etc.',
    descriptionAr: 'GDPR، HIPAA، SOC2، إلخ',
    icon: '📋'
  },
  {
    id: 'vulnerability',
    label: 'Vulnerability Management',
    labelAr: 'إدارة الثغرات',
    description: 'Security scanning and patching',
    descriptionAr: 'فحص الأمان والترقيع',
    icon: '🔍'
  },
  {
    id: 'backup',
    label: 'Backup & Recovery',
    labelAr: 'النسخ الاحتياطي والاستعادة',
    description: 'Data protection and disaster recovery',
    descriptionAr: 'حماية البيانات واستعادة الكوارث',
    icon: '💿'
  }
];

// أهداف الأداء
export const PERFORMANCE_TARGETS: ProjectOption[] = [
  {
    id: 'response-time',
    label: 'Response Time < 200ms',
    labelAr: 'وقت الاستجابة < 200 مللي ثانية',
    description: 'Fast API response times',
    descriptionAr: 'أوقات استجابة سريعة لـ API',
    icon: '⚡'
  },
  {
    id: 'throughput',
    label: 'High Throughput',
    labelAr: 'إنتاجية عالية',
    description: 'Handle many concurrent requests',
    descriptionAr: 'التعامل مع طلبات متزامنة كثيرة',
    icon: '🚀'
  },
  {
    id: 'availability',
    label: '99.9% Uptime',
    labelAr: 'وقت تشغيل 99.9%',
    description: 'High availability requirements',
    descriptionAr: 'متطلبات توفر عالية',
    icon: '🎯'
  },
  {
    id: 'scalability',
    label: 'Linear Scalability',
    labelAr: 'قابلية توسع خطية',
    description: 'Performance scales with resources',
    descriptionAr: 'الأداء يتوسع مع الموارد',
    icon: '📈'
  },
  {
    id: 'memory-efficiency',
    label: 'Memory Efficiency',
    labelAr: 'كفاءة الذاكرة',
    description: 'Optimized memory usage',
    descriptionAr: 'استخدام محسن للذاكرة',
    icon: '🧠'
  }
];

// احتياجات التكامل
export const INTEGRATION_NEEDS: ProjectOption[] = [
  {
    id: 'rest-apis',
    label: 'REST APIs',
    labelAr: 'واجهات REST',
    description: 'RESTful web services',
    descriptionAr: 'خدمات ويب RESTful',
    icon: '🔗'
  },
  {
    id: 'graphql',
    label: 'GraphQL',
    labelAr: 'GraphQL',
    description: 'Query language for APIs',
    descriptionAr: 'لغة استعلام لواجهات API',
    icon: '📊'
  },
  {
    id: 'webhooks',
    label: 'Webhooks',
    labelAr: 'ويب هوكس',
    description: 'Event-driven HTTP callbacks',
    descriptionAr: 'استدعاءات HTTP مدفوعة بالأحداث',
    icon: '🪝'
  },
  {
    id: 'message-queues',
    label: 'Message Queues',
    labelAr: 'طوابير الرسائل',
    description: 'Asynchronous messaging',
    descriptionAr: 'رسائل غير متزامنة',
    icon: '📬'
  },
  {
    id: 'third-party',
    label: 'Third-party Services',
    labelAr: 'خدمات طرف ثالث',
    description: 'External service integrations',
    descriptionAr: 'تكاملات خدمات خارجية',
    icon: '🔌'
  },
  {
    id: 'databases',
    label: 'Database Integration',
    labelAr: 'تكامل قواعد البيانات',
    description: 'Multiple database connections',
    descriptionAr: 'اتصالات قواعد بيانات متعددة',
    icon: '🗄️'
  }
];

// أدوات المراقبة
export const MONITORING_TOOLS: ProjectOption[] = [
  {
    id: 'application-monitoring',
    label: 'Application Monitoring',
    labelAr: 'مراقبة التطبيق',
    description: 'Performance and error tracking',
    descriptionAr: 'تتبع الأداء والأخطاء',
    icon: '📊'
  },
  {
    id: 'infrastructure-monitoring',
    label: 'Infrastructure Monitoring',
    labelAr: 'مراقبة البنية التحتية',
    description: 'Server and resource monitoring',
    descriptionAr: 'مراقبة الخادم والموارد',
    icon: '🖥️'
  },
  {
    id: 'logging',
    label: 'Centralized Logging',
    labelAr: 'تسجيل مركزي',
    description: 'Log aggregation and analysis',
    descriptionAr: 'تجميع وتحليل السجلات',
    icon: '📝'
  },
  {
    id: 'alerting',
    label: 'Alerting System',
    labelAr: 'نظام التنبيهات',
    description: 'Real-time notifications',
    descriptionAr: 'إشعارات في الوقت الفعلي',
    icon: '🚨'
  },
  {
    id: 'metrics',
    label: 'Custom Metrics',
    labelAr: 'مقاييس مخصصة',
    description: 'Business and technical metrics',
    descriptionAr: 'مقاييس تجارية وتقنية',
    icon: '📈'
  },
  {
    id: 'tracing',
    label: 'Distributed Tracing',
    labelAr: 'تتبع موزع',
    description: 'Request flow tracking',
    descriptionAr: 'تتبع تدفق الطلبات',
    icon: '🔍'
  }
];
