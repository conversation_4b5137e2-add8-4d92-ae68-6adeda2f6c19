"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/context-map/page",{

/***/ "(app-pages-browser)/./src/app/context-map/page.tsx":
/*!**************************************!*\
  !*** ./src/app/context-map/page.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ContextMap; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ModuleLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ModuleLayout */ \"(app-pages-browser)/./src/components/ModuleLayout.tsx\");\n/* harmony import */ var _components_SmartQuestion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/SmartQuestion */ \"(app-pages-browser)/./src/components/SmartQuestion.tsx\");\n/* harmony import */ var _components_OutputPanel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/OutputPanel */ \"(app-pages-browser)/./src/components/OutputPanel.tsx\");\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ContextMap() {\n    _s();\n    const { contextMap, updateContextMap } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_4__.useContextStore)();\n    const questions = [\n        {\n            id: \"timeContext\",\n            question: \"What is the temporal context of your project?\",\n            questionAr: \"ما هو السياق الزمني لمشروعك؟\",\n            placeholder: \"e.g., Global 24/7 support, Business hours EST, Real-time responses...\",\n            placeholderAr: \"مثال: دعم عالمي على مدار الساعة، ساعات العمل بتوقيت شرق أمريكا، استجابات فورية...\",\n            aiSuggestion: \"Consider time zones, working hours, response time expectations, and any time-sensitive requirements.\",\n            aiSuggestionAr: \"فكر في المناطق الزمنية وساعات العمل وتوقعات وقت الاستجابة وأي متطلبات حساسة للوقت.\",\n            promptTemplate: 'Help me optimize this temporal context for an AI project: \"{answer}\". Suggest improvements for better time management.'\n        },\n        {\n            id: \"language\",\n            question: \"What languages should your AI system support?\",\n            questionAr: \"ما هي اللغات التي يجب أن يدعمها نظام الذكاء الاصطناعي؟\",\n            placeholder: \"e.g., English primary, Arabic secondary, Multilingual support...\",\n            placeholderAr: \"مثال: الإنجليزية أساسية، العربية ثانوية، دعم متعدد اللغات...\",\n            type: \"text\",\n            aiSuggestion: \"Consider your target audience, regional requirements, and the complexity of multilingual support.\",\n            aiSuggestionAr: \"فكر في جمهورك المستهدف والمتطلبات الإقليمية وتعقيد الدعم متعدد اللغات.\",\n            promptTemplate: 'Analyze this language requirement for an AI system: \"{answer}\". Suggest implementation strategies.'\n        },\n        {\n            id: \"location\",\n            question: \"What geographic regions or locations will this project serve?\",\n            questionAr: \"ما هي المناطق الجغرافية أو المواقع التي سيخدمها هذا المشروع؟\",\n            placeholder: \"e.g., Middle East, North America, Global, Specific cities...\",\n            placeholderAr: \"مثال: الشرق الأوسط، أمريكا الشمالية، عالمي، مدن محددة...\",\n            aiSuggestion: \"Think about regional regulations, cultural differences, and infrastructure requirements.\",\n            aiSuggestionAr: \"فكر في اللوائح الإقليمية والاختلافات الثقافية ومتطلبات البنية التحتية.\",\n            promptTemplate: 'Help me understand the geographic implications of this scope: \"{answer}\". What should I consider?'\n        }\n    ];\n    const handleFieldChange = (field, value)=>{\n        updateContextMap({\n            [field]: value\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ModuleLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        title: \"Context Map\",\n        titleAr: \"خريطة السياق\",\n        subtitle: \"Define the contextual framework for your AI project\",\n        subtitleAr: \"حدد الإطار السياقي لمشروع الذكاء الاصطناعي\",\n        emoji: \"\\uD83D\\uDDFA️\",\n        moduleKey: \"context-map\",\n        backLink: {\n            href: \"/project-definition\",\n            label: \"Back to Project Definition\",\n            labelAr: \"العودة لتعريف المشروع\"\n        },\n        nextLink: {\n            href: \"/emotional-tone\",\n            label: \"Next: Emotional Tone\",\n            labelAr: \"التالي: النبرة العاطفية\"\n        },\n        rightPanel: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OutputPanel__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            moduleData: contextMap,\n            moduleName: \"Context Map\",\n            moduleNameAr: \"خريطة السياق\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n            lineNumber: 68,\n            columnNumber: 9\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: questions.map((question)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SmartQuestion__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    id: question.id,\n                    question: question.question,\n                    questionAr: question.questionAr,\n                    placeholder: question.placeholder,\n                    placeholderAr: question.placeholderAr,\n                    value: contextMap[question.id] || \"\",\n                    onChange: (value)=>handleFieldChange(question.id, value),\n                    type: question.type,\n                    aiSuggestion: question.aiSuggestion,\n                    aiSuggestionAr: question.aiSuggestionAr,\n                    promptTemplate: question.promptTemplate\n                }, question.id, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\context-map\\\\page.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n_s(ContextMap, \"jlOHj64dV16jvKA34KKvBF1tiHE=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_4__.useContextStore\n    ];\n});\n_c = ContextMap;\nvar _c;\n$RefreshReg$(_c, \"ContextMap\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/context-map/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ModuleLayout.tsx":
/*!*****************************************!*\
  !*** ./src/components/ModuleLayout.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ModuleLayout; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Header */ \"(app-pages-browser)/./src/components/Header.tsx\");\n/* harmony import */ var _ProgressIndicator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ProgressIndicator */ \"(app-pages-browser)/./src/components/ProgressIndicator.tsx\");\n/* harmony import */ var _AutoSaveIndicator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AutoSaveIndicator */ \"(app-pages-browser)/./src/components/AutoSaveIndicator.tsx\");\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ModuleLayout(param) {\n    let { title, titleAr, subtitle, subtitleAr, emoji, moduleKey, backLink, nextLink, children, rightPanel } = param;\n    _s();\n    const { currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_4__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800\",\n        dir: isArabic ? \"rtl\" : \"ltr\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    title: isArabic ? titleAr : title,\n                    subtitle: isArabic ? subtitleAr : subtitle,\n                    emoji: emoji,\n                    backLink: backLink ? {\n                        href: backLink.href,\n                        label: isArabic ? backLink.labelAr : backLink.label\n                    } : undefined\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProgressIndicator__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    currentModule: moduleKey\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-2 gap-8 max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6 lg:order-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center mb-4\",\n                                        children: isArabic ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold text-gray-900 dark:text-white text-center ml-3\",\n                                                    children: \"الأسئلة الذكية\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                    lineNumber: 70,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl\",\n                                                    children: \"✍️\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl mr-3\",\n                                                    children: \"✍️\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                    lineNumber: 77,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold text-gray-900 dark:text-white text-center\",\n                                                    children: \"Smart Questions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                    lineNumber: 78,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 15\n                                    }, this),\n                                    children\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6 lg:order-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 sticky top-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center mb-4\",\n                                        children: isArabic ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold text-gray-900 dark:text-white text-center ml-3\",\n                                                    children: \"المخرجات المجمّعة\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl\",\n                                                    children: \"\\uD83D\\uDCC4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl mr-3\",\n                                                    children: \"\\uD83D\\uDCC4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold text-gray-900 dark:text-white text-center\",\n                                                    children: \"Generated Outputs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 15\n                                    }, this),\n                                    rightPanel\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this),\n                (backLink || nextLink) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mt-12 max-w-7xl mx-auto gap-6\",\n                    children: isArabic ? // Arabic layout: Next button on the right, Back button on the left\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            nextLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: nextLink.href,\n                                className: \"px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-all duration-200 hover:scale-105 shadow-sm hover:shadow-md font-medium\",\n                                children: nextLink.labelAr\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 19\n                            }, this),\n                            backLink ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: backLink.href,\n                                className: \"px-6 py-3 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500 transition-all duration-200 hover:scale-105 shadow-sm hover:shadow-md font-medium\",\n                                children: backLink.labelAr\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 19\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 19\n                            }, this)\n                        ]\n                    }, void 0, true) : // English layout: Back button on the left, Next button on the right\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            backLink ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: backLink.href,\n                                className: \"px-6 py-3 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500 transition-all duration-200 hover:scale-105 shadow-sm hover:shadow-md font-medium\",\n                                children: backLink.label\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 19\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 19\n                            }, this),\n                            nextLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: nextLink.href,\n                                className: \"px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-all duration-200 hover:scale-105 shadow-sm hover:shadow-md font-medium\",\n                                children: nextLink.label\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 19\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AutoSaveIndicator__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n            lineNumber: 47,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n_s(ModuleLayout, \"TUcpdwZ+GxByrtxz7lNUh3/XmDk=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_4__.useContextStore\n    ];\n});\n_c = ModuleLayout;\nvar _c;\n$RefreshReg$(_c, \"ModuleLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ModuleLayout.tsx\n"));

/***/ })

});