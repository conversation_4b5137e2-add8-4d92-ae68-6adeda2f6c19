"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/project-definition/page",{

/***/ "(app-pages-browser)/./src/app/project-definition/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/project-definition/page.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProjectDefinition; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ModuleLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ModuleLayout */ \"(app-pages-browser)/./src/components/ModuleLayout.tsx\");\n/* harmony import */ var _components_SmartQuestion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/SmartQuestion */ \"(app-pages-browser)/./src/components/SmartQuestion.tsx\");\n/* harmony import */ var _components_OutputPanel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/OutputPanel */ \"(app-pages-browser)/./src/components/OutputPanel.tsx\");\n/* harmony import */ var _components_AdvancedOptionsPanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/AdvancedOptionsPanel */ \"(app-pages-browser)/./src/components/AdvancedOptionsPanel.tsx\");\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction ProjectDefinition() {\n    _s();\n    const { projectDefinition, updateProjectDefinition } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_5__.useContextStore)();\n    const questions = [\n        {\n            id: \"name\",\n            question: \"What is the name of your AI project?\",\n            questionAr: \"ما هو اسم مشروع الذكاء الاصطناعي الخاص بك؟\",\n            placeholder: \"e.g., Smart Customer Support Bot, Content Generator AI, etc.\",\n            placeholderAr: \"مثال: بوت دعم العملاء الذكي، مولد المحتوى بالذكاء الاصطناعي، إلخ.\",\n            type: \"text\",\n            aiSuggestion: \"Choose a clear, descriptive name that reflects your project's main function and target audience.\",\n            aiSuggestionAr: \"اختر اسماً واضحاً ووصفياً يعكس الوظيفة الرئيسية لمشروعك والجمهور المستهدف.\",\n            promptTemplate: 'Help me refine this AI project name: \"{answer}\". Suggest improvements for clarity and market appeal.'\n        },\n        {\n            id: \"purpose\",\n            question: \"What is the main purpose or problem your AI project aims to solve?\",\n            questionAr: \"ما هو الهدف الرئيسي أو المشكلة التي يهدف مشروع الذكاء الاصطناعي لحلها؟\",\n            placeholder: \"Describe the core problem you want to address...\",\n            placeholderAr: \"صف المشكلة الأساسية التي تريد معالجتها...\",\n            aiSuggestion: \"Focus on a specific, measurable problem. Avoid being too broad or vague.\",\n            aiSuggestionAr: \"ركز على مشكلة محددة وقابلة للقياس. تجنب أن تكون عاماً أو غامضاً.\",\n            promptTemplate: 'Analyze this problem statement for an AI project: \"{answer}\". Help me make it more specific and actionable.'\n        },\n        {\n            id: \"targetUsers\",\n            question: \"Who are the primary users or beneficiaries of this project?\",\n            questionAr: \"من هم المستخدمون الأساسيون أو المستفيدون من هذا المشروع؟\",\n            placeholder: \"e.g., Customer service teams, Content creators, Students, etc.\",\n            placeholderAr: \"مثال: فرق خدمة العملاء، منشئو المحتوى، الطلاب، إلخ.\",\n            aiSuggestion: \"Be specific about user demographics, roles, and their current pain points.\",\n            aiSuggestionAr: \"كن محدداً حول التركيبة السكانية للمستخدمين وأدوارهم ونقاط الألم الحالية لديهم.\",\n            promptTemplate: 'Help me create detailed user personas for this target audience: \"{answer}\". Include their needs and challenges.'\n        }\n    ];\n    const handleFieldChange = (field, value)=>{\n        updateProjectDefinition({\n            [field]: value\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ModuleLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        title: \"Project Definition\",\n        titleAr: \"تعريف المشروع\",\n        subtitle: \"Define the scope, users, and goals of your AI project\",\n        subtitleAr: \"حدد نطاق مشروعك والمستخدمين والأهداف\",\n        emoji: \"\\uD83C\\uDFAF\",\n        moduleKey: \"project-definition\",\n        backLink: {\n            href: \"/\",\n            label: \"Back to Home\",\n            labelAr: \"العودة للرئيسية\"\n        },\n        nextLink: {\n            href: \"/context-map\",\n            label: \"Next: Context Map\",\n            labelAr: \"التالي: خريطة السياق\"\n        },\n        rightPanel: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_OutputPanel__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            moduleData: projectDefinition,\n            moduleName: \"Project Definition\",\n            moduleNameAr: \"تعريف المشروع\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\project-definition\\\\page.tsx\",\n            lineNumber: 69,\n            columnNumber: 9\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedOptionsPanel__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    moduleType: \"project-definition\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\project-definition\\\\page.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, this),\n                questions.map((question)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SmartQuestion__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        id: question.id,\n                        question: question.question,\n                        questionAr: question.questionAr,\n                        placeholder: question.placeholder,\n                        placeholderAr: question.placeholderAr,\n                        value: projectDefinition[question.id] || \"\",\n                        onChange: (value)=>handleFieldChange(question.id, value),\n                        type: question.type,\n                        aiSuggestion: question.aiSuggestion,\n                        aiSuggestionAr: question.aiSuggestionAr,\n                        promptTemplate: question.promptTemplate\n                    }, question.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\project-definition\\\\page.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, this))\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\project-definition\\\\page.tsx\",\n            lineNumber: 76,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\project-definition\\\\page.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n_s(ProjectDefinition, \"b1u7zlnbPXM3lhftuhCjgzVu/Sk=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_5__.useContextStore\n    ];\n});\n_c = ProjectDefinition;\nvar _c;\n$RefreshReg$(_c, \"ProjectDefinition\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/project-definition/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ModuleLayout.tsx":
/*!*****************************************!*\
  !*** ./src/components/ModuleLayout.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ModuleLayout; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Header */ \"(app-pages-browser)/./src/components/Header.tsx\");\n/* harmony import */ var _ProgressIndicator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ProgressIndicator */ \"(app-pages-browser)/./src/components/ProgressIndicator.tsx\");\n/* harmony import */ var _AutoSaveIndicator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AutoSaveIndicator */ \"(app-pages-browser)/./src/components/AutoSaveIndicator.tsx\");\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ModuleLayout(param) {\n    let { title, titleAr, subtitle, subtitleAr, emoji, moduleKey, backLink, nextLink, children, rightPanel } = param;\n    _s();\n    const { currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_4__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800\",\n        dir: isArabic ? \"rtl\" : \"ltr\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    title: isArabic ? titleAr : title,\n                    subtitle: isArabic ? subtitleAr : subtitle,\n                    emoji: emoji,\n                    backLink: backLink ? {\n                        href: backLink.href,\n                        label: isArabic ? backLink.labelAr : backLink.label\n                    } : undefined\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProgressIndicator__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    currentModule: moduleKey\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-2 gap-8 max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6 lg:order-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center mb-4\",\n                                        children: isArabic ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold text-gray-900 dark:text-white text-center ml-3\",\n                                                    children: \"الأسئلة الذكية\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                    lineNumber: 70,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl\",\n                                                    children: \"✍️\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl mr-3\",\n                                                    children: \"✍️\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                    lineNumber: 77,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold text-gray-900 dark:text-white text-center\",\n                                                    children: \"Smart Questions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                    lineNumber: 78,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 15\n                                    }, this),\n                                    children\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6 lg:order-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 sticky top-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center mb-4\",\n                                        children: isArabic ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold text-gray-900 dark:text-white text-center ml-3\",\n                                                    children: \"المخرجات المجمّعة\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl\",\n                                                    children: \"\\uD83D\\uDCC4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl mr-3\",\n                                                    children: \"\\uD83D\\uDCC4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold text-gray-900 dark:text-white text-center\",\n                                                    children: \"Generated Outputs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 15\n                                    }, this),\n                                    rightPanel\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this),\n                (backLink || nextLink) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mt-12 max-w-7xl mx-auto gap-6\",\n                    children: isArabic ? // Arabic layout: Next button on the right, Back button on the left\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            nextLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: nextLink.href,\n                                className: \"px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-all duration-200 hover:scale-105 shadow-sm hover:shadow-md font-medium\",\n                                children: nextLink.labelAr\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 19\n                            }, this),\n                            backLink ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: backLink.href,\n                                className: \"px-6 py-3 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500 transition-all duration-200 hover:scale-105 shadow-sm hover:shadow-md font-medium\",\n                                children: backLink.labelAr\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 19\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 19\n                            }, this)\n                        ]\n                    }, void 0, true) : // English layout: Back button on the left, Next button on the right\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            backLink ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: backLink.href,\n                                className: \"px-6 py-3 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500 transition-all duration-200 hover:scale-105 shadow-sm hover:shadow-md font-medium\",\n                                children: backLink.label\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 19\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 19\n                            }, this),\n                            nextLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: nextLink.href,\n                                className: \"px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-all duration-200 hover:scale-105 shadow-sm hover:shadow-md font-medium\",\n                                children: nextLink.label\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 19\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AutoSaveIndicator__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n            lineNumber: 47,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ModuleLayout.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n_s(ModuleLayout, \"TUcpdwZ+GxByrtxz7lNUh3/XmDk=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_4__.useContextStore\n    ];\n});\n_c = ModuleLayout;\nvar _c;\n$RefreshReg$(_c, \"ModuleLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ModuleLayout.tsx\n"));

/***/ })

});