'use client';

import ModuleLayout from '@/components/ModuleLayout';
import SmartQuestion from '@/components/SmartQuestion';
import OutputPanel from '@/components/OutputPanel';
import AdvancedOptionsPanel from '@/components/AdvancedOptionsPanel';
import { useContextStore } from '@/store/contextStore';

export default function TechnicalLayer() {
  const { technicalLayer, updateTechnicalLayer } = useContextStore();

  const questions = [
    {
      id: 'programmingLanguages',
      question: 'What programming languages will be used in your project?',
      questionAr: 'ما هي لغات البرمجة التي ستُستخدم في مشروعك؟',
      placeholder: 'e.g., Python, JavaScript, TypeScript, Java, C#...',
      placeholderAr: 'مثال: Python، JavaScript، TypeScript، Java، C#...',
      type: 'text' as const,
      aiSuggestion: 'Consider the AI/ML libraries available, team expertise, and performance requirements.',
      aiSuggestionAr: 'فكر في مكتبات الذكاء الاصطناعي المتاحة وخبرة الفريق ومتطلبات الأداء.',
      promptTemplate: 'Help me choose the best programming languages for this AI project: "{answer}". Consider scalability and ecosystem.'
    },
    {
      id: 'frameworks',
      question: 'What frameworks and libraries will you use?',
      questionAr: 'ما هي الأطر والمكتبات التي ستستخدمها؟',
      placeholder: 'e.g., TensorFlow, PyTorch, React, Next.js, FastAPI, Django...',
      placeholderAr: 'مثال: TensorFlow، PyTorch، React، Next.js، FastAPI، Django...',
      aiSuggestion: 'Choose frameworks that align with your project goals and team capabilities.',
      aiSuggestionAr: 'اختر الأطر التي تتماشى مع أهداف مشروعك وقدرات فريقك.',
      promptTemplate: 'Analyze this technology stack: "{answer}". Suggest optimizations and alternatives.'
    },
    {
      id: 'llmModels',
      question: 'Which LLM models and AI services will you integrate?',
      questionAr: 'ما هي نماذج اللغة الكبيرة وخدمات الذكاء الاصطناعي التي ستدمجها؟',
      placeholder: 'e.g., GPT-4, Claude, Gemini, Local models, OpenAI API, Anthropic API...',
      placeholderAr: 'مثال: GPT-4، Claude، Gemini، نماذج محلية، OpenAI API، Anthropic API...',
      aiSuggestion: 'Consider cost, performance, privacy requirements, and specific capabilities needed.',
      aiSuggestionAr: 'فكر في التكلفة والأداء ومتطلبات الخصوصية والقدرات المحددة المطلوبة.',
      promptTemplate: 'Help me design an LLM integration strategy for: "{answer}". Include fallback options.'
    },
    {
      id: 'databases',
      question: 'What databases and data storage solutions will you use?',
      questionAr: 'ما هي قواعد البيانات وحلول تخزين البيانات التي ستستخدمها؟',
      placeholder: 'e.g., PostgreSQL, MongoDB, Redis, Vector databases, Cloud storage...',
      placeholderAr: 'مثال: PostgreSQL، MongoDB، Redis، قواعد بيانات المتجهات، التخزين السحابي...',
      aiSuggestion: 'Consider data types, scalability, consistency requirements, and AI-specific needs.',
      aiSuggestionAr: 'فكر في أنواع البيانات وقابلية التوسع ومتطلبات الاتساق والاحتياجات الخاصة بالذكاء الاصطناعي.',
      promptTemplate: 'Design a data architecture for: "{answer}". Include backup and scaling strategies.'
    },
    {
      id: 'apis',
      question: 'What APIs and external services will you integrate?',
      questionAr: 'ما هي واجهات برمجة التطبيقات والخدمات الخارجية التي ستدمجها؟',
      placeholder: 'e.g., REST APIs, GraphQL, Third-party services, Payment gateways...',
      placeholderAr: 'مثال: REST APIs، GraphQL، خدمات طرف ثالث، بوابات الدفع...',
      aiSuggestion: 'Plan for API rate limits, authentication, error handling, and monitoring.',
      aiSuggestionAr: 'خطط لحدود معدل API والمصادقة ومعالجة الأخطاء والمراقبة.',
      promptTemplate: 'Create an API integration plan for: "{answer}". Include security and reliability measures.'
    },
    {
      id: 'infrastructure',
      question: 'What infrastructure and deployment strategy will you use?',
      questionAr: 'ما هي البنية التحتية واستراتيجية النشر التي ستستخدمها؟',
      placeholder: 'e.g., AWS, Google Cloud, Azure, Docker, Kubernetes, Serverless...',
      placeholderAr: 'مثال: AWS، Google Cloud، Azure، Docker، Kubernetes، Serverless...',
      aiSuggestion: 'Consider scalability, cost optimization, security, and maintenance requirements.',
      aiSuggestionAr: 'فكر في قابلية التوسع وتحسين التكلفة والأمان ومتطلبات الصيانة.',
      promptTemplate: 'Design an infrastructure architecture for: "{answer}". Include monitoring and scaling plans.'
    }
  ];

  const handleFieldChange = (field: keyof typeof technicalLayer, value: string) => {
    updateTechnicalLayer({ [field]: value });
  };

  return (
    <ModuleLayout
      title="Technical Layer"
      titleAr="الطبقة التقنية"
      subtitle="Define the technical architecture and tools for your AI project"
      subtitleAr="حدد الهندسة التقنية والأدوات لمشروع الذكاء الاصطناعي"
      emoji="⚙️"
      moduleKey="technical-layer"
      backLink={{
        href: "/emotional-tone",
        label: "Back to Emotional Tone",
        labelAr: "العودة للنبرة العاطفية"
      }}
      nextLink={{
        href: "/legal-risk",
        label: "Next: Legal & Privacy",
        labelAr: "التالي: القانونية والخصوصية"
      }}
      rightPanel={
        <OutputPanel 
          moduleData={technicalLayer}
          moduleName="Technical Layer"
          moduleNameAr="الطبقة التقنية"
        />
      }
    >
      <div className="space-y-6">
        {/* قسم الخيارات التقنية المتقدمة */}
        <AdvancedOptionsPanel moduleType="technical-layer" />

        {/* الأسئلة الأساسية */}
        {questions.map((question) => (
          <SmartQuestion
            key={question.id}
            id={question.id}
            question={question.question}
            questionAr={question.questionAr}
            placeholder={question.placeholder}
            placeholderAr={question.placeholderAr}
            value={technicalLayer[question.id as keyof typeof technicalLayer] || ''}
            onChange={(value) => handleFieldChange(question.id as keyof typeof technicalLayer, value)}
            type={question.type}
            aiSuggestion={question.aiSuggestion}
            aiSuggestionAr={question.aiSuggestionAr}
            promptTemplate={question.promptTemplate}
          />
        ))}
      </div>
    </ModuleLayout>
  );
}
