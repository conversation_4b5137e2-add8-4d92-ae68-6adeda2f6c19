"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/project-definition/page",{

/***/ "(app-pages-browser)/./src/store/contextStore.ts":
/*!***********************************!*\
  !*** ./src/store/contextStore.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useContextStore: function() { return /* binding */ useContextStore; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n\n\n// القيم الافتراضية\nconst initialState = {\n    projectDefinition: {\n        name: \"\",\n        purpose: \"\",\n        targetUsers: \"\",\n        goals: \"\",\n        scope: \"\",\n        timeline: \"\",\n        projectType: \"\",\n        projectNature: \"\",\n        geographicRegion: \"\",\n        targetPlatforms: [],\n        primaryLanguages: [],\n        complexity: \"\",\n        budget: \"\",\n        teamSize: \"\",\n        deploymentType: \"\"\n    },\n    contextMap: {\n        timeContext: \"\",\n        language: \"\",\n        location: \"\",\n        culturalContext: \"\",\n        behavioralAspects: \"\",\n        environmentalFactors: \"\"\n    },\n    emotionalTone: {\n        personality: \"\",\n        communicationStyle: \"\",\n        userExperience: \"\",\n        brandVoice: \"\",\n        emotionalIntelligence: \"\",\n        interactionFlow: \"\"\n    },\n    technicalLayer: {\n        programmingLanguages: \"\",\n        frameworks: \"\",\n        llmModels: \"\",\n        databases: \"\",\n        apis: \"\",\n        infrastructure: \"\",\n        architecturePattern: \"\",\n        scalingStrategy: \"\",\n        securityRequirements: \"\",\n        performanceTargets: \"\",\n        integrationNeeds: \"\",\n        monitoringTools: \"\"\n    },\n    legalRisk: {\n        privacyConcerns: \"\",\n        dataProtection: \"\",\n        compliance: \"\",\n        risks: \"\",\n        mitigation: \"\",\n        ethicalConsiderations: \"\"\n    },\n    currentLanguage: \"ar\",\n    outputFormat: \"markdown\",\n    showAdvancedOptions: true,\n    apiSettings: {\n        providers: [],\n        globalSettings: {\n            temperature: 0.7,\n            topP: 0.9,\n            maxTokens: 1000,\n            timeout: 30000\n        },\n        // Legacy support\n        openaiApiKey: \"\",\n        openrouterApiKey: \"\",\n        customModels: []\n    }\n};\n// إنشاء المتجر مع التخزين المستمر\nconst useContextStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        ...initialState,\n        updateProjectDefinition: (data)=>set((state)=>({\n                    projectDefinition: {\n                        ...state.projectDefinition,\n                        ...data\n                    }\n                })),\n        updateContextMap: (data)=>set((state)=>({\n                    contextMap: {\n                        ...state.contextMap,\n                        ...data\n                    }\n                })),\n        updateEmotionalTone: (data)=>set((state)=>({\n                    emotionalTone: {\n                        ...state.emotionalTone,\n                        ...data\n                    }\n                })),\n        updateTechnicalLayer: (data)=>set((state)=>({\n                    technicalLayer: {\n                        ...state.technicalLayer,\n                        ...data\n                    }\n                })),\n        updateLegalRisk: (data)=>set((state)=>({\n                    legalRisk: {\n                        ...state.legalRisk,\n                        ...data\n                    }\n                })),\n        setLanguage: (lang)=>set({\n                currentLanguage: lang\n            }),\n        setOutputFormat: (format)=>set({\n                outputFormat: format\n            }),\n        setShowAdvancedOptions: (show)=>set({\n                showAdvancedOptions: show\n            }),\n        setApiSettings: (settings)=>set({\n                apiSettings: {\n                    ...settings,\n                    providers: settings.providers || []\n                }\n            }),\n        // LLM Provider management functions\n        addProvider: (provider)=>set((state)=>{\n                const existingProviders = state.apiSettings.providers || [];\n                // تحقق من عدم وجود مقدم خدمة بنفس المعرف\n                if (existingProviders.find((p)=>p.id === provider.id)) {\n                    console.warn(\"Provider with id \".concat(provider.id, \" already exists\"));\n                    return state; // لا تضيف مقدم خدمة مكرر\n                }\n                // إضافة القيم الافتراضية للميزات المتقدمة\n                const enhancedProvider = {\n                    ...provider,\n                    priority: provider.priority || 5,\n                    isBackup: provider.isBackup || false,\n                    maxRequestsPerMinute: provider.maxRequestsPerMinute || 60,\n                    timeout: provider.timeout || 30,\n                    retryAttempts: provider.retryAttempts || 3,\n                    stats: provider.stats || {\n                        totalRequests: 0,\n                        successfulRequests: 0,\n                        failedRequests: 0,\n                        averageResponseTime: 0,\n                        totalTokensUsed: 0,\n                        totalCost: 0\n                    }\n                };\n                return {\n                    apiSettings: {\n                        ...state.apiSettings,\n                        providers: [\n                            ...existingProviders,\n                            enhancedProvider\n                        ]\n                    }\n                };\n            }),\n        updateProvider: (providerId, updates)=>set((state)=>({\n                    apiSettings: {\n                        ...state.apiSettings,\n                        providers: (state.apiSettings.providers || []).map((p)=>p.id === providerId ? {\n                                ...p,\n                                ...updates\n                            } : p)\n                    }\n                })),\n        removeProvider: (providerId)=>set((state)=>({\n                    apiSettings: {\n                        ...state.apiSettings,\n                        providers: (state.apiSettings.providers || []).filter((p)=>p.id !== providerId),\n                        defaultProvider: state.apiSettings.defaultProvider === providerId ? undefined : state.apiSettings.defaultProvider\n                    }\n                })),\n        validateProvider: async (providerId)=>{\n            const state = get();\n            const provider = (state.apiSettings.providers || []).find((p)=>p.id === providerId);\n            if (!provider) return false;\n            try {\n                // Update status to pending\n                state.updateProvider(providerId, {\n                    validationStatus: \"pending\",\n                    errorMessage: undefined\n                });\n                // Call validation API\n                const response = await fetch(\"/api/llm/validate\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        providerId: provider.id,\n                        apiKey: provider.apiKey,\n                        baseUrl: provider.baseUrl\n                    })\n                });\n                const result = await response.json();\n                if (result.valid) {\n                    state.updateProvider(providerId, {\n                        validationStatus: \"valid\",\n                        lastValidated: new Date(),\n                        errorMessage: undefined,\n                        isEnabled: true // تفعيل المقدم تلقائياً عند نجاح التحقق\n                    });\n                    return true;\n                } else {\n                    state.updateProvider(providerId, {\n                        validationStatus: \"invalid\",\n                        errorMessage: result.error || \"Validation failed\"\n                    });\n                    return false;\n                }\n            } catch (error) {\n                state.updateProvider(providerId, {\n                    validationStatus: \"error\",\n                    errorMessage: error instanceof Error ? error.message : \"Unknown error\"\n                });\n                return false;\n            }\n        },\n        getProvider: (providerId)=>{\n            const state = get();\n            return (state.apiSettings.providers || []).find((p)=>p.id === providerId);\n        },\n        getActiveProviders: ()=>{\n            const state = get();\n            return (state.apiSettings.providers || []).filter((p)=>p.isEnabled);\n        },\n        setDefaultProvider: (providerId)=>set((state)=>({\n                    apiSettings: {\n                        ...state.apiSettings,\n                        defaultProvider: providerId\n                    }\n                })),\n        // ميزات متقدمة للمزودين\n        getProvidersByPriority: ()=>{\n            const state = get();\n            return (state.apiSettings.providers || []).filter((p)=>p.isEnabled).sort((a, b)=>(b.priority || 5) - (a.priority || 5));\n        },\n        getBackupProviders: ()=>{\n            const state = get();\n            return (state.apiSettings.providers || []).filter((p)=>p.isEnabled && p.isBackup);\n        },\n        updateProviderStats: (id, stats)=>set((state)=>{\n                const providers = state.apiSettings.providers || [];\n                const providerIndex = providers.findIndex((p)=>p.id === id);\n                if (providerIndex !== -1) {\n                    const updatedProviders = [\n                        ...providers\n                    ];\n                    updatedProviders[providerIndex] = {\n                        ...updatedProviders[providerIndex],\n                        stats: {\n                            ...updatedProviders[providerIndex].stats,\n                            ...stats,\n                            lastUsed: new Date()\n                        }\n                    };\n                    return {\n                        apiSettings: {\n                            ...state.apiSettings,\n                            providers: updatedProviders\n                        }\n                    };\n                }\n                return state;\n            }),\n        getBestProvider: function() {\n            let criteria = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"reliability\";\n            const state = get();\n            const activeProviders = (state.apiSettings.providers || []).filter((p)=>p.isEnabled && !p.isBackup);\n            if (activeProviders.length === 0) return undefined;\n            switch(criteria){\n                case \"speed\":\n                    return activeProviders.reduce((best, current)=>{\n                        var _best_stats, _current_stats;\n                        const bestSpeed = ((_best_stats = best.stats) === null || _best_stats === void 0 ? void 0 : _best_stats.averageResponseTime) || Infinity;\n                        const currentSpeed = ((_current_stats = current.stats) === null || _current_stats === void 0 ? void 0 : _current_stats.averageResponseTime) || Infinity;\n                        return currentSpeed < bestSpeed ? current : best;\n                    });\n                case \"cost\":\n                    return activeProviders.reduce((best, current)=>{\n                        const bestCost = best.costPerToken || Infinity;\n                        const currentCost = current.costPerToken || Infinity;\n                        return currentCost < bestCost ? current : best;\n                    });\n                case \"reliability\":\n                default:\n                    return activeProviders.reduce((best, current)=>{\n                        const bestReliability = best.stats ? best.stats.successfulRequests / (best.stats.totalRequests || 1) : 0;\n                        const currentReliability = current.stats ? current.stats.successfulRequests / (current.stats.totalRequests || 1) : 0;\n                        return currentReliability > bestReliability ? current : best;\n                    });\n            }\n        },\n        resetAll: ()=>set(initialState),\n        // مسح جميع الإجابات فقط (الاحتفاظ بالإعدادات)\n        clearAllAnswers: ()=>set((state)=>({\n                    ...state,\n                    projectDefinition: {\n                        name: \"\",\n                        purpose: \"\",\n                        targetUsers: \"\",\n                        goals: \"\",\n                        scope: \"\",\n                        timeline: \"\"\n                    },\n                    contextMap: {\n                        timeContext: \"\",\n                        language: \"\",\n                        location: \"\",\n                        culturalContext: \"\",\n                        behavioralAspects: \"\",\n                        environmentalFactors: \"\"\n                    },\n                    emotionalTone: {\n                        personality: \"\",\n                        communicationStyle: \"\",\n                        userExperience: \"\",\n                        brandVoice: \"\",\n                        emotionalIntelligence: \"\",\n                        interactionFlow: \"\"\n                    },\n                    technicalLayer: {\n                        programmingLanguages: \"\",\n                        frameworks: \"\",\n                        llmModels: \"\",\n                        databases: \"\",\n                        apis: \"\",\n                        infrastructure: \"\"\n                    },\n                    legalRisk: {\n                        privacyConcerns: \"\",\n                        dataProtection: \"\",\n                        compliance: \"\",\n                        risks: \"\",\n                        mitigation: \"\",\n                        ethicalConsiderations: \"\"\n                    }\n                })),\n        getModuleData: (module)=>{\n            const state = get();\n            switch(module){\n                case \"project\":\n                    return state.projectDefinition;\n                case \"context\":\n                    return state.contextMap;\n                case \"emotional\":\n                    return state.emotionalTone;\n                case \"technical\":\n                    return state.technicalLayer;\n                case \"legal\":\n                    return state.legalRisk;\n                default:\n                    return {};\n            }\n        },\n        getAllData: ()=>{\n            const state = get();\n            return {\n                projectDefinition: state.projectDefinition,\n                contextMap: state.contextMap,\n                emotionalTone: state.emotionalTone,\n                technicalLayer: state.technicalLayer,\n                legalRisk: state.legalRisk\n            };\n        }\n    }), {\n    name: \"contextkit-storage\",\n    version: 1,\n    skipHydration: true\n}));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/contextStore.ts\n"));

/***/ })

});