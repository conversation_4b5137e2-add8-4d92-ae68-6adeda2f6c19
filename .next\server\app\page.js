/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZwYWdlJnBhZ2U9JTJGcGFnZSZhcHBQYXRocz0lMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGcGFnZS50c3gmYXBwRGlyPUMlM0ElNUNVc2VycyU1Q2ZhaXNzJTVDRGVza3RvcCU1Q0NvbnRleHRLaXQlNUNzcmMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUMlM0ElNUNVc2VycyU1Q2ZhaXNzJTVDRGVza3RvcCU1Q0NvbnRleHRLaXQmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxhQUFhLHNCQUFzQjtBQUNpRTtBQUNyQztBQUMvRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUM7QUFDakMsdUJBQXVCLGdKQUE4RjtBQUNySDtBQUNBLFNBQVM7QUFDVCxPQUFPO0FBQ1A7QUFDQSx5QkFBeUIsb0pBQWdHO0FBQ3pILG9CQUFvQiwwTkFBZ0Y7QUFDcEc7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ3VCO0FBQzZEO0FBQ3BGLDZCQUE2QixtQkFBbUI7QUFDaEQ7QUFDTztBQUNBO0FBQ1A7QUFDQTtBQUNBO0FBQ3VEO0FBQ3ZEO0FBQ08sd0JBQXdCLDhHQUFrQjtBQUNqRDtBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLENBQUM7O0FBRUQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jcmFmdGVyeS8/ODNlNiJdLCJzb3VyY2VzQ29udGVudCI6WyJcIlRVUkJPUEFDSyB7IHRyYW5zaXRpb246IG5leHQtc3NyIH1cIjtcbmltcG9ydCB7IEFwcFBhZ2VSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL2FwcC1wYWdlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmRcIjtcbi8vIFdlIGluamVjdCB0aGUgdHJlZSBhbmQgcGFnZXMgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IHRyZWUgPSB7XG4gICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICcnLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbJ19fUEFHRV9fJywge30sIHtcbiAgICAgICAgICBwYWdlOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxmYWlzc1xcXFxEZXNrdG9wXFxcXENvbnRleHRLaXRcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKSwgXCJDOlxcXFxVc2Vyc1xcXFxmYWlzc1xcXFxEZXNrdG9wXFxcXENvbnRleHRLaXRcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiXSxcbiAgICAgICAgICBcbiAgICAgICAgfV1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgJ2xheW91dCc6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGZhaXNzXFxcXERlc2t0b3BcXFxcQ29udGV4dEtpdFxcXFxzcmNcXFxcYXBwXFxcXGxheW91dC50c3hcIiksIFwiQzpcXFxcVXNlcnNcXFxcZmFpc3NcXFxcRGVza3RvcFxcXFxDb250ZXh0S2l0XFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiXSxcbidub3QtZm91bmQnOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCIpLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIl0sXG4gICAgICAgIFxuICAgICAgfVxuICAgICAgXVxuICAgICAgfS5jaGlsZHJlbjtcbmNvbnN0IHBhZ2VzID0gW1wiQzpcXFxcVXNlcnNcXFxcZmFpc3NcXFxcRGVza3RvcFxcXFxDb250ZXh0S2l0XFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIl07XG5leHBvcnQgeyB0cmVlLCBwYWdlcyB9O1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBHbG9iYWxFcnJvciB9IGZyb20gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3ItYm91bmRhcnlcIjtcbmNvbnN0IF9fbmV4dF9hcHBfcmVxdWlyZV9fID0gX193ZWJwYWNrX3JlcXVpcmVfX1xuY29uc3QgX19uZXh0X2FwcF9sb2FkX2NodW5rX18gPSAoKSA9PiBQcm9taXNlLnJlc29sdmUoKVxuZXhwb3J0IGNvbnN0IG9yaWdpbmFsUGF0aG5hbWUgPSBcIi9wYWdlXCI7XG5leHBvcnQgY29uc3QgX19uZXh0X2FwcF9fID0ge1xuICAgIHJlcXVpcmU6IF9fbmV4dF9hcHBfcmVxdWlyZV9fLFxuICAgIGxvYWRDaHVuazogX19uZXh0X2FwcF9sb2FkX2NodW5rX19cbn07XG5leHBvcnQgKiBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL2VudHJ5LWJhc2VcIjtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFBhZ2VSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1BBR0UsXG4gICAgICAgIHBhZ2U6IFwiL3BhZ2VcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL1wiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcIlwiLFxuICAgICAgICBhcHBQYXRoczogW11cbiAgICB9LFxuICAgIHVzZXJsYW5kOiB7XG4gICAgICAgIGxvYWRlclRyZWU6IHRyZWVcbiAgICB9XG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXBhZ2UuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CStoreHydration.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Cstyles%5C%5Cglass-effects.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Cstyles%5C%5Ctext-improvements.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CStoreHydration.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Cstyles%5C%5Cglass-effects.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Cstyles%5C%5Ctext-improvements.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/StoreHydration.tsx */ \"(ssr)/./src/components/StoreHydration.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ThemeProvider.tsx */ \"(ssr)/./src/components/ThemeProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2ZhaXNzJTVDJTVDRGVza3RvcCU1QyU1Q0NvbnRleHRLaXQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNmYWlzcyU1QyU1Q0Rlc2t0b3AlNUMlNUNDb250ZXh0S2l0JTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q1N0b3JlSHlkcmF0aW9uLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDZmFpc3MlNUMlNUNEZXNrdG9wJTVDJTVDQ29udGV4dEtpdCU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNUaGVtZVByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlRoZW1lUHJvdmlkZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDZmFpc3MlNUMlNUNEZXNrdG9wJTVDJTVDQ29udGV4dEtpdCU1QyU1Q3NyYyU1QyU1Q3N0eWxlcyU1QyU1Q2dsYXNzLWVmZmVjdHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2ZhaXNzJTVDJTVDRGVza3RvcCU1QyU1Q0NvbnRleHRLaXQlNUMlNUNzcmMlNUMlNUNzdHlsZXMlNUMlNUN0ZXh0LWltcHJvdmVtZW50cy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtMQUE0STtBQUM1STtBQUNBLGdMQUFpSiIsInNvdXJjZXMiOlsid2VicGFjazovL2NyYWZ0ZXJ5Lz8yZWRkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXGZhaXNzXFxcXERlc2t0b3BcXFxcQ29udGV4dEtpdFxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxTdG9yZUh5ZHJhdGlvbi50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlRoZW1lUHJvdmlkZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxmYWlzc1xcXFxEZXNrdG9wXFxcXENvbnRleHRLaXRcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcVGhlbWVQcm92aWRlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CStoreHydration.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Cstyles%5C%5Cglass-effects.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Cstyles%5C%5Ctext-improvements.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2ZhaXNzJTVDJTVDRGVza3RvcCU1QyU1Q0NvbnRleHRLaXQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQThGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY3JhZnRlcnkvPzM5YmYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxmYWlzc1xcXFxEZXNrdG9wXFxcXENvbnRleHRLaXRcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Header */ \"(ssr)/./src/components/Header.tsx\");\n/* harmony import */ var _components_ProjectSummaryCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ProjectSummaryCard */ \"(ssr)/./src/components/ProjectSummaryCard.tsx\");\n/* harmony import */ var _components_SmartRecommendations__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/SmartRecommendations */ \"(ssr)/./src/components/SmartRecommendations.tsx\");\n/* harmony import */ var _components_ProjectStats__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ProjectStats */ \"(ssr)/./src/components/ProjectStats.tsx\");\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n// import ProjectRoadmap from '@/components/ProjectRoadmap';\n\nfunction Home() {\n    const { currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_5__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 ${isArabic ? \"font-arabic\" : \"\"}`,\n        dir: isArabic ? \"rtl\" : \"ltr\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-16\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    title: isArabic ? \"Craftery\" : \"Craftery\",\n                    subtitle: isArabic ? \"منصة ذكية مدعومة بالذكاء الاصطناعي لبناء وتطوير الأفكار الإبداعية\" : \"AI-powered Idea Builder\",\n                    emoji: \"\\uD83E\\uDDE0\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProjectSummaryCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SmartRecommendations__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProjectStats__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl mb-4\",\n                                    children: \"\\uD83E\\uDDED\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-2 text-gray-900 dark:text-white\",\n                                    children: isArabic ? \"واجهة متعددة الصفحات\" : \"Multi-page Interface\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300\",\n                                    children: isArabic ? \"صفحات مخصصة لكل وحدة مع أسئلة ومخرجات مصممة خصيصاً.\" : \"Dedicated pages for each module with tailored prompts and outputs.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl mb-4\",\n                                    children: \"✍️\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-2 text-gray-900 dark:text-white\",\n                                    children: isArabic ? \"أسئلة موجهة\" : \"Guided Prompts\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300\",\n                                    children: isArabic ? \"أسئلة ذكية لتوجيه عملية تفكيرك عبر كل وحدة.\" : \"Smart questions to guide your thought process through each module.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl mb-4\",\n                                    children: \"\\uD83D\\uDCCB\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-2 text-gray-900 dark:text-white\",\n                                    children: isArabic ? \"نسخ المخرجات\" : \"Output Copying\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300\",\n                                    children: isArabic ? \"أزرار سهلة الاستخدام لنسخ المخرجات الكاملة أو الإجابات الفردية.\" : \"Easy-to-use buttons for copying entire outputs or individual responses.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl mb-4\",\n                                    children: \"\\uD83E\\uDDFE\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-2 text-gray-900 dark:text-white\",\n                                    children: isArabic ? \"مخرجات منظمة\" : \"Structured Outputs\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300\",\n                                    children: isArabic ? \"مخرجات بصيغة Markdown أو HTML أو JSON للتكامل السهل.\" : \"Outputs in Markdown, HTML, or JSON for easy integration.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl mb-4\",\n                                    children: \"\\uD83D\\uDCBE\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-2 text-gray-900 dark:text-white\",\n                                    children: isArabic ? \"حفظ تلقائي وجلسات\" : \"Auto-save & Sessions\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300\",\n                                    children: isArabic ? \"حفظ واسترجاع تلقائي لجلسات المستخدم للراحة.\" : \"Automatic saving and retrieval of user sessions for convenience.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl mb-4\",\n                                    children: \"\\uD83C\\uDF10\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-2 text-gray-900 dark:text-white\",\n                                    children: isArabic ? \"دعم متعدد اللغات\" : \"Multilingual Support\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300\",\n                                    children: isArabic ? \"واجهات باللغتين الإنجليزية والعربية لخدمة جمهور أوسع.\" : \"English and Arabic interfaces to cater to a wider audience.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold text-center mb-12 text-gray-900 dark:text-white\",\n                            children: [\n                                \"\\uD83D\\uDEE0️ \",\n                                isArabic ? \"الوحدات المتاحة\" : \"Available Modules\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/project-definition\",\n                                    className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-blue-500 hover:shadow-xl transition-shadow block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-3 text-gray-900 dark:text-white\",\n                                            children: [\n                                                \"\\uD83C\\uDFAF \",\n                                                isArabic ? \"تعريف المشروع\" : \"Project Definition\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: isArabic ? \"حدد نطاق مشروعك والمستخدمين والأهداف بأسئلة موجهة.\" : \"Outline the scope, users, and goals of your project with guided prompts.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/interactive\",\n                                    className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-green-500 hover:shadow-xl transition-shadow block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-3 text-gray-900 dark:text-white\",\n                                            children: [\n                                                \"\\uD83E\\uDD16 \",\n                                                isArabic ? \"المساعد التفاعلي\" : \"Interactive Assistant\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: isArabic ? \"تجربة محادثة متقدمة مع الذكاء الاصطناعي مع ميزات تفاعلية حديثة.\" : \"Advanced conversational experience with AI featuring modern interactive capabilities.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/context-map\",\n                                    className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-green-500 hover:shadow-xl transition-shadow block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-3 text-gray-900 dark:text-white\",\n                                            children: [\n                                                \"\\uD83D\\uDDFA️ \",\n                                                isArabic ? \"خريطة السياق\" : \"Context Map\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: isArabic ? \"حدد الوقت واللغة والموقع والجوانب السلوكية لمشروعك.\" : \"Define time, language, location, and behavioral aspects of your project.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/emotional-tone\",\n                                    className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-purple-500 hover:shadow-xl transition-shadow block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-3 text-gray-900 dark:text-white\",\n                                            children: [\n                                                \"✨ \",\n                                                isArabic ? \"النبرة والتجربة\" : \"Emotional Tone & Experience\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: isArabic ? \"احدد النبرة العامة وتجربة المستخدم المرغوبة لمشروعك.\" : \"Capture the overall tone and user experience desired for your project.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/technical-layer\",\n                                    className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-orange-500 hover:shadow-xl transition-shadow block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-3 text-gray-900 dark:text-white\",\n                                            children: [\n                                                \"⚙️ \",\n                                                isArabic ? \"الطبقة التقنية\" : \"Technical Layer\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: isArabic ? \"حدد المتطلبات التقنية والأدوات والنماذج المستخدمة.\" : \"Define technical requirements, tools, and models to be used.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/legal-risk\",\n                                    className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-red-500 hover:shadow-xl transition-shadow block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-3 text-gray-900 dark:text-white\",\n                                            children: [\n                                                \"\\uD83D\\uDD12 \",\n                                                isArabic ? \"التحديات والخصوصية\" : \"Legal & Privacy\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: isArabic ? \"تناول التحديات ومخاوف الخصوصية والقضايا التنظيمية.\" : \"Address challenges, privacy concerns, and regulatory issues.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/final-preview\",\n                                    className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-indigo-500 hover:shadow-xl transition-shadow block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-3 text-gray-900 dark:text-white\",\n                                            children: [\n                                                \"\\uD83D\\uDCCB \",\n                                                isArabic ? \"المعاينة النهائية\" : \"Final Preview\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: isArabic ? \"راجع واستخرج السياق الكامل لمشروعك.\" : \"Review and export your complete project context.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"/project-definition\",\n                            className: \"inline-block bg-blue-600 hover:bg-blue-700 text-white font-bold py-4 px-8 rounded-lg text-lg transition-colors duration-200\",\n                            children: isArabic ? \"ابدأ ببناء السياق\" : \"Start Building Context\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-4 text-gray-600 dark:text-gray-300\",\n                            children: isArabic ? \"هل أنت مستعد لإنشاء سياق منظم وقابل للتنفيذ لمشاريع الذكاء الاصطناعي؟\" : \"Ready to create structured, actionable context for your AI projects?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 221,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ClearContentButton.tsx":
/*!***********************************************!*\
  !*** ./src/components/ClearContentButton.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClearContentButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* harmony import */ var _barrel_optimize_names_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ClearContentButton() {\n    const { currentLanguage, clearAllAnswers } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_1__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    const translations = {\n        ar: {\n            clearAll: \"مسح جميع الإجابات\",\n            confirmMessage: \"هل أنت متأكد من مسح جميع الإجابات؟ لا يمكن التراجع عن هذا الإجراء.\",\n            success: \"تم مسح جميع الإجابات بنجاح\"\n        },\n        en: {\n            clearAll: \"Clear All Answers\",\n            confirmMessage: \"Are you sure you want to clear all answers? This action cannot be undone.\",\n            success: \"All answers cleared successfully\"\n        }\n    };\n    const t = translations[isArabic ? \"ar\" : \"en\"];\n    const handleClearAll = ()=>{\n        if (window.confirm(t.confirmMessage)) {\n            clearAllAnswers();\n            // Show success message\n            setTimeout(()=>{\n                alert(t.success);\n            }, 100);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: handleClearAll,\n        className: \"relative w-12 h-12 rounded-xl bg-gradient-to-br from-red-50 to-red-100 dark:from-gray-800 dark:to-gray-900 hover:from-red-100 hover:to-red-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden\",\n        title: t.clearAll,\n        \"aria-label\": t.clearAll,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-6 h-6 text-red-500 dark:text-red-400 group-hover:text-red-600 dark:group-hover:text-red-300 transition-colors duration-300\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ClearContentButton.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ClearContentButton.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ClearContentButton.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ClearContentButton.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9DbGVhckNvbnRlbnRCdXR0b24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUV1RDtBQUNqQjtBQUV2QixTQUFTRTtJQUN0QixNQUFNLEVBQUVDLGVBQWUsRUFBRUMsZUFBZSxFQUFFLEdBQUdKLG9FQUFlQTtJQUM1RCxNQUFNSyxXQUFXRixvQkFBb0I7SUFFckMsTUFBTUcsZUFBZTtRQUNuQkMsSUFBSTtZQUNGQyxVQUFVO1lBQ1ZDLGdCQUFnQjtZQUNoQkMsU0FBUztRQUNYO1FBQ0FDLElBQUk7WUFDRkgsVUFBVTtZQUNWQyxnQkFBZ0I7WUFDaEJDLFNBQVM7UUFDWDtJQUNGO0lBRUEsTUFBTUUsSUFBSU4sWUFBWSxDQUFDRCxXQUFXLE9BQU8sS0FBSztJQUU5QyxNQUFNUSxpQkFBaUI7UUFDckIsSUFBSUMsT0FBT0MsT0FBTyxDQUFDSCxFQUFFSCxjQUFjLEdBQUc7WUFDcENMO1lBRUEsdUJBQXVCO1lBQ3ZCWSxXQUFXO2dCQUNUQyxNQUFNTCxFQUFFRixPQUFPO1lBQ2pCLEdBQUc7UUFDTDtJQUNGO0lBRUEscUJBQ0UsOERBQUNRO1FBQ0NDLFNBQVNOO1FBQ1RPLFdBQVU7UUFDVkMsT0FBT1QsRUFBRUosUUFBUTtRQUNqQmMsY0FBWVYsRUFBRUosUUFBUTs7MEJBRXRCLDhEQUFDZTtnQkFBSUgsV0FBVTswQkFDYiw0RUFBQ25CLGtGQUFNQTtvQkFBQ21CLFdBQVU7Ozs7Ozs7Ozs7OzBCQUlwQiw4REFBQ0c7Z0JBQUlILFdBQVU7Ozs7Ozs7Ozs7OztBQUdyQiIsInNvdXJjZXMiOlsid2VicGFjazovL2NyYWZ0ZXJ5Ly4vc3JjL2NvbXBvbmVudHMvQ2xlYXJDb250ZW50QnV0dG9uLnRzeD9jMjRmIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlQ29udGV4dFN0b3JlIH0gZnJvbSAnQC9zdG9yZS9jb250ZXh0U3RvcmUnO1xuaW1wb3J0IHsgVHJhc2gyIH0gZnJvbSAnbHVjaWRlLXJlYWN0JztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQ2xlYXJDb250ZW50QnV0dG9uKCkge1xuICBjb25zdCB7IGN1cnJlbnRMYW5ndWFnZSwgY2xlYXJBbGxBbnN3ZXJzIH0gPSB1c2VDb250ZXh0U3RvcmUoKTtcbiAgY29uc3QgaXNBcmFiaWMgPSBjdXJyZW50TGFuZ3VhZ2UgPT09ICdhcic7XG5cbiAgY29uc3QgdHJhbnNsYXRpb25zID0ge1xuICAgIGFyOiB7XG4gICAgICBjbGVhckFsbDogJ9mF2LPYrSDYrNmF2YrYuSDYp9mE2KXYrNin2KjYp9iqJyxcbiAgICAgIGNvbmZpcm1NZXNzYWdlOiAn2YfZhCDYo9mG2Kog2YXYqtij2YPYryDZhdmGINmF2LPYrSDYrNmF2YrYuSDYp9mE2KXYrNin2KjYp9iq2J8g2YTYpyDZitmF2YPZhiDYp9mE2KrYsdin2KzYuSDYudmGINmH2LDYpyDYp9mE2KXYrNix2KfYoS4nLFxuICAgICAgc3VjY2VzczogJ9iq2YUg2YXYs9itINis2YXZiti5INin2YTYpdis2KfYqNin2Kog2KjZhtis2KfYrSdcbiAgICB9LFxuICAgIGVuOiB7XG4gICAgICBjbGVhckFsbDogJ0NsZWFyIEFsbCBBbnN3ZXJzJyxcbiAgICAgIGNvbmZpcm1NZXNzYWdlOiAnQXJlIHlvdSBzdXJlIHlvdSB3YW50IHRvIGNsZWFyIGFsbCBhbnN3ZXJzPyBUaGlzIGFjdGlvbiBjYW5ub3QgYmUgdW5kb25lLicsXG4gICAgICBzdWNjZXNzOiAnQWxsIGFuc3dlcnMgY2xlYXJlZCBzdWNjZXNzZnVsbHknXG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IHQgPSB0cmFuc2xhdGlvbnNbaXNBcmFiaWMgPyAnYXInIDogJ2VuJ107XG5cbiAgY29uc3QgaGFuZGxlQ2xlYXJBbGwgPSAoKSA9PiB7XG4gICAgaWYgKHdpbmRvdy5jb25maXJtKHQuY29uZmlybU1lc3NhZ2UpKSB7XG4gICAgICBjbGVhckFsbEFuc3dlcnMoKTtcblxuICAgICAgLy8gU2hvdyBzdWNjZXNzIG1lc3NhZ2VcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICBhbGVydCh0LnN1Y2Nlc3MpO1xuICAgICAgfSwgMTAwKTtcbiAgICB9XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8YnV0dG9uXG4gICAgICBvbkNsaWNrPXtoYW5kbGVDbGVhckFsbH1cbiAgICAgIGNsYXNzTmFtZT1cInJlbGF0aXZlIHctMTIgaC0xMiByb3VuZGVkLXhsIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tcmVkLTUwIHRvLXJlZC0xMDAgZGFyazpmcm9tLWdyYXktODAwIGRhcms6dG8tZ3JheS05MDAgaG92ZXI6ZnJvbS1yZWQtMTAwIGhvdmVyOnRvLXJlZC0yMDAgZGFyazpob3Zlcjpmcm9tLWdyYXktNzAwIGRhcms6aG92ZXI6dG8tZ3JheS04MDAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGVhc2UtaW4tb3V0IHNoYWRvdy1sZyBob3ZlcjpzaGFkb3cteGwgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBkYXJrOmJvcmRlci1ncmF5LTYwMCBncm91cCBvdmVyZmxvdy1oaWRkZW5cIlxuICAgICAgdGl0bGU9e3QuY2xlYXJBbGx9XG4gICAgICBhcmlhLWxhYmVsPXt0LmNsZWFyQWxsfVxuICAgID5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICA8VHJhc2gyIGNsYXNzTmFtZT1cInctNiBoLTYgdGV4dC1yZWQtNTAwIGRhcms6dGV4dC1yZWQtNDAwIGdyb3VwLWhvdmVyOnRleHQtcmVkLTYwMCBkYXJrOmdyb3VwLWhvdmVyOnRleHQtcmVkLTMwMCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0zMDBcIiAvPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBTdWJ0bGUgZ2xvdyBlZmZlY3QgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctZ3JhZGllbnQtdG8tciBmcm9tLXRyYW5zcGFyZW50IHZpYS13aGl0ZS8xMCB0by10cmFuc3BhcmVudCBvcGFjaXR5LTAgZ3JvdXAtaG92ZXI6b3BhY2l0eS0xMDAgdHJhbnNpdGlvbi1vcGFjaXR5IGR1cmF0aW9uLTMwMFwiIC8+XG4gICAgPC9idXR0b24+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlQ29udGV4dFN0b3JlIiwiVHJhc2gyIiwiQ2xlYXJDb250ZW50QnV0dG9uIiwiY3VycmVudExhbmd1YWdlIiwiY2xlYXJBbGxBbnN3ZXJzIiwiaXNBcmFiaWMiLCJ0cmFuc2xhdGlvbnMiLCJhciIsImNsZWFyQWxsIiwiY29uZmlybU1lc3NhZ2UiLCJzdWNjZXNzIiwiZW4iLCJ0IiwiaGFuZGxlQ2xlYXJBbGwiLCJ3aW5kb3ciLCJjb25maXJtIiwic2V0VGltZW91dCIsImFsZXJ0IiwiYnV0dG9uIiwib25DbGljayIsImNsYXNzTmFtZSIsInRpdGxlIiwiYXJpYS1sYWJlbCIsImRpdiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ClearContentButton.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _ThemeToggle__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ThemeToggle */ \"(ssr)/./src/components/ThemeToggle.tsx\");\n/* harmony import */ var _LanguageToggle__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./LanguageToggle */ \"(ssr)/./src/components/LanguageToggle.tsx\");\n/* harmony import */ var _ClearContentButton__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ClearContentButton */ \"(ssr)/./src/components/ClearContentButton.tsx\");\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction Header({ title, subtitle, backLink, emoji }) {\n    const { currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_6__.useContextStore)();\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    const isArabic = mounted ? currentLanguage === \"ar\" : false;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-1/2 right-4 transform -translate-y-1/2 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white/90 dark:bg-gray-800/90 backdrop-blur-md rounded-2xl shadow-xl border border-gray-200/50 dark:border-gray-700/50 p-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                className: \"relative w-12 h-12 rounded-xl bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 hover:from-gray-100 hover:to-gray-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden\",\n                                title: isArabic ? \"الصفحة الرئيسية\" : \"Home\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-6 h-6 text-gray-600 dark:text-gray-300 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/settings\",\n                                className: \"relative w-12 h-12 rounded-xl bg-gradient-to-br from-purple-50 to-violet-100 dark:from-gray-800 dark:to-gray-900 hover:from-purple-100 hover:to-violet-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden\",\n                                title: isArabic ? \"إعدادات API\" : \"API Settings\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-6 h-6 text-purple-600 dark:text-purple-400 transition-all duration-300 group-hover:rotate-90\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClearContentButton__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LanguageToggle__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ThemeToggle__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-8 pt-4\",\n                children: [\n                    backLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        href: backLink.href,\n                        className: \"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mb-4 inline-block transition-colors\",\n                        children: backLink.label\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-2 text-center\",\n                        children: [\n                            emoji && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-2\",\n                                children: emoji\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 21\n                            }, this),\n                            title\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this),\n                    subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto text-center\",\n                        children: subtitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/LanguageToggle.tsx":
/*!*******************************************!*\
  !*** ./src/components/LanguageToggle.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LanguageToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction LanguageToggle() {\n    const { currentLanguage, setLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_1__.useContextStore)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: ()=>setLanguage(currentLanguage === \"ar\" ? \"en\" : \"ar\"),\n        className: \"relative w-12 h-12 rounded-xl bg-gradient-to-br from-green-50 to-emerald-100 dark:from-gray-800 dark:to-gray-900 hover:from-green-100 hover:to-emerald-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden\",\n        \"aria-label\": currentLanguage === \"ar\" ? \"Switch to English\" : \"التبديل إلى العربية\",\n        title: currentLanguage === \"ar\" ? \"Switch to English\" : \"التبديل إلى العربية\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `text-sm font-bold text-blue-600 dark:text-blue-400 transition-all duration-500 ease-in-out ${currentLanguage === \"en\" ? \"opacity-100 rotate-0 scale-100\" : \"opacity-0 rotate-180 scale-0\"}`,\n                        children: \"EN\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `text-sm font-bold text-green-600 dark:text-green-400 absolute transition-all duration-500 ease-in-out font-arabic ${currentLanguage === \"ar\" ? \"opacity-100 rotate-0 scale-100\" : \"opacity-0 -rotate-180 scale-0\"}`,\n                        children: \"عر\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/LanguageToggle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ProjectStats.tsx":
/*!*****************************************!*\
  !*** ./src/components/ProjectStats.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProjectStats)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ProjectStats() {\n    const { projectDefinition, contextMap, emotionalTone, technicalLayer, legalRisk, currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_1__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    // حساب إحصائيات المشروع\n    const calculateStats = ()=>{\n        const modules = [\n            {\n                name: \"projectDefinition\",\n                data: projectDefinition\n            },\n            {\n                name: \"contextMap\",\n                data: contextMap\n            },\n            {\n                name: \"emotionalTone\",\n                data: emotionalTone\n            },\n            {\n                name: \"technicalLayer\",\n                data: technicalLayer\n            },\n            {\n                name: \"legalRisk\",\n                data: legalRisk\n            }\n        ];\n        let totalFields = 0;\n        let completedFields = 0;\n        let totalWords = 0;\n        let advancedOptionsUsed = 0;\n        modules.forEach((module)=>{\n            const fields = Object.entries(module.data);\n            totalFields += fields.length;\n            fields.forEach(([key, value])=>{\n                if (value) {\n                    if (Array.isArray(value)) {\n                        if (value.length > 0) {\n                            completedFields++;\n                            advancedOptionsUsed++;\n                            totalWords += value.join(\" \").split(\" \").length;\n                        }\n                    } else if (typeof value === \"string\" && value.trim()) {\n                        completedFields++;\n                        totalWords += value.split(\" \").length;\n                        // تحقق من الخيارات المتقدمة\n                        if ([\n                            \"projectType\",\n                            \"complexity\",\n                            \"teamSize\",\n                            \"budget\",\n                            \"deploymentType\",\n                            \"architecturePattern\",\n                            \"scalingStrategy\",\n                            \"securityRequirements\",\n                            \"performanceTargets\",\n                            \"integrationNeeds\",\n                            \"monitoringTools\"\n                        ].includes(key)) {\n                            advancedOptionsUsed++;\n                        }\n                    }\n                }\n            });\n        });\n        const completionPercentage = totalFields > 0 ? Math.round(completedFields / totalFields * 100) : 0;\n        return {\n            totalFields,\n            completedFields,\n            completionPercentage,\n            totalWords,\n            advancedOptionsUsed,\n            estimatedReadingTime: Math.ceil(totalWords / 200) // متوسط 200 كلمة في الدقيقة\n        };\n    };\n    const stats = calculateStats();\n    // تحديد مستوى التقدم\n    const getProgressLevel = (percentage)=>{\n        if (percentage >= 80) return {\n            level: \"excellent\",\n            color: \"green\",\n            icon: \"\\uD83C\\uDF89\"\n        };\n        if (percentage >= 60) return {\n            level: \"good\",\n            color: \"blue\",\n            icon: \"\\uD83D\\uDC4D\"\n        };\n        if (percentage >= 40) return {\n            level: \"moderate\",\n            color: \"yellow\",\n            icon: \"⚡\"\n        };\n        if (percentage >= 20) return {\n            level: \"started\",\n            color: \"orange\",\n            icon: \"\\uD83D\\uDE80\"\n        };\n        return {\n            level: \"beginning\",\n            color: \"gray\",\n            icon: \"\\uD83D\\uDCDD\"\n        };\n    };\n    const progress = getProgressLevel(stats.completionPercentage);\n    const getProgressMessage = ()=>{\n        const messages = {\n            excellent: {\n                ar: \"ممتاز! مشروعك مكتمل تقريباً\",\n                en: \"Excellent! Your project is nearly complete\"\n            },\n            good: {\n                ar: \"جيد جداً! تقدم ملحوظ\",\n                en: \"Very good! Notable progress\"\n            },\n            moderate: {\n                ar: \"تقدم جيد، استمر!\",\n                en: \"Good progress, keep going!\"\n            },\n            started: {\n                ar: \"بداية جيدة!\",\n                en: \"Good start!\"\n            },\n            beginning: {\n                ar: \"ابدأ رحلتك!\",\n                en: \"Start your journey!\"\n            }\n        };\n        return messages[progress.level]?.[isArabic ? \"ar\" : \"en\"] || \"\";\n    };\n    if (stats.completedFields === 0) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl p-6 border border-green-200 dark:border-green-800\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `text-center mb-6 ${isArabic ? \"text-right\" : \"text-left\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2\",\n                        children: isArabic ? \"\\uD83D\\uDCCA إحصائيات المشروع\" : \"\\uD83D\\uDCCA Project Statistics\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                        children: isArabic ? \"تتبع تقدمك وإنجازاتك\" : \"Track your progress and achievements\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                children: isArabic ? \"نسبة الإكمال\" : \"Completion Rate\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-bold text-gray-900 dark:text-gray-100\",\n                                children: [\n                                    stats.completionPercentage,\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `h-3 rounded-full transition-all duration-500 ease-out bg-gradient-to-r ${progress.color === \"green\" ? \"from-green-400 to-emerald-500\" : progress.color === \"blue\" ? \"from-blue-400 to-cyan-500\" : progress.color === \"yellow\" ? \"from-yellow-400 to-orange-500\" : progress.color === \"orange\" ? \"from-orange-400 to-red-500\" : \"from-gray-400 to-gray-500\"}`,\n                            style: {\n                                width: `${stats.completionPercentage}%`\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `mt-2 text-center ${isArabic ? \"text-right\" : \"text-left\"}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                            children: [\n                                progress.icon,\n                                \" \",\n                                getProgressMessage()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `bg-white dark:bg-gray-800 rounded-lg p-4 text-center ${isArabic ? \"text-right\" : \"text-left\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-blue-600 dark:text-blue-400\",\n                                children: stats.completedFields\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-500 dark:text-gray-400 mt-1\",\n                                children: isArabic ? \"حقول مكتملة\" : \"Completed Fields\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-400 dark:text-gray-500\",\n                                children: isArabic ? `من أصل ${stats.totalFields}` : `out of ${stats.totalFields}`\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `bg-white dark:bg-gray-800 rounded-lg p-4 text-center ${isArabic ? \"text-right\" : \"text-left\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-purple-600 dark:text-purple-400\",\n                                children: stats.totalWords\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-500 dark:text-gray-400 mt-1\",\n                                children: isArabic ? \"إجمالي الكلمات\" : \"Total Words\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-400 dark:text-gray-500\",\n                                children: isArabic ? `${stats.estimatedReadingTime} دقائق قراءة` : `${stats.estimatedReadingTime} min read`\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `bg-white dark:bg-gray-800 rounded-lg p-4 text-center ${isArabic ? \"text-right\" : \"text-left\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-green-600 dark:text-green-400\",\n                                children: stats.advancedOptionsUsed\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-500 dark:text-gray-400 mt-1\",\n                                children: isArabic ? \"خيارات متقدمة\" : \"Advanced Options\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-400 dark:text-gray-500\",\n                                children: isArabic ? \"مستخدمة\" : \"Used\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `bg-white dark:bg-gray-800 rounded-lg p-4 text-center ${isArabic ? \"text-right\" : \"text-left\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-orange-600 dark:text-orange-400\",\n                                children: [\n                                    Math.round(stats.advancedOptionsUsed / Math.max(stats.completedFields, 1) * 100),\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-500 dark:text-gray-400 mt-1\",\n                                children: isArabic ? \"مستوى التخصيص\" : \"Customization Level\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-400 dark:text-gray-500\",\n                                children: isArabic ? \"متقدم\" : \"Advanced\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                lineNumber: 150,\n                columnNumber: 7\n            }, this),\n            stats.completionPercentage < 100 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 p-4 bg-blue-50 dark:bg-blue-900/30 rounded-lg border border-blue-200 dark:border-blue-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg\",\n                            children: \"\\uD83D\\uDCA1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: isArabic ? \"text-right\" : \"text-left\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium text-blue-900 dark:text-blue-100 mb-1\",\n                                    children: isArabic ? \"نصيحة للتحسين\" : \"Improvement Tip\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-blue-700 dark:text-blue-300\",\n                                    children: isArabic ? `أكمل ${stats.totalFields - stats.completedFields} حقل إضافي للحصول على سياق أكثر شمولية لمشروعك.` : `Complete ${stats.totalFields - stats.completedFields} more fields to get a more comprehensive context for your project.`\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                lineNumber: 202,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n        lineNumber: 107,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ProjectStats.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ProjectSummaryCard.tsx":
/*!***********************************************!*\
  !*** ./src/components/ProjectSummaryCard.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProjectSummaryCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* harmony import */ var _lib_projectOptions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/projectOptions */ \"(ssr)/./src/lib/projectOptions.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ProjectSummaryCard() {\n    const { projectDefinition, currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_1__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    const getOptionLabel = (options, id)=>{\n        const option = options.find((opt)=>opt.id === id);\n        return option ? isArabic ? option.labelAr : option.label : \"\";\n    };\n    const getMultipleOptionsLabels = (options, ids)=>{\n        return ids.map((id)=>getOptionLabel(options, id)).filter(Boolean).join(\", \");\n    };\n    const hasAdvancedOptions = projectDefinition.projectType || projectDefinition.targetPlatforms?.length > 0 || projectDefinition.primaryLanguages?.length > 0 || projectDefinition.complexity || projectDefinition.budget || projectDefinition.teamSize || projectDefinition.deploymentType;\n    if (!hasAdvancedOptions) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-800\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `text-center mb-4 ${isArabic ? \"text-right\" : \"text-left\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2\",\n                        children: isArabic ? \"\\uD83D\\uDCCB ملخص تخصيص المشروع\" : \"\\uD83D\\uDCCB Project Customization Summary\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                        children: isArabic ? \"الخيارات المتقدمة المحددة لمشروعك\" : \"Advanced options selected for your project\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                children: [\n                    projectDefinition.projectType && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `bg-white dark:bg-gray-800 rounded-lg p-4 ${isArabic ? \"text-right\" : \"text-left\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: \"\\uD83C\\uDFAF\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-gray-700 dark:text-gray-300\",\n                                        children: isArabic ? \"نوع المشروع\" : \"Project Type\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                children: getOptionLabel(_lib_projectOptions__WEBPACK_IMPORTED_MODULE_2__.PROJECT_TYPES, projectDefinition.projectType)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, this),\n                    projectDefinition.complexity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `bg-white dark:bg-gray-800 rounded-lg p-4 ${isArabic ? \"text-right\" : \"text-left\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: \"⚡\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-gray-700 dark:text-gray-300\",\n                                        children: isArabic ? \"مستوى التعقيد\" : \"Complexity Level\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                children: getOptionLabel(_lib_projectOptions__WEBPACK_IMPORTED_MODULE_2__.COMPLEXITY_LEVELS, projectDefinition.complexity)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, this),\n                    projectDefinition.targetPlatforms && projectDefinition.targetPlatforms.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `bg-white dark:bg-gray-800 rounded-lg p-4 ${isArabic ? \"text-right\" : \"text-left\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: \"\\uD83D\\uDCF1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-gray-700 dark:text-gray-300\",\n                                        children: isArabic ? \"المنصات المستهدفة\" : \"Target Platforms\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                children: getMultipleOptionsLabels(_lib_projectOptions__WEBPACK_IMPORTED_MODULE_2__.TARGET_PLATFORMS, projectDefinition.targetPlatforms)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, this),\n                    projectDefinition.primaryLanguages && projectDefinition.primaryLanguages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `bg-white dark:bg-gray-800 rounded-lg p-4 ${isArabic ? \"text-right\" : \"text-left\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: \"\\uD83D\\uDCBB\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-gray-700 dark:text-gray-300\",\n                                        children: isArabic ? \"لغات البرمجة\" : \"Programming Languages\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                children: getMultipleOptionsLabels(_lib_projectOptions__WEBPACK_IMPORTED_MODULE_2__.PROGRAMMING_LANGUAGES, projectDefinition.primaryLanguages)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, this),\n                    projectDefinition.teamSize && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `bg-white dark:bg-gray-800 rounded-lg p-4 ${isArabic ? \"text-right\" : \"text-left\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: \"\\uD83D\\uDC65\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-gray-700 dark:text-gray-300\",\n                                        children: isArabic ? \"حجم الفريق\" : \"Team Size\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                children: getOptionLabel(_lib_projectOptions__WEBPACK_IMPORTED_MODULE_2__.TEAM_SIZES, projectDefinition.teamSize)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, this),\n                    projectDefinition.budget && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `bg-white dark:bg-gray-800 rounded-lg p-4 ${isArabic ? \"text-right\" : \"text-left\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: \"\\uD83D\\uDCB0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-gray-700 dark:text-gray-300\",\n                                        children: isArabic ? \"نطاق الميزانية\" : \"Budget Range\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                children: getOptionLabel(_lib_projectOptions__WEBPACK_IMPORTED_MODULE_2__.BUDGET_RANGES, projectDefinition.budget)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this),\n                    projectDefinition.deploymentType && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `bg-white dark:bg-gray-800 rounded-lg p-4 md:col-span-2 ${isArabic ? \"text-right\" : \"text-left\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: \"☁️\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-gray-700 dark:text-gray-300\",\n                                        children: isArabic ? \"نوع النشر\" : \"Deployment Type\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                children: getOptionLabel(_lib_projectOptions__WEBPACK_IMPORTED_MODULE_2__.DEPLOYMENT_TYPES, projectDefinition.deploymentType)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 p-4 bg-blue-50 dark:bg-blue-900/30 rounded-lg border border-blue-200 dark:border-blue-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg\",\n                            children: \"\\uD83D\\uDCA1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: isArabic ? \"text-right\" : \"text-left\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium text-blue-900 dark:text-blue-100 mb-1\",\n                                    children: isArabic ? \"نصيحة ذكية\" : \"Smart Tip\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-blue-700 dark:text-blue-300\",\n                                    children: isArabic ? \"هذه الخيارات ستساعد الذكاء الاصطناعي في تقديم اقتراحات أكثر دقة وتخصصاً لمشروعك.\" : \"These options will help AI provide more accurate and specialized suggestions for your project.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ProjectSummaryCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SmartRecommendations.tsx":
/*!*************************************************!*\
  !*** ./src/components/SmartRecommendations.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SmartRecommendations)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction SmartRecommendations() {\n    const { projectDefinition, technicalLayer, currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_1__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    const generateRecommendations = ()=>{\n        const recommendations = [];\n        // توصيات بناءً على نوع المشروع\n        if (projectDefinition.projectType === \"mobile-app\") {\n            if (!projectDefinition.targetPlatforms?.includes(\"android\") && !projectDefinition.targetPlatforms?.includes(\"ios\")) {\n                recommendations.push({\n                    id: \"mobile-platforms\",\n                    title: \"Select Mobile Platforms\",\n                    titleAr: \"اختر منصات الجوال\",\n                    description: \"Consider targeting Android and/or iOS for maximum reach\",\n                    descriptionAr: \"فكر في استهداف أندرويد و/أو iOS للوصول الأقصى\",\n                    icon: \"\\uD83D\\uDCF1\",\n                    priority: \"high\",\n                    category: \"Platform\",\n                    categoryAr: \"المنصة\"\n                });\n            }\n            if (!projectDefinition.primaryLanguages?.some((lang)=>[\n                    \"kotlin\",\n                    \"swift\",\n                    \"dart\"\n                ].includes(lang))) {\n                recommendations.push({\n                    id: \"mobile-languages\",\n                    title: \"Mobile Development Languages\",\n                    titleAr: \"لغات تطوير الجوال\",\n                    description: \"Consider Kotlin for Android, Swift for iOS, or Dart/Flutter for cross-platform\",\n                    descriptionAr: \"فكر في Kotlin لأندرويد، Swift لـ iOS، أو Dart/Flutter للمنصات المتعددة\",\n                    icon: \"\\uD83D\\uDCBB\",\n                    priority: \"high\",\n                    category: \"Technology\",\n                    categoryAr: \"التقنية\"\n                });\n            }\n        }\n        // توصيات بناءً على نوع المشروع - تطبيق ويب\n        if (projectDefinition.projectType === \"web-app\") {\n            if (!projectDefinition.primaryLanguages?.includes(\"javascript\")) {\n                recommendations.push({\n                    id: \"web-languages\",\n                    title: \"Web Development Stack\",\n                    titleAr: \"مجموعة تطوير الويب\",\n                    description: \"JavaScript/TypeScript is essential for modern web development\",\n                    descriptionAr: \"JavaScript/TypeScript ضروري لتطوير الويب الحديث\",\n                    icon: \"\\uD83C\\uDF10\",\n                    priority: \"high\",\n                    category: \"Technology\",\n                    categoryAr: \"التقنية\"\n                });\n            }\n            if (!technicalLayer.architecturePattern) {\n                recommendations.push({\n                    id: \"web-architecture\",\n                    title: \"Choose Architecture Pattern\",\n                    titleAr: \"اختر نمط الهندسة المعمارية\",\n                    description: \"Consider microservices for scalability or monolithic for simplicity\",\n                    descriptionAr: \"فكر في المايكروسيرفس للتوسع أو الأحادي للبساطة\",\n                    icon: \"\\uD83C\\uDFD7️\",\n                    priority: \"medium\",\n                    category: \"Architecture\",\n                    categoryAr: \"الهندسة المعمارية\"\n                });\n            }\n        }\n        // توصيات بناءً على مستوى التعقيد\n        if (projectDefinition.complexity === \"enterprise\") {\n            if (!technicalLayer.securityRequirements) {\n                recommendations.push({\n                    id: \"enterprise-security\",\n                    title: \"Enterprise Security Requirements\",\n                    titleAr: \"متطلبات الأمان المؤسسي\",\n                    description: \"Enterprise projects need comprehensive security measures\",\n                    descriptionAr: \"المشاريع المؤسسية تحتاج إجراءات أمان شاملة\",\n                    icon: \"\\uD83D\\uDD12\",\n                    priority: \"high\",\n                    category: \"Security\",\n                    categoryAr: \"الأمان\"\n                });\n            }\n            if (!technicalLayer.scalingStrategy) {\n                recommendations.push({\n                    id: \"enterprise-scaling\",\n                    title: \"Scaling Strategy Required\",\n                    titleAr: \"استراتيجية التوسع مطلوبة\",\n                    description: \"Plan for horizontal scaling and load balancing\",\n                    descriptionAr: \"خطط للتوسع الأفقي وتوزيع الأحمال\",\n                    icon: \"\\uD83D\\uDCC8\",\n                    priority: \"high\",\n                    category: \"Performance\",\n                    categoryAr: \"الأداء\"\n                });\n            }\n        }\n        // توصيات بناءً على حجم الفريق\n        if (projectDefinition.teamSize === \"large\" || projectDefinition.teamSize === \"enterprise\") {\n            if (!technicalLayer.monitoringTools) {\n                recommendations.push({\n                    id: \"team-monitoring\",\n                    title: \"Team Monitoring Tools\",\n                    titleAr: \"أدوات مراقبة الفريق\",\n                    description: \"Large teams need comprehensive monitoring and logging\",\n                    descriptionAr: \"الفرق الكبيرة تحتاج مراقبة وتسجيل شامل\",\n                    icon: \"\\uD83D\\uDCCA\",\n                    priority: \"medium\",\n                    category: \"Operations\",\n                    categoryAr: \"العمليات\"\n                });\n            }\n        }\n        // توصيات بناءً على نوع النشر\n        if (projectDefinition.deploymentType === \"cloud\") {\n            if (!technicalLayer.scalingStrategy) {\n                recommendations.push({\n                    id: \"cloud-scaling\",\n                    title: \"Cloud Auto-scaling\",\n                    titleAr: \"التوسع التلقائي السحابي\",\n                    description: \"Leverage cloud auto-scaling capabilities\",\n                    descriptionAr: \"استفد من قدرات التوسع التلقائي السحابي\",\n                    icon: \"☁️\",\n                    priority: \"medium\",\n                    category: \"Infrastructure\",\n                    categoryAr: \"البنية التحتية\"\n                });\n            }\n        }\n        // توصيات بناءً على الميزانية\n        if (projectDefinition.budget === \"startup\") {\n            recommendations.push({\n                id: \"startup-budget\",\n                title: \"Cost-Effective Solutions\",\n                titleAr: \"حلول فعالة من ناحية التكلفة\",\n                description: \"Consider open-source tools and serverless architecture\",\n                descriptionAr: \"فكر في الأدوات مفتوحة المصدر والهندسة بلا خادم\",\n                icon: \"\\uD83D\\uDCB0\",\n                priority: \"high\",\n                category: \"Budget\",\n                categoryAr: \"الميزانية\"\n            });\n        }\n        // توصيات عامة للذكاء الاصطناعي\n        if (projectDefinition.projectType === \"ai-model\" || projectDefinition.name?.toLowerCase().includes(\"ai\")) {\n            if (!projectDefinition.primaryLanguages?.includes(\"python\")) {\n                recommendations.push({\n                    id: \"ai-python\",\n                    title: \"Python for AI Development\",\n                    titleAr: \"Python لتطوير الذكاء الاصطناعي\",\n                    description: \"Python is the most popular language for AI/ML projects\",\n                    descriptionAr: \"Python هي اللغة الأكثر شعبية لمشاريع الذكاء الاصطناعي\",\n                    icon: \"\\uD83D\\uDC0D\",\n                    priority: \"high\",\n                    category: \"Technology\",\n                    categoryAr: \"التقنية\"\n                });\n            }\n        }\n        return recommendations.sort((a, b)=>{\n            const priorityOrder = {\n                high: 3,\n                medium: 2,\n                low: 1\n            };\n            return priorityOrder[b.priority] - priorityOrder[a.priority];\n        });\n    };\n    const recommendations = generateRecommendations();\n    if (recommendations.length === 0) {\n        return null;\n    }\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case \"high\":\n                return \"border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-900/20\";\n            case \"medium\":\n                return \"border-yellow-200 dark:border-yellow-800 bg-yellow-50 dark:bg-yellow-900/20\";\n            case \"low\":\n                return \"border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/20\";\n            default:\n                return \"border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800\";\n        }\n    };\n    const getPriorityLabel = (priority)=>{\n        const labels = {\n            high: {\n                ar: \"عالية\",\n                en: \"High\"\n            },\n            medium: {\n                ar: \"متوسطة\",\n                en: \"Medium\"\n            },\n            low: {\n                ar: \"منخفضة\",\n                en: \"Low\"\n            }\n        };\n        return labels[priority]?.[isArabic ? \"ar\" : \"en\"] || priority;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gradient-to-br from-purple-50 to-pink-100 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl p-6 border border-purple-200 dark:border-purple-800\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `text-center mb-6 ${isArabic ? \"text-right\" : \"text-left\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2\",\n                        children: isArabic ? \"\\uD83C\\uDFAF توصيات ذكية لمشروعك\" : \"\\uD83C\\uDFAF Smart Recommendations for Your Project\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartRecommendations.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                        children: isArabic ? \"اقتراحات مخصصة بناءً على خيارات مشروعك\" : \"Personalized suggestions based on your project options\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartRecommendations.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartRecommendations.tsx\",\n                lineNumber: 230,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: recommendations.map((rec)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `p-4 rounded-lg border ${getPriorityColor(rec.priority)} ${isArabic ? \"text-right\" : \"text-left\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start justify-between mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex items-center gap-2 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg\",\n                                                children: rec.icon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartRecommendations.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-gray-900 dark:text-gray-100\",\n                                                children: isArabic ? rec.titleAr : rec.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartRecommendations.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartRecommendations.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: `text-xs px-2 py-1 rounded-full ${rec.priority === \"high\" ? \"bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300\" : rec.priority === \"medium\" ? \"bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300\" : \"bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300\"}`,\n                                                children: getPriorityLabel(rec.priority)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartRecommendations.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                children: isArabic ? rec.categoryAr : rec.category\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartRecommendations.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartRecommendations.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartRecommendations.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                children: isArabic ? rec.descriptionAr : rec.description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartRecommendations.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, rec.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartRecommendations.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartRecommendations.tsx\",\n                lineNumber: 242,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartRecommendations.tsx\",\n        lineNumber: 229,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SmartRecommendations.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/StoreHydration.tsx":
/*!*******************************************!*\
  !*** ./src/components/StoreHydration.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StoreHydration)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction StoreHydration() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // Manually hydrate the store to prevent hydration mismatches\n        const store = _store_contextStore__WEBPACK_IMPORTED_MODULE_1__.useContextStore.getState();\n        if (false) {}\n    }, []);\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9TdG9yZUh5ZHJhdGlvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs2REFFa0M7QUFDcUI7QUFFeEMsU0FBU0U7SUFDdEJGLGdEQUFTQSxDQUFDO1FBQ1IsNkRBQTZEO1FBQzdELE1BQU1HLFFBQVFGLGdFQUFlQSxDQUFDRyxRQUFRO1FBQ3RDLElBQUksS0FBa0IsRUFBYSxFQUVsQztJQUNILEdBQUcsRUFBRTtJQUVMLE9BQU87QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL2NyYWZ0ZXJ5Ly4vc3JjL2NvbXBvbmVudHMvU3RvcmVIeWRyYXRpb24udHN4P2EyOTciXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VDb250ZXh0U3RvcmUgfSBmcm9tICdAL3N0b3JlL2NvbnRleHRTdG9yZSc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFN0b3JlSHlkcmF0aW9uKCkge1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIE1hbnVhbGx5IGh5ZHJhdGUgdGhlIHN0b3JlIHRvIHByZXZlbnQgaHlkcmF0aW9uIG1pc21hdGNoZXNcbiAgICBjb25zdCBzdG9yZSA9IHVzZUNvbnRleHRTdG9yZS5nZXRTdGF0ZSgpO1xuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgdXNlQ29udGV4dFN0b3JlLnBlcnNpc3QucmVoeWRyYXRlKCk7XG4gICAgfVxuICB9LCBbXSk7XG5cbiAgcmV0dXJuIG51bGw7XG59XG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlQ29udGV4dFN0b3JlIiwiU3RvcmVIeWRyYXRpb24iLCJzdG9yZSIsImdldFN0YXRlIiwicGVyc2lzdCIsInJlaHlkcmF0ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/StoreHydration.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ThemeProvider.tsx":
/*!******************************************!*\
  !*** ./src/components/ThemeProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme auto */ \n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ThemeProvider({ children }) {\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"light\");\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize theme from localStorage only, ignore system preference after first load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedTheme = localStorage.getItem(\"contextkit-theme\");\n        if (savedTheme && (savedTheme === \"light\" || savedTheme === \"dark\")) {\n            // إذا كان هناك theme محفوظ، استخدمه\n            setTheme(savedTheme);\n        } else {\n            // فقط في المرة الأولى، استخدم system preference\n            const systemTheme = window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\";\n            setTheme(systemTheme);\n            localStorage.setItem(\"contextkit-theme\", systemTheme);\n        }\n        setMounted(true);\n    }, []);\n    // Apply theme to document immediately\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (mounted) {\n            const root = document.documentElement;\n            // إزالة جميع classes المتعلقة بالـ theme أولاً\n            root.classList.remove(\"light\", \"dark\");\n            // إضافة الـ class الصحيح\n            root.classList.add(theme);\n            // حفظ في localStorage مع مفتاح مخصص\n            localStorage.setItem(\"contextkit-theme\", theme);\n            // تطبيق فوري على body أيضاً\n            document.body.setAttribute(\"data-theme\", theme);\n        }\n    }, [\n        theme,\n        mounted\n    ]);\n    const toggleTheme = ()=>{\n        const newTheme = theme === \"light\" ? \"dark\" : \"light\";\n        setTheme(newTheme);\n        // حفظ فوري في localStorage\n        localStorage.setItem(\"contextkit-theme\", newTheme);\n    };\n    // Prevent hydration mismatch\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: {\n            theme,\n            toggleTheme\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            suppressHydrationWarning: true,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeProvider.tsx\",\n            lineNumber: 69,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeProvider.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        // Return default values instead of throwing error during SSR\n        return {\n            theme: \"light\",\n            toggleTheme: ()=>{}\n        };\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ThemeProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ThemeToggle.tsx":
/*!****************************************!*\
  !*** ./src/components/ThemeToggle.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ThemeToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _ThemeProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ThemeProvider */ \"(ssr)/./src/components/ThemeProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ThemeToggle() {\n    const { theme, toggleTheme } = (0,_ThemeProvider__WEBPACK_IMPORTED_MODULE_1__.useTheme)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: toggleTheme,\n        className: \"relative w-12 h-12 rounded-xl bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-900 hover:from-blue-100 hover:to-indigo-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden\",\n        \"aria-label\": `Switch to ${theme === \"light\" ? \"dark\" : \"light\"} mode`,\n        title: `Switch to ${theme === \"light\" ? \"dark\" : \"light\"} mode`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: `w-6 h-6 text-amber-500 transition-all duration-500 ease-in-out ${theme === \"light\" ? \"opacity-100 rotate-0 scale-100\" : \"opacity-0 rotate-180 scale-0\"}`,\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: `w-6 h-6 text-blue-400 absolute transition-all duration-500 ease-in-out ${theme === \"dark\" ? \"opacity-100 rotate-0 scale-100\" : \"opacity-0 -rotate-180 scale-0\"}`,\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ThemeToggle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/projectOptions.ts":
/*!***********************************!*\
  !*** ./src/lib/projectOptions.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BUDGET_RANGES: () => (/* binding */ BUDGET_RANGES),\n/* harmony export */   COMPLEXITY_LEVELS: () => (/* binding */ COMPLEXITY_LEVELS),\n/* harmony export */   DEPLOYMENT_TYPES: () => (/* binding */ DEPLOYMENT_TYPES),\n/* harmony export */   PROGRAMMING_LANGUAGES: () => (/* binding */ PROGRAMMING_LANGUAGES),\n/* harmony export */   PROJECT_TYPES: () => (/* binding */ PROJECT_TYPES),\n/* harmony export */   TARGET_PLATFORMS: () => (/* binding */ TARGET_PLATFORMS),\n/* harmony export */   TEAM_SIZES: () => (/* binding */ TEAM_SIZES)\n/* harmony export */ });\n// خيارات التخصيص المتقدمة للمشاريع\n// أنواع المشاريع\nconst PROJECT_TYPES = [\n    {\n        id: \"web-app\",\n        label: \"Web Application\",\n        labelAr: \"تطبيق ويب\",\n        description: \"Browser-based application accessible via web browsers\",\n        descriptionAr: \"تطبيق يعمل في المتصفح ويمكن الوصول إليه عبر متصفحات الويب\",\n        icon: \"\\uD83C\\uDF10\"\n    },\n    {\n        id: \"mobile-app\",\n        label: \"Mobile Application\",\n        labelAr: \"تطبيق جوال\",\n        description: \"Native or cross-platform mobile application\",\n        descriptionAr: \"تطبيق جوال أصلي أو متعدد المنصات\",\n        icon: \"\\uD83D\\uDCF1\"\n    },\n    {\n        id: \"desktop-app\",\n        label: \"Desktop Application\",\n        labelAr: \"تطبيق سطح المكتب\",\n        description: \"Native desktop application for Windows, macOS, or Linux\",\n        descriptionAr: \"تطبيق سطح مكتب أصلي لـ Windows أو macOS أو Linux\",\n        icon: \"\\uD83D\\uDCBB\"\n    },\n    {\n        id: \"api-service\",\n        label: \"API/Microservice\",\n        labelAr: \"خدمة API/مايكروسيرفس\",\n        description: \"Backend API or microservice architecture\",\n        descriptionAr: \"خدمة API خلفية أو هندسة مايكروسيرفس\",\n        icon: \"\\uD83D\\uDD0C\"\n    },\n    {\n        id: \"ai-model\",\n        label: \"AI/ML Model\",\n        labelAr: \"نموذج ذكاء اصطناعي\",\n        description: \"Machine learning model or AI system\",\n        descriptionAr: \"نموذج تعلم آلي أو نظام ذكاء اصطناعي\",\n        icon: \"\\uD83E\\uDD16\"\n    },\n    {\n        id: \"chatbot\",\n        label: \"Chatbot/Virtual Assistant\",\n        labelAr: \"شات بوت/مساعد افتراضي\",\n        description: \"Conversational AI or chatbot system\",\n        descriptionAr: \"نظام ذكاء اصطناعي محادثة أو شات بوت\",\n        icon: \"\\uD83D\\uDCAC\"\n    },\n    {\n        id: \"data-analytics\",\n        label: \"Data Analytics Platform\",\n        labelAr: \"منصة تحليل البيانات\",\n        description: \"Data processing and analytics solution\",\n        descriptionAr: \"حل معالجة وتحليل البيانات\",\n        icon: \"\\uD83D\\uDCCA\"\n    },\n    {\n        id: \"iot-system\",\n        label: \"IoT System\",\n        labelAr: \"نظام إنترنت الأشياء\",\n        description: \"Internet of Things connected system\",\n        descriptionAr: \"نظام متصل بإنترنت الأشياء\",\n        icon: \"\\uD83C\\uDF10\"\n    }\n];\n// المنصات المستهدفة\nconst TARGET_PLATFORMS = [\n    {\n        id: \"web-browsers\",\n        label: \"Web Browsers\",\n        labelAr: \"متصفحات الويب\",\n        description: \"Chrome, Firefox, Safari, Edge\",\n        descriptionAr: \"كروم، فايرفوكس، سفاري، إيدج\",\n        icon: \"\\uD83C\\uDF10\"\n    },\n    {\n        id: \"android\",\n        label: \"Android\",\n        labelAr: \"أندرويد\",\n        description: \"Android mobile devices\",\n        descriptionAr: \"أجهزة أندرويد المحمولة\",\n        icon: \"\\uD83E\\uDD16\"\n    },\n    {\n        id: \"ios\",\n        label: \"iOS\",\n        labelAr: \"iOS\",\n        description: \"iPhone and iPad devices\",\n        descriptionAr: \"أجهزة آيفون وآيباد\",\n        icon: \"\\uD83C\\uDF4E\"\n    },\n    {\n        id: \"windows\",\n        label: \"Windows\",\n        labelAr: \"ويندوز\",\n        description: \"Windows desktop and server\",\n        descriptionAr: \"سطح مكتب وخادم ويندوز\",\n        icon: \"\\uD83E\\uDE9F\"\n    },\n    {\n        id: \"macos\",\n        label: \"macOS\",\n        labelAr: \"macOS\",\n        description: \"Apple macOS desktop\",\n        descriptionAr: \"سطح مكتب آبل macOS\",\n        icon: \"\\uD83C\\uDF4E\"\n    },\n    {\n        id: \"linux\",\n        label: \"Linux\",\n        labelAr: \"لينكس\",\n        description: \"Linux distributions\",\n        descriptionAr: \"توزيعات لينكس\",\n        icon: \"\\uD83D\\uDC27\"\n    },\n    {\n        id: \"cloud\",\n        label: \"Cloud Platforms\",\n        labelAr: \"المنصات السحابية\",\n        description: \"AWS, Azure, Google Cloud\",\n        descriptionAr: \"AWS، Azure، Google Cloud\",\n        icon: \"☁️\"\n    },\n    {\n        id: \"embedded\",\n        label: \"Embedded Systems\",\n        labelAr: \"الأنظمة المدمجة\",\n        description: \"IoT devices, microcontrollers\",\n        descriptionAr: \"أجهزة إنترنت الأشياء، المتحكمات الدقيقة\",\n        icon: \"\\uD83D\\uDD27\"\n    }\n];\n// لغات البرمجة الأساسية\nconst PROGRAMMING_LANGUAGES = [\n    {\n        id: \"javascript\",\n        label: \"JavaScript/TypeScript\",\n        labelAr: \"جافا سكريبت/تايب سكريبت\",\n        description: \"Modern web development\",\n        descriptionAr: \"تطوير الويب الحديث\",\n        icon: \"\\uD83D\\uDFE8\"\n    },\n    {\n        id: \"python\",\n        label: \"Python\",\n        labelAr: \"بايثون\",\n        description: \"AI/ML, backend, data science\",\n        descriptionAr: \"ذكاء اصطناعي، خلفية، علوم البيانات\",\n        icon: \"\\uD83D\\uDC0D\"\n    },\n    {\n        id: \"java\",\n        label: \"Java\",\n        labelAr: \"جافا\",\n        description: \"Enterprise applications, Android\",\n        descriptionAr: \"تطبيقات المؤسسات، أندرويد\",\n        icon: \"☕\"\n    },\n    {\n        id: \"csharp\",\n        label: \"C#\",\n        labelAr: \"سي شارب\",\n        description: \".NET ecosystem, Windows apps\",\n        descriptionAr: \"نظام .NET، تطبيقات ويندوز\",\n        icon: \"\\uD83D\\uDD37\"\n    },\n    {\n        id: \"swift\",\n        label: \"Swift\",\n        labelAr: \"سويفت\",\n        description: \"iOS and macOS development\",\n        descriptionAr: \"تطوير iOS و macOS\",\n        icon: \"\\uD83E\\uDD89\"\n    },\n    {\n        id: \"kotlin\",\n        label: \"Kotlin\",\n        labelAr: \"كوتلن\",\n        description: \"Android development, JVM\",\n        descriptionAr: \"تطوير أندرويد، JVM\",\n        icon: \"\\uD83D\\uDFE3\"\n    },\n    {\n        id: \"rust\",\n        label: \"Rust\",\n        labelAr: \"رست\",\n        description: \"System programming, performance\",\n        descriptionAr: \"برمجة الأنظمة، الأداء\",\n        icon: \"\\uD83E\\uDD80\"\n    },\n    {\n        id: \"go\",\n        label: \"Go\",\n        labelAr: \"جو\",\n        description: \"Backend services, microservices\",\n        descriptionAr: \"خدمات خلفية، مايكروسيرفس\",\n        icon: \"\\uD83D\\uDC39\"\n    },\n    {\n        id: \"cpp\",\n        label: \"C++\",\n        labelAr: \"سي++\",\n        description: \"High-performance applications\",\n        descriptionAr: \"تطبيقات عالية الأداء\",\n        icon: \"⚡\"\n    },\n    {\n        id: \"php\",\n        label: \"PHP\",\n        labelAr: \"PHP\",\n        description: \"Web development, server-side\",\n        descriptionAr: \"تطوير الويب، جانب الخادم\",\n        icon: \"\\uD83D\\uDC18\"\n    },\n    {\n        id: \"ruby\",\n        label: \"Ruby\",\n        labelAr: \"روبي\",\n        description: \"Web applications, rapid development\",\n        descriptionAr: \"تطبيقات الويب، التطوير السريع\",\n        icon: \"\\uD83D\\uDC8E\"\n    },\n    {\n        id: \"dart\",\n        label: \"Dart/Flutter\",\n        labelAr: \"دارت/فلاتر\",\n        description: \"Cross-platform mobile apps\",\n        descriptionAr: \"تطبيقات جوال متعددة المنصات\",\n        icon: \"\\uD83C\\uDFAF\"\n    }\n];\n// مستويات التعقيد\nconst COMPLEXITY_LEVELS = [\n    {\n        id: \"simple\",\n        label: \"Simple\",\n        labelAr: \"بسيط\",\n        description: \"Basic functionality, minimal features\",\n        descriptionAr: \"وظائف أساسية، ميزات قليلة\",\n        icon: \"\\uD83D\\uDFE2\"\n    },\n    {\n        id: \"moderate\",\n        label: \"Moderate\",\n        labelAr: \"متوسط\",\n        description: \"Standard features, some integrations\",\n        descriptionAr: \"ميزات قياسية، بعض التكاملات\",\n        icon: \"\\uD83D\\uDFE1\"\n    },\n    {\n        id: \"complex\",\n        label: \"Complex\",\n        labelAr: \"معقد\",\n        description: \"Advanced features, multiple integrations\",\n        descriptionAr: \"ميزات متقدمة، تكاملات متعددة\",\n        icon: \"\\uD83D\\uDFE0\"\n    },\n    {\n        id: \"enterprise\",\n        label: \"Enterprise\",\n        labelAr: \"مؤسسي\",\n        description: \"Large-scale, high availability, security\",\n        descriptionAr: \"واسع النطاق، توفر عالي، أمان\",\n        icon: \"\\uD83D\\uDD34\"\n    }\n];\n// نطاقات الميزانية\nconst BUDGET_RANGES = [\n    {\n        id: \"startup\",\n        label: \"Startup Budget\",\n        labelAr: \"ميزانية ناشئة\",\n        description: \"Under $10K\",\n        descriptionAr: \"أقل من 10 آلاف دولار\",\n        icon: \"\\uD83D\\uDCB0\"\n    },\n    {\n        id: \"small\",\n        label: \"Small Project\",\n        labelAr: \"مشروع صغير\",\n        description: \"$10K - $50K\",\n        descriptionAr: \"10-50 ألف دولار\",\n        icon: \"\\uD83D\\uDCB5\"\n    },\n    {\n        id: \"medium\",\n        label: \"Medium Project\",\n        labelAr: \"مشروع متوسط\",\n        description: \"$50K - $200K\",\n        descriptionAr: \"50-200 ألف دولار\",\n        icon: \"\\uD83D\\uDCB8\"\n    },\n    {\n        id: \"large\",\n        label: \"Large Project\",\n        labelAr: \"مشروع كبير\",\n        description: \"$200K - $1M\",\n        descriptionAr: \"200 ألف - مليون دولار\",\n        icon: \"\\uD83D\\uDC8E\"\n    },\n    {\n        id: \"enterprise\",\n        label: \"Enterprise\",\n        labelAr: \"مؤسسي\",\n        description: \"$1M+\",\n        descriptionAr: \"أكثر من مليون دولار\",\n        icon: \"\\uD83C\\uDFE6\"\n    }\n];\n// أحجام الفريق\nconst TEAM_SIZES = [\n    {\n        id: \"solo\",\n        label: \"Solo Developer\",\n        labelAr: \"مطور منفرد\",\n        description: \"1 person\",\n        descriptionAr: \"شخص واحد\",\n        icon: \"\\uD83D\\uDC64\"\n    },\n    {\n        id: \"small\",\n        label: \"Small Team\",\n        labelAr: \"فريق صغير\",\n        description: \"2-5 people\",\n        descriptionAr: \"2-5 أشخاص\",\n        icon: \"\\uD83D\\uDC65\"\n    },\n    {\n        id: \"medium\",\n        label: \"Medium Team\",\n        labelAr: \"فريق متوسط\",\n        description: \"6-15 people\",\n        descriptionAr: \"6-15 شخص\",\n        icon: \"\\uD83D\\uDC68‍\\uD83D\\uDC69‍\\uD83D\\uDC67‍\\uD83D\\uDC66\"\n    },\n    {\n        id: \"large\",\n        label: \"Large Team\",\n        labelAr: \"فريق كبير\",\n        description: \"16-50 people\",\n        descriptionAr: \"16-50 شخص\",\n        icon: \"\\uD83C\\uDFE2\"\n    },\n    {\n        id: \"enterprise\",\n        label: \"Enterprise Team\",\n        labelAr: \"فريق مؤسسي\",\n        description: \"50+ people\",\n        descriptionAr: \"أكثر من 50 شخص\",\n        icon: \"\\uD83C\\uDFED\"\n    }\n];\n// أنواع النشر\nconst DEPLOYMENT_TYPES = [\n    {\n        id: \"cloud\",\n        label: \"Cloud Deployment\",\n        labelAr: \"نشر سحابي\",\n        description: \"AWS, Azure, Google Cloud, Vercel\",\n        descriptionAr: \"AWS، Azure، Google Cloud، Vercel\",\n        icon: \"☁️\"\n    },\n    {\n        id: \"on-premise\",\n        label: \"On-Premise\",\n        labelAr: \"محلي\",\n        description: \"Self-hosted infrastructure\",\n        descriptionAr: \"بنية تحتية ذاتية الاستضافة\",\n        icon: \"\\uD83C\\uDFE2\"\n    },\n    {\n        id: \"hybrid\",\n        label: \"Hybrid\",\n        labelAr: \"هجين\",\n        description: \"Mix of cloud and on-premise\",\n        descriptionAr: \"مزيج من السحابي والمحلي\",\n        icon: \"\\uD83D\\uDD04\"\n    },\n    {\n        id: \"edge\",\n        label: \"Edge Computing\",\n        labelAr: \"حوسبة الحافة\",\n        description: \"Distributed edge deployment\",\n        descriptionAr: \"نشر موزع على الحافة\",\n        icon: \"\\uD83C\\uDF10\"\n    },\n    {\n        id: \"mobile-stores\",\n        label: \"App Stores\",\n        labelAr: \"متاجر التطبيقات\",\n        description: \"Google Play, App Store\",\n        descriptionAr: \"جوجل بلاي، آب ستور\",\n        icon: \"\\uD83D\\uDCF1\"\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/projectOptions.ts\n");

/***/ }),

/***/ "(ssr)/./src/store/contextStore.ts":
/*!***********************************!*\
  !*** ./src/store/contextStore.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useContextStore: () => (/* binding */ useContextStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n\n\n// القيم الافتراضية\nconst initialState = {\n    projectDefinition: {\n        name: \"\",\n        purpose: \"\",\n        targetUsers: \"\",\n        goals: \"\",\n        scope: \"\",\n        timeline: \"\",\n        projectType: \"\",\n        targetPlatforms: [],\n        primaryLanguages: [],\n        complexity: \"\",\n        budget: \"\",\n        teamSize: \"\",\n        deploymentType: \"\"\n    },\n    contextMap: {\n        timeContext: \"\",\n        language: \"\",\n        location: \"\",\n        culturalContext: \"\",\n        behavioralAspects: \"\",\n        environmentalFactors: \"\"\n    },\n    emotionalTone: {\n        personality: \"\",\n        communicationStyle: \"\",\n        userExperience: \"\",\n        brandVoice: \"\",\n        emotionalIntelligence: \"\",\n        interactionFlow: \"\"\n    },\n    technicalLayer: {\n        programmingLanguages: \"\",\n        frameworks: \"\",\n        llmModels: \"\",\n        databases: \"\",\n        apis: \"\",\n        infrastructure: \"\",\n        architecturePattern: \"\",\n        scalingStrategy: \"\",\n        securityRequirements: \"\",\n        performanceTargets: \"\",\n        integrationNeeds: \"\",\n        monitoringTools: \"\"\n    },\n    legalRisk: {\n        privacyConcerns: \"\",\n        dataProtection: \"\",\n        compliance: \"\",\n        risks: \"\",\n        mitigation: \"\",\n        ethicalConsiderations: \"\"\n    },\n    currentLanguage: \"ar\",\n    outputFormat: \"markdown\",\n    showAdvancedOptions: true,\n    apiSettings: {\n        providers: [],\n        globalSettings: {\n            temperature: 0.7,\n            topP: 0.9,\n            maxTokens: 1000,\n            timeout: 30000\n        },\n        // Legacy support\n        openaiApiKey: \"\",\n        openrouterApiKey: \"\",\n        customModels: []\n    }\n};\n// إنشاء المتجر مع التخزين المستمر\nconst useContextStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        ...initialState,\n        updateProjectDefinition: (data)=>set((state)=>({\n                    projectDefinition: {\n                        ...state.projectDefinition,\n                        ...data\n                    }\n                })),\n        updateContextMap: (data)=>set((state)=>({\n                    contextMap: {\n                        ...state.contextMap,\n                        ...data\n                    }\n                })),\n        updateEmotionalTone: (data)=>set((state)=>({\n                    emotionalTone: {\n                        ...state.emotionalTone,\n                        ...data\n                    }\n                })),\n        updateTechnicalLayer: (data)=>set((state)=>({\n                    technicalLayer: {\n                        ...state.technicalLayer,\n                        ...data\n                    }\n                })),\n        updateLegalRisk: (data)=>set((state)=>({\n                    legalRisk: {\n                        ...state.legalRisk,\n                        ...data\n                    }\n                })),\n        setLanguage: (lang)=>set({\n                currentLanguage: lang\n            }),\n        setOutputFormat: (format)=>set({\n                outputFormat: format\n            }),\n        setShowAdvancedOptions: (show)=>set({\n                showAdvancedOptions: show\n            }),\n        setApiSettings: (settings)=>set({\n                apiSettings: {\n                    ...settings,\n                    providers: settings.providers || []\n                }\n            }),\n        // LLM Provider management functions\n        addProvider: (provider)=>set((state)=>{\n                const existingProviders = state.apiSettings.providers || [];\n                // تحقق من عدم وجود مقدم خدمة بنفس المعرف\n                if (existingProviders.find((p)=>p.id === provider.id)) {\n                    console.warn(`Provider with id ${provider.id} already exists`);\n                    return state; // لا تضيف مقدم خدمة مكرر\n                }\n                // إضافة القيم الافتراضية للميزات المتقدمة\n                const enhancedProvider = {\n                    ...provider,\n                    priority: provider.priority || 5,\n                    isBackup: provider.isBackup || false,\n                    maxRequestsPerMinute: provider.maxRequestsPerMinute || 60,\n                    timeout: provider.timeout || 30,\n                    retryAttempts: provider.retryAttempts || 3,\n                    stats: provider.stats || {\n                        totalRequests: 0,\n                        successfulRequests: 0,\n                        failedRequests: 0,\n                        averageResponseTime: 0,\n                        totalTokensUsed: 0,\n                        totalCost: 0\n                    }\n                };\n                return {\n                    apiSettings: {\n                        ...state.apiSettings,\n                        providers: [\n                            ...existingProviders,\n                            enhancedProvider\n                        ]\n                    }\n                };\n            }),\n        updateProvider: (providerId, updates)=>set((state)=>({\n                    apiSettings: {\n                        ...state.apiSettings,\n                        providers: (state.apiSettings.providers || []).map((p)=>p.id === providerId ? {\n                                ...p,\n                                ...updates\n                            } : p)\n                    }\n                })),\n        removeProvider: (providerId)=>set((state)=>({\n                    apiSettings: {\n                        ...state.apiSettings,\n                        providers: (state.apiSettings.providers || []).filter((p)=>p.id !== providerId),\n                        defaultProvider: state.apiSettings.defaultProvider === providerId ? undefined : state.apiSettings.defaultProvider\n                    }\n                })),\n        validateProvider: async (providerId)=>{\n            const state = get();\n            const provider = (state.apiSettings.providers || []).find((p)=>p.id === providerId);\n            if (!provider) return false;\n            try {\n                // Update status to pending\n                state.updateProvider(providerId, {\n                    validationStatus: \"pending\",\n                    errorMessage: undefined\n                });\n                // Call validation API\n                const response = await fetch(\"/api/llm/validate\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        providerId: provider.id,\n                        apiKey: provider.apiKey,\n                        baseUrl: provider.baseUrl\n                    })\n                });\n                const result = await response.json();\n                if (result.valid) {\n                    state.updateProvider(providerId, {\n                        validationStatus: \"valid\",\n                        lastValidated: new Date(),\n                        errorMessage: undefined,\n                        isEnabled: true // تفعيل المقدم تلقائياً عند نجاح التحقق\n                    });\n                    return true;\n                } else {\n                    state.updateProvider(providerId, {\n                        validationStatus: \"invalid\",\n                        errorMessage: result.error || \"Validation failed\"\n                    });\n                    return false;\n                }\n            } catch (error) {\n                state.updateProvider(providerId, {\n                    validationStatus: \"error\",\n                    errorMessage: error instanceof Error ? error.message : \"Unknown error\"\n                });\n                return false;\n            }\n        },\n        getProvider: (providerId)=>{\n            const state = get();\n            return (state.apiSettings.providers || []).find((p)=>p.id === providerId);\n        },\n        getActiveProviders: ()=>{\n            const state = get();\n            return (state.apiSettings.providers || []).filter((p)=>p.isEnabled);\n        },\n        setDefaultProvider: (providerId)=>set((state)=>({\n                    apiSettings: {\n                        ...state.apiSettings,\n                        defaultProvider: providerId\n                    }\n                })),\n        // ميزات متقدمة للمزودين\n        getProvidersByPriority: ()=>{\n            const state = get();\n            return (state.apiSettings.providers || []).filter((p)=>p.isEnabled).sort((a, b)=>(b.priority || 5) - (a.priority || 5));\n        },\n        getBackupProviders: ()=>{\n            const state = get();\n            return (state.apiSettings.providers || []).filter((p)=>p.isEnabled && p.isBackup);\n        },\n        updateProviderStats: (id, stats)=>set((state)=>{\n                const providers = state.apiSettings.providers || [];\n                const providerIndex = providers.findIndex((p)=>p.id === id);\n                if (providerIndex !== -1) {\n                    const updatedProviders = [\n                        ...providers\n                    ];\n                    updatedProviders[providerIndex] = {\n                        ...updatedProviders[providerIndex],\n                        stats: {\n                            ...updatedProviders[providerIndex].stats,\n                            ...stats,\n                            lastUsed: new Date()\n                        }\n                    };\n                    return {\n                        apiSettings: {\n                            ...state.apiSettings,\n                            providers: updatedProviders\n                        }\n                    };\n                }\n                return state;\n            }),\n        getBestProvider: (criteria = \"reliability\")=>{\n            const state = get();\n            const activeProviders = (state.apiSettings.providers || []).filter((p)=>p.isEnabled && !p.isBackup);\n            if (activeProviders.length === 0) return undefined;\n            switch(criteria){\n                case \"speed\":\n                    return activeProviders.reduce((best, current)=>{\n                        const bestSpeed = best.stats?.averageResponseTime || Infinity;\n                        const currentSpeed = current.stats?.averageResponseTime || Infinity;\n                        return currentSpeed < bestSpeed ? current : best;\n                    });\n                case \"cost\":\n                    return activeProviders.reduce((best, current)=>{\n                        const bestCost = best.costPerToken || Infinity;\n                        const currentCost = current.costPerToken || Infinity;\n                        return currentCost < bestCost ? current : best;\n                    });\n                case \"reliability\":\n                default:\n                    return activeProviders.reduce((best, current)=>{\n                        const bestReliability = best.stats ? best.stats.successfulRequests / (best.stats.totalRequests || 1) : 0;\n                        const currentReliability = current.stats ? current.stats.successfulRequests / (current.stats.totalRequests || 1) : 0;\n                        return currentReliability > bestReliability ? current : best;\n                    });\n            }\n        },\n        resetAll: ()=>set(initialState),\n        // مسح جميع الإجابات فقط (الاحتفاظ بالإعدادات)\n        clearAllAnswers: ()=>set((state)=>({\n                    ...state,\n                    projectDefinition: {\n                        name: \"\",\n                        purpose: \"\",\n                        targetUsers: \"\",\n                        goals: \"\",\n                        scope: \"\",\n                        timeline: \"\"\n                    },\n                    contextMap: {\n                        timeContext: \"\",\n                        language: \"\",\n                        location: \"\",\n                        culturalContext: \"\",\n                        behavioralAspects: \"\",\n                        environmentalFactors: \"\"\n                    },\n                    emotionalTone: {\n                        personality: \"\",\n                        communicationStyle: \"\",\n                        userExperience: \"\",\n                        brandVoice: \"\",\n                        emotionalIntelligence: \"\",\n                        interactionFlow: \"\"\n                    },\n                    technicalLayer: {\n                        programmingLanguages: \"\",\n                        frameworks: \"\",\n                        llmModels: \"\",\n                        databases: \"\",\n                        apis: \"\",\n                        infrastructure: \"\"\n                    },\n                    legalRisk: {\n                        privacyConcerns: \"\",\n                        dataProtection: \"\",\n                        compliance: \"\",\n                        risks: \"\",\n                        mitigation: \"\",\n                        ethicalConsiderations: \"\"\n                    }\n                })),\n        getModuleData: (module)=>{\n            const state = get();\n            switch(module){\n                case \"project\":\n                    return state.projectDefinition;\n                case \"context\":\n                    return state.contextMap;\n                case \"emotional\":\n                    return state.emotionalTone;\n                case \"technical\":\n                    return state.technicalLayer;\n                case \"legal\":\n                    return state.legalRisk;\n                default:\n                    return {};\n            }\n        },\n        getAllData: ()=>{\n            const state = get();\n            return {\n                projectDefinition: state.projectDefinition,\n                contextMap: state.contextMap,\n                emotionalTone: state.emotionalTone,\n                technicalLayer: state.technicalLayer,\n                legalRisk: state.legalRisk\n            };\n        }\n    }), {\n    name: \"contextkit-storage\",\n    version: 1,\n    skipHydration: true\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/contextStore.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"22d6d015889c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY3JhZnRlcnkvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzdlMzUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIyMmQ2ZDAxNTg4OWNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/styles/glass-effects.css":
/*!**************************************!*\
  !*** ./src/styles/glass-effects.css ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5e0b1f04c4ea\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL2dsYXNzLWVmZmVjdHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY3JhZnRlcnkvLi9zcmMvc3R5bGVzL2dsYXNzLWVmZmVjdHMuY3NzPzVkYjMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI1ZTBiMWYwNGM0ZWFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/glass-effects.css\n");

/***/ }),

/***/ "(rsc)/./src/styles/text-improvements.css":
/*!******************************************!*\
  !*** ./src/styles/text-improvements.css ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"757aecea32c0\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL3RleHQtaW1wcm92ZW1lbnRzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsid2VicGFjazovL2NyYWZ0ZXJ5Ly4vc3JjL3N0eWxlcy90ZXh0LWltcHJvdmVtZW50cy5jc3M/MGJkMSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjc1N2FlY2VhMzJjMFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/text-improvements.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _styles_glass_effects_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../styles/glass-effects.css */ \"(rsc)/./src/styles/glass-effects.css\");\n/* harmony import */ var _styles_text_improvements_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../styles/text-improvements.css */ \"(rsc)/./src/styles/text-improvements.css\");\n/* harmony import */ var _components_ThemeProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ThemeProvider */ \"(rsc)/./src/components/ThemeProvider.tsx\");\n/* harmony import */ var _components_StoreHydration__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/StoreHydration */ \"(rsc)/./src/components/StoreHydration.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"Craftery - AI-powered Idea Builder\",\n    description: \"AI-powered platform for building and developing creative ideas\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"antialiased font-arabic\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeProvider__WEBPACK_IMPORTED_MODULE_4__.ThemeProvider, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_StoreHydration__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this),\n                        children\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\app\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\app\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/StoreHydration.tsx":
/*!*******************************************!*\
  !*** ./src/components/StoreHydration.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\StoreHydration.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\StoreHydration.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/ThemeProvider.tsx":
/*!******************************************!*\
  !*** ./src/components/ThemeProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0),
/* harmony export */   useTheme: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\ThemeProvider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\ThemeProvider.tsx#ThemeProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\ThemeProvider.tsx#useTheme`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react","vendor-chunks/zustand"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();