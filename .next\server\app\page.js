/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZwYWdlJnBhZ2U9JTJGcGFnZSZhcHBQYXRocz0lMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGcGFnZS50c3gmYXBwRGlyPUMlM0ElNUNVc2VycyU1Q2ZhaXNzJTVDRGVza3RvcCU1Q0NvbnRleHRLaXQlNUNzcmMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUMlM0ElNUNVc2VycyU1Q2ZhaXNzJTVDRGVza3RvcCU1Q0NvbnRleHRLaXQmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxhQUFhLHNCQUFzQjtBQUNpRTtBQUNyQztBQUMvRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUM7QUFDakMsdUJBQXVCLGdKQUE4RjtBQUNySDtBQUNBLFNBQVM7QUFDVCxPQUFPO0FBQ1A7QUFDQSx5QkFBeUIsb0pBQWdHO0FBQ3pILG9CQUFvQiwwTkFBZ0Y7QUFDcEc7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ3VCO0FBQzZEO0FBQ3BGLDZCQUE2QixtQkFBbUI7QUFDaEQ7QUFDTztBQUNBO0FBQ1A7QUFDQTtBQUNBO0FBQ3VEO0FBQ3ZEO0FBQ08sd0JBQXdCLDhHQUFrQjtBQUNqRDtBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLENBQUM7O0FBRUQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb250ZXh0a2l0Lz84M2U2Il0sInNvdXJjZXNDb250ZW50IjpbIlwiVFVSQk9QQUNLIHsgdHJhbnNpdGlvbjogbmV4dC1zc3IgfVwiO1xuaW1wb3J0IHsgQXBwUGFnZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvYXBwLXBhZ2UvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZFwiO1xuLy8gV2UgaW5qZWN0IHRoZSB0cmVlIGFuZCBwYWdlcyBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgdHJlZSA9IHtcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgJycsXG4gICAgICAgIHtcbiAgICAgICAgY2hpbGRyZW46IFsnX19QQUdFX18nLCB7fSwge1xuICAgICAgICAgIHBhZ2U6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGZhaXNzXFxcXERlc2t0b3BcXFxcQ29udGV4dEtpdFxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpLCBcIkM6XFxcXFVzZXJzXFxcXGZhaXNzXFxcXERlc2t0b3BcXFxcQ29udGV4dEtpdFxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCJdLFxuICAgICAgICAgIFxuICAgICAgICB9XVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAnbGF5b3V0JzogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcZmFpc3NcXFxcRGVza3RvcFxcXFxDb250ZXh0S2l0XFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKSwgXCJDOlxcXFxVc2Vyc1xcXFxmYWlzc1xcXFxEZXNrdG9wXFxcXENvbnRleHRLaXRcXFxcc3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCJdLFxuJ25vdC1mb3VuZCc6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIiksIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiXSxcbiAgICAgICAgXG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LmNoaWxkcmVuO1xuY29uc3QgcGFnZXMgPSBbXCJDOlxcXFxVc2Vyc1xcXFxmYWlzc1xcXFxEZXNrdG9wXFxcXENvbnRleHRLaXRcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiXTtcbmV4cG9ydCB7IHRyZWUsIHBhZ2VzIH07XG5leHBvcnQgeyBkZWZhdWx0IGFzIEdsb2JhbEVycm9yIH0gZnJvbSBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9lcnJvci1ib3VuZGFyeVwiO1xuY29uc3QgX19uZXh0X2FwcF9yZXF1aXJlX18gPSBfX3dlYnBhY2tfcmVxdWlyZV9fXG5jb25zdCBfX25leHRfYXBwX2xvYWRfY2h1bmtfXyA9ICgpID0+IFByb21pc2UucmVzb2x2ZSgpXG5leHBvcnQgY29uc3Qgb3JpZ2luYWxQYXRobmFtZSA9IFwiL3BhZ2VcIjtcbmV4cG9ydCBjb25zdCBfX25leHRfYXBwX18gPSB7XG4gICAgcmVxdWlyZTogX19uZXh0X2FwcF9yZXF1aXJlX18sXG4gICAgbG9hZENodW5rOiBfX25leHRfYXBwX2xvYWRfY2h1bmtfX1xufTtcbmV4cG9ydCAqIGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvZW50cnktYmFzZVwiO1xuLy8gQ3JlYXRlIGFuZCBleHBvcnQgdGhlIHJvdXRlIG1vZHVsZSB0aGF0IHdpbGwgYmUgY29uc3VtZWQuXG5leHBvcnQgY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUGFnZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUEFHRSxcbiAgICAgICAgcGFnZTogXCIvcGFnZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvXCIsXG4gICAgICAgIC8vIFRoZSBmb2xsb3dpbmcgYXJlbid0IHVzZWQgaW4gcHJvZHVjdGlvbi5cbiAgICAgICAgYnVuZGxlUGF0aDogXCJcIixcbiAgICAgICAgZmlsZW5hbWU6IFwiXCIsXG4gICAgICAgIGFwcFBhdGhzOiBbXVxuICAgIH0sXG4gICAgdXNlcmxhbmQ6IHtcbiAgICAgICAgbG9hZGVyVHJlZTogdHJlZVxuICAgIH1cbn0pO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcGFnZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CStoreHydration.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Cstyles%5C%5Cglass-effects.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Cstyles%5C%5Ctext-improvements.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CStoreHydration.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Cstyles%5C%5Cglass-effects.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Cstyles%5C%5Ctext-improvements.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/StoreHydration.tsx */ \"(ssr)/./src/components/StoreHydration.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ThemeProvider.tsx */ \"(ssr)/./src/components/ThemeProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2ZhaXNzJTVDJTVDRGVza3RvcCU1QyU1Q0NvbnRleHRLaXQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNmYWlzcyU1QyU1Q0Rlc2t0b3AlNUMlNUNDb250ZXh0S2l0JTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q1N0b3JlSHlkcmF0aW9uLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDZmFpc3MlNUMlNUNEZXNrdG9wJTVDJTVDQ29udGV4dEtpdCU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNUaGVtZVByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlRoZW1lUHJvdmlkZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDZmFpc3MlNUMlNUNEZXNrdG9wJTVDJTVDQ29udGV4dEtpdCU1QyU1Q3NyYyU1QyU1Q3N0eWxlcyU1QyU1Q2dsYXNzLWVmZmVjdHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2ZhaXNzJTVDJTVDRGVza3RvcCU1QyU1Q0NvbnRleHRLaXQlNUMlNUNzcmMlNUMlNUNzdHlsZXMlNUMlNUN0ZXh0LWltcHJvdmVtZW50cy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtMQUE0STtBQUM1STtBQUNBLGdMQUFpSiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbnRleHRraXQvPzJlZGQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcZmFpc3NcXFxcRGVza3RvcFxcXFxDb250ZXh0S2l0XFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXFN0b3JlSHlkcmF0aW9uLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiVGhlbWVQcm92aWRlclwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXGZhaXNzXFxcXERlc2t0b3BcXFxcQ29udGV4dEtpdFxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxUaGVtZVByb3ZpZGVyLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CStoreHydration.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Cstyles%5C%5Cglass-effects.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Cstyles%5C%5Ctext-improvements.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2ZhaXNzJTVDJTVDRGVza3RvcCU1QyU1Q0NvbnRleHRLaXQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQThGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29udGV4dGtpdC8/MzliZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGZhaXNzXFxcXERlc2t0b3BcXFxcQ29udGV4dEtpdFxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Header */ \"(ssr)/./src/components/Header.tsx\");\n/* harmony import */ var _components_ProjectSummaryCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ProjectSummaryCard */ \"(ssr)/./src/components/ProjectSummaryCard.tsx\");\n/* harmony import */ var _components_SmartRecommendations__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/SmartRecommendations */ \"(ssr)/./src/components/SmartRecommendations.tsx\");\n/* harmony import */ var _components_ProjectStats__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ProjectStats */ \"(ssr)/./src/components/ProjectStats.tsx\");\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n// import ProjectRoadmap from '@/components/ProjectRoadmap';\n\nfunction Home() {\n    const { currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_5__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 ${isArabic ? \"font-arabic\" : \"\"}`,\n        dir: isArabic ? \"rtl\" : \"ltr\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-16\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    title: isArabic ? \"Craftery\" : \"Craftery\",\n                    subtitle: isArabic ? \"منصة ذكية مدعومة بالذكاء الاصطناعي لبناء وتطوير الأفكار الإبداعية\" : \"AI-powered Idea Builder\",\n                    emoji: \"\\uD83E\\uDDE0\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProjectSummaryCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SmartRecommendations__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProjectStats__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl mb-4\",\n                                    children: \"\\uD83E\\uDDED\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-2 text-gray-900 dark:text-white\",\n                                    children: isArabic ? \"واجهة متعددة الصفحات\" : \"Multi-page Interface\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300\",\n                                    children: isArabic ? \"صفحات مخصصة لكل وحدة مع أسئلة ومخرجات مصممة خصيصاً.\" : \"Dedicated pages for each module with tailored prompts and outputs.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl mb-4\",\n                                    children: \"✍️\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-2 text-gray-900 dark:text-white\",\n                                    children: isArabic ? \"أسئلة موجهة\" : \"Guided Prompts\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300\",\n                                    children: isArabic ? \"أسئلة ذكية لتوجيه عملية تفكيرك عبر كل وحدة.\" : \"Smart questions to guide your thought process through each module.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl mb-4\",\n                                    children: \"\\uD83D\\uDCCB\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-2 text-gray-900 dark:text-white\",\n                                    children: isArabic ? \"نسخ المخرجات\" : \"Output Copying\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300\",\n                                    children: isArabic ? \"أزرار سهلة الاستخدام لنسخ المخرجات الكاملة أو الإجابات الفردية.\" : \"Easy-to-use buttons for copying entire outputs or individual responses.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl mb-4\",\n                                    children: \"\\uD83E\\uDDFE\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-2 text-gray-900 dark:text-white\",\n                                    children: isArabic ? \"مخرجات منظمة\" : \"Structured Outputs\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300\",\n                                    children: isArabic ? \"مخرجات بصيغة Markdown أو HTML أو JSON للتكامل السهل.\" : \"Outputs in Markdown, HTML, or JSON for easy integration.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl mb-4\",\n                                    children: \"\\uD83D\\uDCBE\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-2 text-gray-900 dark:text-white\",\n                                    children: isArabic ? \"حفظ تلقائي وجلسات\" : \"Auto-save & Sessions\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300\",\n                                    children: isArabic ? \"حفظ واسترجاع تلقائي لجلسات المستخدم للراحة.\" : \"Automatic saving and retrieval of user sessions for convenience.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl mb-4\",\n                                    children: \"\\uD83C\\uDF10\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold mb-2 text-gray-900 dark:text-white\",\n                                    children: isArabic ? \"دعم متعدد اللغات\" : \"Multilingual Support\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300\",\n                                    children: isArabic ? \"واجهات باللغتين الإنجليزية والعربية لخدمة جمهور أوسع.\" : \"English and Arabic interfaces to cater to a wider audience.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold text-center mb-12 text-gray-900 dark:text-white\",\n                            children: [\n                                \"\\uD83D\\uDEE0️ \",\n                                isArabic ? \"الوحدات المتاحة\" : \"Available Modules\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/project-definition\",\n                                    className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-blue-500 hover:shadow-xl transition-shadow block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-3 text-gray-900 dark:text-white\",\n                                            children: [\n                                                \"\\uD83C\\uDFAF \",\n                                                isArabic ? \"تعريف المشروع\" : \"Project Definition\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: isArabic ? \"حدد نطاق مشروعك والمستخدمين والأهداف بأسئلة موجهة.\" : \"Outline the scope, users, and goals of your project with guided prompts.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/interactive\",\n                                    className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-green-500 hover:shadow-xl transition-shadow block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-3 text-gray-900 dark:text-white\",\n                                            children: [\n                                                \"\\uD83E\\uDD16 \",\n                                                isArabic ? \"المساعد التفاعلي\" : \"Interactive Assistant\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: isArabic ? \"تجربة محادثة متقدمة مع الذكاء الاصطناعي مع ميزات تفاعلية حديثة.\" : \"Advanced conversational experience with AI featuring modern interactive capabilities.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/context-map\",\n                                    className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-green-500 hover:shadow-xl transition-shadow block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-3 text-gray-900 dark:text-white\",\n                                            children: [\n                                                \"\\uD83D\\uDDFA️ \",\n                                                isArabic ? \"خريطة السياق\" : \"Context Map\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: isArabic ? \"حدد الوقت واللغة والموقع والجوانب السلوكية لمشروعك.\" : \"Define time, language, location, and behavioral aspects of your project.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/emotional-tone\",\n                                    className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-purple-500 hover:shadow-xl transition-shadow block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-3 text-gray-900 dark:text-white\",\n                                            children: [\n                                                \"✨ \",\n                                                isArabic ? \"النبرة والتجربة\" : \"Emotional Tone & Experience\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: isArabic ? \"احدد النبرة العامة وتجربة المستخدم المرغوبة لمشروعك.\" : \"Capture the overall tone and user experience desired for your project.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/technical-layer\",\n                                    className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-orange-500 hover:shadow-xl transition-shadow block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-3 text-gray-900 dark:text-white\",\n                                            children: [\n                                                \"⚙️ \",\n                                                isArabic ? \"الطبقة التقنية\" : \"Technical Layer\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: isArabic ? \"حدد المتطلبات التقنية والأدوات والنماذج المستخدمة.\" : \"Define technical requirements, tools, and models to be used.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/legal-risk\",\n                                    className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-red-500 hover:shadow-xl transition-shadow block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-3 text-gray-900 dark:text-white\",\n                                            children: [\n                                                \"\\uD83D\\uDD12 \",\n                                                isArabic ? \"التحديات والخصوصية\" : \"Legal & Privacy\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: isArabic ? \"تناول التحديات ومخاوف الخصوصية والقضايا التنظيمية.\" : \"Address challenges, privacy concerns, and regulatory issues.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/final-preview\",\n                                    className: \"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border-l-4 border-indigo-500 hover:shadow-xl transition-shadow block\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-3 text-gray-900 dark:text-white\",\n                                            children: [\n                                                \"\\uD83D\\uDCCB \",\n                                                isArabic ? \"المعاينة النهائية\" : \"Final Preview\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: isArabic ? \"راجع واستخرج السياق الكامل لمشروعك.\" : \"Review and export your complete project context.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"/project-definition\",\n                            className: \"inline-block bg-blue-600 hover:bg-blue-700 text-white font-bold py-4 px-8 rounded-lg text-lg transition-colors duration-200\",\n                            children: isArabic ? \"ابدأ ببناء السياق\" : \"Start Building Context\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-4 text-gray-600 dark:text-gray-300\",\n                            children: isArabic ? \"هل أنت مستعد لإنشاء سياق منظم وقابل للتنفيذ لمشاريع الذكاء الاصطناعي؟\" : \"Ready to create structured, actionable context for your AI projects?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 221,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ClearContentButton.tsx":
/*!***********************************************!*\
  !*** ./src/components/ClearContentButton.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClearContentButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* harmony import */ var _barrel_optimize_names_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ClearContentButton() {\n    const { currentLanguage, clearAllAnswers } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_1__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    const translations = {\n        ar: {\n            clearAll: \"مسح جميع الإجابات\",\n            confirmMessage: \"هل أنت متأكد من مسح جميع الإجابات؟ لا يمكن التراجع عن هذا الإجراء.\",\n            success: \"تم مسح جميع الإجابات بنجاح\"\n        },\n        en: {\n            clearAll: \"Clear All Answers\",\n            confirmMessage: \"Are you sure you want to clear all answers? This action cannot be undone.\",\n            success: \"All answers cleared successfully\"\n        }\n    };\n    const t = translations[isArabic ? \"ar\" : \"en\"];\n    const handleClearAll = ()=>{\n        if (window.confirm(t.confirmMessage)) {\n            clearAllAnswers();\n            // Show success message\n            setTimeout(()=>{\n                alert(t.success);\n            }, 100);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: handleClearAll,\n        className: \"relative w-12 h-12 rounded-xl bg-gradient-to-br from-red-50 to-red-100 dark:from-gray-800 dark:to-gray-900 hover:from-red-100 hover:to-red-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden\",\n        title: t.clearAll,\n        \"aria-label\": t.clearAll,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-6 h-6 text-red-500 dark:text-red-400 group-hover:text-red-600 dark:group-hover:text-red-300 transition-colors duration-300\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ClearContentButton.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ClearContentButton.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ClearContentButton.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ClearContentButton.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ClearContentButton.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _ThemeToggle__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ThemeToggle */ \"(ssr)/./src/components/ThemeToggle.tsx\");\n/* harmony import */ var _LanguageToggle__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./LanguageToggle */ \"(ssr)/./src/components/LanguageToggle.tsx\");\n/* harmony import */ var _ClearContentButton__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ClearContentButton */ \"(ssr)/./src/components/ClearContentButton.tsx\");\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction Header({ title, subtitle, backLink, emoji }) {\n    const { currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_6__.useContextStore)();\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    const isArabic = mounted ? currentLanguage === \"ar\" : false;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-1/2 right-4 transform -translate-y-1/2 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white/90 dark:bg-gray-800/90 backdrop-blur-md rounded-2xl shadow-xl border border-gray-200/50 dark:border-gray-700/50 p-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                className: \"relative w-12 h-12 rounded-xl bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 hover:from-gray-100 hover:to-gray-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden\",\n                                title: isArabic ? \"الصفحة الرئيسية\" : \"Home\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-6 h-6 text-gray-600 dark:text-gray-300 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/settings\",\n                                className: \"relative w-12 h-12 rounded-xl bg-gradient-to-br from-purple-50 to-violet-100 dark:from-gray-800 dark:to-gray-900 hover:from-purple-100 hover:to-violet-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden\",\n                                title: isArabic ? \"إعدادات API\" : \"API Settings\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-6 h-6 text-purple-600 dark:text-purple-400 transition-all duration-300 group-hover:rotate-90\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClearContentButton__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LanguageToggle__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ThemeToggle__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-8 pt-4\",\n                children: [\n                    backLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        href: backLink.href,\n                        className: \"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mb-4 inline-block transition-colors\",\n                        children: backLink.label\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-2 text-center\",\n                        children: [\n                            emoji && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"mr-2\",\n                                children: emoji\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 21\n                            }, this),\n                            title\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this),\n                    subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto text-center\",\n                        children: subtitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\Header.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9IZWFkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFNEM7QUFDZjtBQUNpQjtBQUNOO0FBQ007QUFDUTtBQUNDO0FBWXhDLFNBQVNTLE9BQU8sRUFBRUMsS0FBSyxFQUFFQyxRQUFRLEVBQUVDLFFBQVEsRUFBRUMsS0FBSyxFQUFlO0lBQzlFLE1BQU0sRUFBRUMsZUFBZSxFQUFFLEdBQUdOLG9FQUFlQTtJQUMzQyxNQUFNLENBQUNPLFNBQVNDLFdBQVcsR0FBR2YsK0NBQVFBLENBQUM7SUFFdkNELGdEQUFTQSxDQUFDO1FBQ1JnQixXQUFXO0lBQ2IsR0FBRyxFQUFFO0lBRUwsTUFBTUMsV0FBV0YsVUFBVUQsb0JBQW9CLE9BQU87SUFFdEQscUJBQ0UsOERBQUNJO1FBQU9DLFdBQVU7OzBCQUVoQiw4REFBQ0M7Z0JBQUlELFdBQVU7MEJBQ2IsNEVBQUNDO29CQUFJRCxXQUFVOzhCQUNiLDRFQUFDQzt3QkFBSUQsV0FBVTs7MENBQ2IsOERBQUNqQixpREFBSUE7Z0NBQ0htQixNQUFLO2dDQUNMRixXQUFVO2dDQUNWVCxPQUFPTyxXQUFXLG9CQUFvQjs7a0RBRXRDLDhEQUFDRzt3Q0FBSUQsV0FBVTtrREFDYiw0RUFBQ2hCLHlGQUFJQTs0Q0FBQ2dCLFdBQVU7Ozs7Ozs7Ozs7O2tEQUlsQiw4REFBQ0M7d0NBQUlELFdBQVU7Ozs7Ozs7Ozs7OzswQ0FFakIsOERBQUNqQixpREFBSUE7Z0NBQ0htQixNQUFLO2dDQUNMRixXQUFVO2dDQUNWVCxPQUFPTyxXQUFXLGdCQUFnQjs7a0RBRWxDLDhEQUFDRzt3Q0FBSUQsV0FBVTtrREFDYiw0RUFBQ2YseUZBQVFBOzRDQUFDZSxXQUFVOzs7Ozs7Ozs7OztrREFJdEIsOERBQUNDO3dDQUFJRCxXQUFVOzs7Ozs7Ozs7Ozs7MENBRWpCLDhEQUFDWiwyREFBa0JBOzs7OzswQ0FDbkIsOERBQUNELHVEQUFjQTs7Ozs7MENBQ2YsOERBQUNELG9EQUFXQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU1sQiw4REFBQ2U7Z0JBQUlELFdBQVU7O29CQUNaUCwwQkFDQyw4REFBQ1YsaURBQUlBO3dCQUNIbUIsTUFBTVQsU0FBU1MsSUFBSTt3QkFDbkJGLFdBQVU7a0NBRVRQLFNBQVNVLEtBQUs7Ozs7OztrQ0FHbkIsOERBQUNDO3dCQUFHSixXQUFVOzs0QkFDWE4sdUJBQVMsOERBQUNXO2dDQUFLTCxXQUFVOzBDQUFRTjs7Ozs7OzRCQUNqQ0g7Ozs7Ozs7b0JBRUZDLDBCQUNDLDhEQUFDYzt3QkFBRU4sV0FBVTtrQ0FDVlI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU1iIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29udGV4dGtpdC8uL3NyYy9jb21wb25lbnRzL0hlYWRlci50c3g/YTY5NiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnO1xuaW1wb3J0IHsgSG9tZSwgU2V0dGluZ3MgfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuaW1wb3J0IFRoZW1lVG9nZ2xlIGZyb20gJy4vVGhlbWVUb2dnbGUnO1xuaW1wb3J0IExhbmd1YWdlVG9nZ2xlIGZyb20gJy4vTGFuZ3VhZ2VUb2dnbGUnO1xuaW1wb3J0IENsZWFyQ29udGVudEJ1dHRvbiBmcm9tICcuL0NsZWFyQ29udGVudEJ1dHRvbic7XG5pbXBvcnQgeyB1c2VDb250ZXh0U3RvcmUgfSBmcm9tICdAL3N0b3JlL2NvbnRleHRTdG9yZSc7XG5cbmludGVyZmFjZSBIZWFkZXJQcm9wcyB7XG4gIHRpdGxlOiBzdHJpbmc7XG4gIHN1YnRpdGxlPzogc3RyaW5nO1xuICBiYWNrTGluaz86IHtcbiAgICBocmVmOiBzdHJpbmc7XG4gICAgbGFiZWw6IHN0cmluZztcbiAgfTtcbiAgZW1vamk/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEhlYWRlcih7IHRpdGxlLCBzdWJ0aXRsZSwgYmFja0xpbmssIGVtb2ppIH06IEhlYWRlclByb3BzKSB7XG4gIGNvbnN0IHsgY3VycmVudExhbmd1YWdlIH0gPSB1c2VDb250ZXh0U3RvcmUoKTtcbiAgY29uc3QgW21vdW50ZWQsIHNldE1vdW50ZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgc2V0TW91bnRlZCh0cnVlKTtcbiAgfSwgW10pO1xuXG4gIGNvbnN0IGlzQXJhYmljID0gbW91bnRlZCA/IGN1cnJlbnRMYW5ndWFnZSA9PT0gJ2FyJyA6IGZhbHNlO1xuXG4gIHJldHVybiAoXG4gICAgPGhlYWRlciBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgey8qIENvbnRyb2xzIC0gRml4ZWQgcG9zaXRpb24gdmVydGljYWxseSBjZW50ZXJlZCBvbiByaWdodCAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgdG9wLTEvMiByaWdodC00IHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIHotNTBcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZS85MCBkYXJrOmJnLWdyYXktODAwLzkwIGJhY2tkcm9wLWJsdXItbWQgcm91bmRlZC0yeGwgc2hhZG93LXhsIGJvcmRlciBib3JkZXItZ3JheS0yMDAvNTAgZGFyazpib3JkZXItZ3JheS03MDAvNTAgcC0yXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGdhcC0yXCI+XG4gICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICBocmVmPVwiL1wiXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJlbGF0aXZlIHctMTIgaC0xMiByb3VuZGVkLXhsIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tZ3JheS01MCB0by1ncmF5LTEwMCBkYXJrOmZyb20tZ3JheS04MDAgZGFyazp0by1ncmF5LTkwMCBob3Zlcjpmcm9tLWdyYXktMTAwIGhvdmVyOnRvLWdyYXktMjAwIGRhcms6aG92ZXI6ZnJvbS1ncmF5LTcwMCBkYXJrOmhvdmVyOnRvLWdyYXktODAwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBlYXNlLWluLW91dCBzaGFkb3ctbGcgaG92ZXI6c2hhZG93LXhsIGJvcmRlciBib3JkZXItZ3JheS0yMDAgZGFyazpib3JkZXItZ3JheS02MDAgZ3JvdXAgb3ZlcmZsb3ctaGlkZGVuXCJcbiAgICAgICAgICAgICAgdGl0bGU9e2lzQXJhYmljID8gJ9in2YTYtdmB2K3YqSDYp9mE2LHYptmK2LPZitipJyA6ICdIb21lJ31cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPEhvbWUgY2xhc3NOYW1lPVwidy02IGgtNiB0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1ncmF5LTMwMCBncm91cC1ob3Zlcjp0ZXh0LWJsdWUtNjAwIGRhcms6Z3JvdXAtaG92ZXI6dGV4dC1ibHVlLTQwMCB0cmFuc2l0aW9uLWNvbG9yc1wiIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBTdWJ0bGUgZ2xvdyBlZmZlY3QgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ncmFkaWVudC10by1yIGZyb20tdHJhbnNwYXJlbnQgdmlhLXdoaXRlLzEwIHRvLXRyYW5zcGFyZW50IG9wYWNpdHktMCBncm91cC1ob3ZlcjpvcGFjaXR5LTEwMCB0cmFuc2l0aW9uLW9wYWNpdHkgZHVyYXRpb24tMzAwXCIgLz5cbiAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgIGhyZWY9XCIvc2V0dGluZ3NcIlxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyZWxhdGl2ZSB3LTEyIGgtMTIgcm91bmRlZC14bCBiZy1ncmFkaWVudC10by1iciBmcm9tLXB1cnBsZS01MCB0by12aW9sZXQtMTAwIGRhcms6ZnJvbS1ncmF5LTgwMCBkYXJrOnRvLWdyYXktOTAwIGhvdmVyOmZyb20tcHVycGxlLTEwMCBob3Zlcjp0by12aW9sZXQtMjAwIGRhcms6aG92ZXI6ZnJvbS1ncmF5LTcwMCBkYXJrOmhvdmVyOnRvLWdyYXktODAwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBlYXNlLWluLW91dCBzaGFkb3ctbGcgaG92ZXI6c2hhZG93LXhsIGJvcmRlciBib3JkZXItZ3JheS0yMDAgZGFyazpib3JkZXItZ3JheS02MDAgZ3JvdXAgb3ZlcmZsb3ctaGlkZGVuXCJcbiAgICAgICAgICAgICAgdGl0bGU9e2lzQXJhYmljID8gJ9il2LnYr9in2K/Yp9iqIEFQSScgOiAnQVBJIFNldHRpbmdzJ31cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPFNldHRpbmdzIGNsYXNzTmFtZT1cInctNiBoLTYgdGV4dC1wdXJwbGUtNjAwIGRhcms6dGV4dC1wdXJwbGUtNDAwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBncm91cC1ob3Zlcjpyb3RhdGUtOTBcIiAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7LyogU3VidGxlIGdsb3cgZWZmZWN0ICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctZ3JhZGllbnQtdG8tciBmcm9tLXRyYW5zcGFyZW50IHZpYS13aGl0ZS8xMCB0by10cmFuc3BhcmVudCBvcGFjaXR5LTAgZ3JvdXAtaG92ZXI6b3BhY2l0eS0xMDAgdHJhbnNpdGlvbi1vcGFjaXR5IGR1cmF0aW9uLTMwMFwiIC8+XG4gICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICA8Q2xlYXJDb250ZW50QnV0dG9uIC8+XG4gICAgICAgICAgICA8TGFuZ3VhZ2VUb2dnbGUgLz5cbiAgICAgICAgICAgIDxUaGVtZVRvZ2dsZSAvPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogSGVhZGVyIENvbnRlbnQgLSBBbHdheXMgY2VudGVyZWQgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTggcHQtNFwiPlxuICAgICAgICB7YmFja0xpbmsgJiYgKFxuICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICBocmVmPXtiYWNrTGluay5ocmVmfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTYwMCBob3Zlcjp0ZXh0LWJsdWUtODAwIGRhcms6dGV4dC1ibHVlLTQwMCBkYXJrOmhvdmVyOnRleHQtYmx1ZS0zMDAgbWItNCBpbmxpbmUtYmxvY2sgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIHtiYWNrTGluay5sYWJlbH1cbiAgICAgICAgICA8L0xpbms+XG4gICAgICAgICl9XG4gICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTR4bCBtZDp0ZXh0LTV4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGUgbWItMiB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgIHtlbW9qaSAmJiA8c3BhbiBjbGFzc05hbWU9XCJtci0yXCI+e2Vtb2ppfTwvc3Bhbj59XG4gICAgICAgICAge3RpdGxlfVxuICAgICAgICA8L2gxPlxuICAgICAgICB7c3VidGl0bGUgJiYgKFxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbGcgbWQ6dGV4dC14bCB0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1ncmF5LTMwMCBtYXgtdy0zeGwgbXgtYXV0byB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAge3N1YnRpdGxlfVxuICAgICAgICAgIDwvcD5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuICAgIDwvaGVhZGVyPlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZUVmZmVjdCIsInVzZVN0YXRlIiwiTGluayIsIkhvbWUiLCJTZXR0aW5ncyIsIlRoZW1lVG9nZ2xlIiwiTGFuZ3VhZ2VUb2dnbGUiLCJDbGVhckNvbnRlbnRCdXR0b24iLCJ1c2VDb250ZXh0U3RvcmUiLCJIZWFkZXIiLCJ0aXRsZSIsInN1YnRpdGxlIiwiYmFja0xpbmsiLCJlbW9qaSIsImN1cnJlbnRMYW5ndWFnZSIsIm1vdW50ZWQiLCJzZXRNb3VudGVkIiwiaXNBcmFiaWMiLCJoZWFkZXIiLCJjbGFzc05hbWUiLCJkaXYiLCJocmVmIiwibGFiZWwiLCJoMSIsInNwYW4iLCJwIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/LanguageToggle.tsx":
/*!*******************************************!*\
  !*** ./src/components/LanguageToggle.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LanguageToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction LanguageToggle() {\n    const { currentLanguage, setLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_1__.useContextStore)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: ()=>setLanguage(currentLanguage === \"ar\" ? \"en\" : \"ar\"),\n        className: \"relative w-12 h-12 rounded-xl bg-gradient-to-br from-green-50 to-emerald-100 dark:from-gray-800 dark:to-gray-900 hover:from-green-100 hover:to-emerald-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden\",\n        \"aria-label\": currentLanguage === \"ar\" ? \"Switch to English\" : \"التبديل إلى العربية\",\n        title: currentLanguage === \"ar\" ? \"Switch to English\" : \"التبديل إلى العربية\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `text-sm font-bold text-blue-600 dark:text-blue-400 transition-all duration-500 ease-in-out ${currentLanguage === \"en\" ? \"opacity-100 rotate-0 scale-100\" : \"opacity-0 rotate-180 scale-0\"}`,\n                        children: \"EN\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `text-sm font-bold text-green-600 dark:text-green-400 absolute transition-all duration-500 ease-in-out font-arabic ${currentLanguage === \"ar\" ? \"opacity-100 rotate-0 scale-100\" : \"opacity-0 -rotate-180 scale-0\"}`,\n                        children: \"عر\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/LanguageToggle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ProjectStats.tsx":
/*!*****************************************!*\
  !*** ./src/components/ProjectStats.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProjectStats)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ProjectStats() {\n    const { projectDefinition, contextMap, emotionalTone, technicalLayer, legalRisk, currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_1__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    // حساب إحصائيات المشروع\n    const calculateStats = ()=>{\n        const modules = [\n            {\n                name: \"projectDefinition\",\n                data: projectDefinition\n            },\n            {\n                name: \"contextMap\",\n                data: contextMap\n            },\n            {\n                name: \"emotionalTone\",\n                data: emotionalTone\n            },\n            {\n                name: \"technicalLayer\",\n                data: technicalLayer\n            },\n            {\n                name: \"legalRisk\",\n                data: legalRisk\n            }\n        ];\n        let totalFields = 0;\n        let completedFields = 0;\n        let totalWords = 0;\n        let advancedOptionsUsed = 0;\n        modules.forEach((module)=>{\n            const fields = Object.entries(module.data);\n            totalFields += fields.length;\n            fields.forEach(([key, value])=>{\n                if (value) {\n                    if (Array.isArray(value)) {\n                        if (value.length > 0) {\n                            completedFields++;\n                            advancedOptionsUsed++;\n                            totalWords += value.join(\" \").split(\" \").length;\n                        }\n                    } else if (typeof value === \"string\" && value.trim()) {\n                        completedFields++;\n                        totalWords += value.split(\" \").length;\n                        // تحقق من الخيارات المتقدمة\n                        if ([\n                            \"projectType\",\n                            \"complexity\",\n                            \"teamSize\",\n                            \"budget\",\n                            \"deploymentType\",\n                            \"architecturePattern\",\n                            \"scalingStrategy\",\n                            \"securityRequirements\",\n                            \"performanceTargets\",\n                            \"integrationNeeds\",\n                            \"monitoringTools\"\n                        ].includes(key)) {\n                            advancedOptionsUsed++;\n                        }\n                    }\n                }\n            });\n        });\n        const completionPercentage = totalFields > 0 ? Math.round(completedFields / totalFields * 100) : 0;\n        return {\n            totalFields,\n            completedFields,\n            completionPercentage,\n            totalWords,\n            advancedOptionsUsed,\n            estimatedReadingTime: Math.ceil(totalWords / 200) // متوسط 200 كلمة في الدقيقة\n        };\n    };\n    const stats = calculateStats();\n    // تحديد مستوى التقدم\n    const getProgressLevel = (percentage)=>{\n        if (percentage >= 80) return {\n            level: \"excellent\",\n            color: \"green\",\n            icon: \"\\uD83C\\uDF89\"\n        };\n        if (percentage >= 60) return {\n            level: \"good\",\n            color: \"blue\",\n            icon: \"\\uD83D\\uDC4D\"\n        };\n        if (percentage >= 40) return {\n            level: \"moderate\",\n            color: \"yellow\",\n            icon: \"⚡\"\n        };\n        if (percentage >= 20) return {\n            level: \"started\",\n            color: \"orange\",\n            icon: \"\\uD83D\\uDE80\"\n        };\n        return {\n            level: \"beginning\",\n            color: \"gray\",\n            icon: \"\\uD83D\\uDCDD\"\n        };\n    };\n    const progress = getProgressLevel(stats.completionPercentage);\n    const getProgressMessage = ()=>{\n        const messages = {\n            excellent: {\n                ar: \"ممتاز! مشروعك مكتمل تقريباً\",\n                en: \"Excellent! Your project is nearly complete\"\n            },\n            good: {\n                ar: \"جيد جداً! تقدم ملحوظ\",\n                en: \"Very good! Notable progress\"\n            },\n            moderate: {\n                ar: \"تقدم جيد، استمر!\",\n                en: \"Good progress, keep going!\"\n            },\n            started: {\n                ar: \"بداية جيدة!\",\n                en: \"Good start!\"\n            },\n            beginning: {\n                ar: \"ابدأ رحلتك!\",\n                en: \"Start your journey!\"\n            }\n        };\n        return messages[progress.level]?.[isArabic ? \"ar\" : \"en\"] || \"\";\n    };\n    if (stats.completedFields === 0) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl p-6 border border-green-200 dark:border-green-800\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `text-center mb-6 ${isArabic ? \"text-right\" : \"text-left\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2\",\n                        children: isArabic ? \"\\uD83D\\uDCCA إحصائيات المشروع\" : \"\\uD83D\\uDCCA Project Statistics\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                        children: isArabic ? \"تتبع تقدمك وإنجازاتك\" : \"Track your progress and achievements\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                children: isArabic ? \"نسبة الإكمال\" : \"Completion Rate\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-bold text-gray-900 dark:text-gray-100\",\n                                children: [\n                                    stats.completionPercentage,\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `h-3 rounded-full transition-all duration-500 ease-out bg-gradient-to-r ${progress.color === \"green\" ? \"from-green-400 to-emerald-500\" : progress.color === \"blue\" ? \"from-blue-400 to-cyan-500\" : progress.color === \"yellow\" ? \"from-yellow-400 to-orange-500\" : progress.color === \"orange\" ? \"from-orange-400 to-red-500\" : \"from-gray-400 to-gray-500\"}`,\n                            style: {\n                                width: `${stats.completionPercentage}%`\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `mt-2 text-center ${isArabic ? \"text-right\" : \"text-left\"}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                            children: [\n                                progress.icon,\n                                \" \",\n                                getProgressMessage()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `bg-white dark:bg-gray-800 rounded-lg p-4 text-center ${isArabic ? \"text-right\" : \"text-left\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-blue-600 dark:text-blue-400\",\n                                children: stats.completedFields\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-500 dark:text-gray-400 mt-1\",\n                                children: isArabic ? \"حقول مكتملة\" : \"Completed Fields\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-400 dark:text-gray-500\",\n                                children: isArabic ? `من أصل ${stats.totalFields}` : `out of ${stats.totalFields}`\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `bg-white dark:bg-gray-800 rounded-lg p-4 text-center ${isArabic ? \"text-right\" : \"text-left\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-purple-600 dark:text-purple-400\",\n                                children: stats.totalWords\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-500 dark:text-gray-400 mt-1\",\n                                children: isArabic ? \"إجمالي الكلمات\" : \"Total Words\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-400 dark:text-gray-500\",\n                                children: isArabic ? `${stats.estimatedReadingTime} دقائق قراءة` : `${stats.estimatedReadingTime} min read`\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `bg-white dark:bg-gray-800 rounded-lg p-4 text-center ${isArabic ? \"text-right\" : \"text-left\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-green-600 dark:text-green-400\",\n                                children: stats.advancedOptionsUsed\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-500 dark:text-gray-400 mt-1\",\n                                children: isArabic ? \"خيارات متقدمة\" : \"Advanced Options\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-400 dark:text-gray-500\",\n                                children: isArabic ? \"مستخدمة\" : \"Used\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `bg-white dark:bg-gray-800 rounded-lg p-4 text-center ${isArabic ? \"text-right\" : \"text-left\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl font-bold text-orange-600 dark:text-orange-400\",\n                                children: [\n                                    Math.round(stats.advancedOptionsUsed / Math.max(stats.completedFields, 1) * 100),\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-500 dark:text-gray-400 mt-1\",\n                                children: isArabic ? \"مستوى التخصيص\" : \"Customization Level\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-400 dark:text-gray-500\",\n                                children: isArabic ? \"متقدم\" : \"Advanced\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                lineNumber: 150,\n                columnNumber: 7\n            }, this),\n            stats.completionPercentage < 100 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 p-4 bg-blue-50 dark:bg-blue-900/30 rounded-lg border border-blue-200 dark:border-blue-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg\",\n                            children: \"\\uD83D\\uDCA1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: isArabic ? \"text-right\" : \"text-left\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium text-blue-900 dark:text-blue-100 mb-1\",\n                                    children: isArabic ? \"نصيحة للتحسين\" : \"Improvement Tip\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-blue-700 dark:text-blue-300\",\n                                    children: isArabic ? `أكمل ${stats.totalFields - stats.completedFields} حقل إضافي للحصول على سياق أكثر شمولية لمشروعك.` : `Complete ${stats.totalFields - stats.completedFields} more fields to get a more comprehensive context for your project.`\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n                lineNumber: 202,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectStats.tsx\",\n        lineNumber: 107,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ProjectStats.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ProjectSummaryCard.tsx":
/*!***********************************************!*\
  !*** ./src/components/ProjectSummaryCard.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProjectSummaryCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* harmony import */ var _lib_projectOptions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/projectOptions */ \"(ssr)/./src/lib/projectOptions.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ProjectSummaryCard() {\n    const { projectDefinition, currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_1__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    const getOptionLabel = (options, id)=>{\n        const option = options.find((opt)=>opt.id === id);\n        return option ? isArabic ? option.labelAr : option.label : \"\";\n    };\n    const getMultipleOptionsLabels = (options, ids)=>{\n        return ids.map((id)=>getOptionLabel(options, id)).filter(Boolean).join(\", \");\n    };\n    const hasAdvancedOptions = projectDefinition.projectType || projectDefinition.targetPlatforms?.length > 0 || projectDefinition.primaryLanguages?.length > 0 || projectDefinition.complexity || projectDefinition.budget || projectDefinition.teamSize || projectDefinition.deploymentType;\n    if (!hasAdvancedOptions) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-800\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `text-center mb-4 ${isArabic ? \"text-right\" : \"text-left\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2\",\n                        children: isArabic ? \"\\uD83D\\uDCCB ملخص تخصيص المشروع\" : \"\\uD83D\\uDCCB Project Customization Summary\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                        children: isArabic ? \"الخيارات المتقدمة المحددة لمشروعك\" : \"Advanced options selected for your project\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                children: [\n                    projectDefinition.projectType && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `bg-white dark:bg-gray-800 rounded-lg p-4 ${isArabic ? \"text-right\" : \"text-left\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: \"\\uD83C\\uDFAF\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-gray-700 dark:text-gray-300\",\n                                        children: isArabic ? \"نوع المشروع\" : \"Project Type\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                children: getOptionLabel(_lib_projectOptions__WEBPACK_IMPORTED_MODULE_2__.PROJECT_TYPES, projectDefinition.projectType)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, this),\n                    projectDefinition.complexity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `bg-white dark:bg-gray-800 rounded-lg p-4 ${isArabic ? \"text-right\" : \"text-left\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: \"⚡\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-gray-700 dark:text-gray-300\",\n                                        children: isArabic ? \"مستوى التعقيد\" : \"Complexity Level\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                children: getOptionLabel(_lib_projectOptions__WEBPACK_IMPORTED_MODULE_2__.COMPLEXITY_LEVELS, projectDefinition.complexity)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, this),\n                    projectDefinition.targetPlatforms && projectDefinition.targetPlatforms.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `bg-white dark:bg-gray-800 rounded-lg p-4 ${isArabic ? \"text-right\" : \"text-left\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: \"\\uD83D\\uDCF1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-gray-700 dark:text-gray-300\",\n                                        children: isArabic ? \"المنصات المستهدفة\" : \"Target Platforms\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                children: getMultipleOptionsLabels(_lib_projectOptions__WEBPACK_IMPORTED_MODULE_2__.TARGET_PLATFORMS, projectDefinition.targetPlatforms)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, this),\n                    projectDefinition.primaryLanguages && projectDefinition.primaryLanguages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `bg-white dark:bg-gray-800 rounded-lg p-4 ${isArabic ? \"text-right\" : \"text-left\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: \"\\uD83D\\uDCBB\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-gray-700 dark:text-gray-300\",\n                                        children: isArabic ? \"لغات البرمجة\" : \"Programming Languages\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                children: getMultipleOptionsLabels(_lib_projectOptions__WEBPACK_IMPORTED_MODULE_2__.PROGRAMMING_LANGUAGES, projectDefinition.primaryLanguages)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, this),\n                    projectDefinition.teamSize && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `bg-white dark:bg-gray-800 rounded-lg p-4 ${isArabic ? \"text-right\" : \"text-left\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: \"\\uD83D\\uDC65\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-gray-700 dark:text-gray-300\",\n                                        children: isArabic ? \"حجم الفريق\" : \"Team Size\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                children: getOptionLabel(_lib_projectOptions__WEBPACK_IMPORTED_MODULE_2__.TEAM_SIZES, projectDefinition.teamSize)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, this),\n                    projectDefinition.budget && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `bg-white dark:bg-gray-800 rounded-lg p-4 ${isArabic ? \"text-right\" : \"text-left\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: \"\\uD83D\\uDCB0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-gray-700 dark:text-gray-300\",\n                                        children: isArabic ? \"نطاق الميزانية\" : \"Budget Range\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                children: getOptionLabel(_lib_projectOptions__WEBPACK_IMPORTED_MODULE_2__.BUDGET_RANGES, projectDefinition.budget)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this),\n                    projectDefinition.deploymentType && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `bg-white dark:bg-gray-800 rounded-lg p-4 md:col-span-2 ${isArabic ? \"text-right\" : \"text-left\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: \"☁️\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-gray-700 dark:text-gray-300\",\n                                        children: isArabic ? \"نوع النشر\" : \"Deployment Type\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                children: getOptionLabel(_lib_projectOptions__WEBPACK_IMPORTED_MODULE_2__.DEPLOYMENT_TYPES, projectDefinition.deploymentType)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 p-4 bg-blue-50 dark:bg-blue-900/30 rounded-lg border border-blue-200 dark:border-blue-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg\",\n                            children: \"\\uD83D\\uDCA1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: isArabic ? \"text-right\" : \"text-left\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium text-blue-900 dark:text-blue-100 mb-1\",\n                                    children: isArabic ? \"نصيحة ذكية\" : \"Smart Tip\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-blue-700 dark:text-blue-300\",\n                                    children: isArabic ? \"هذه الخيارات ستساعد الذكاء الاصطناعي في تقديم اقتراحات أكثر دقة وتخصصاً لمشروعك.\" : \"These options will help AI provide more accurate and specialized suggestions for your project.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ProjectSummaryCard.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ProjectSummaryCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SmartRecommendations.tsx":
/*!*************************************************!*\
  !*** ./src/components/SmartRecommendations.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SmartRecommendations)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction SmartRecommendations() {\n    const { projectDefinition, technicalLayer, currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_1__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    const generateRecommendations = ()=>{\n        const recommendations = [];\n        // توصيات بناءً على نوع المشروع\n        if (projectDefinition.projectType === \"mobile-app\") {\n            if (!projectDefinition.targetPlatforms?.includes(\"android\") && !projectDefinition.targetPlatforms?.includes(\"ios\")) {\n                recommendations.push({\n                    id: \"mobile-platforms\",\n                    title: \"Select Mobile Platforms\",\n                    titleAr: \"اختر منصات الجوال\",\n                    description: \"Consider targeting Android and/or iOS for maximum reach\",\n                    descriptionAr: \"فكر في استهداف أندرويد و/أو iOS للوصول الأقصى\",\n                    icon: \"\\uD83D\\uDCF1\",\n                    priority: \"high\",\n                    category: \"Platform\",\n                    categoryAr: \"المنصة\"\n                });\n            }\n            if (!projectDefinition.primaryLanguages?.some((lang)=>[\n                    \"kotlin\",\n                    \"swift\",\n                    \"dart\"\n                ].includes(lang))) {\n                recommendations.push({\n                    id: \"mobile-languages\",\n                    title: \"Mobile Development Languages\",\n                    titleAr: \"لغات تطوير الجوال\",\n                    description: \"Consider Kotlin for Android, Swift for iOS, or Dart/Flutter for cross-platform\",\n                    descriptionAr: \"فكر في Kotlin لأندرويد، Swift لـ iOS، أو Dart/Flutter للمنصات المتعددة\",\n                    icon: \"\\uD83D\\uDCBB\",\n                    priority: \"high\",\n                    category: \"Technology\",\n                    categoryAr: \"التقنية\"\n                });\n            }\n        }\n        // توصيات بناءً على نوع المشروع - تطبيق ويب\n        if (projectDefinition.projectType === \"web-app\") {\n            if (!projectDefinition.primaryLanguages?.includes(\"javascript\")) {\n                recommendations.push({\n                    id: \"web-languages\",\n                    title: \"Web Development Stack\",\n                    titleAr: \"مجموعة تطوير الويب\",\n                    description: \"JavaScript/TypeScript is essential for modern web development\",\n                    descriptionAr: \"JavaScript/TypeScript ضروري لتطوير الويب الحديث\",\n                    icon: \"\\uD83C\\uDF10\",\n                    priority: \"high\",\n                    category: \"Technology\",\n                    categoryAr: \"التقنية\"\n                });\n            }\n            if (!technicalLayer.architecturePattern) {\n                recommendations.push({\n                    id: \"web-architecture\",\n                    title: \"Choose Architecture Pattern\",\n                    titleAr: \"اختر نمط الهندسة المعمارية\",\n                    description: \"Consider microservices for scalability or monolithic for simplicity\",\n                    descriptionAr: \"فكر في المايكروسيرفس للتوسع أو الأحادي للبساطة\",\n                    icon: \"\\uD83C\\uDFD7️\",\n                    priority: \"medium\",\n                    category: \"Architecture\",\n                    categoryAr: \"الهندسة المعمارية\"\n                });\n            }\n        }\n        // توصيات بناءً على مستوى التعقيد\n        if (projectDefinition.complexity === \"enterprise\") {\n            if (!technicalLayer.securityRequirements) {\n                recommendations.push({\n                    id: \"enterprise-security\",\n                    title: \"Enterprise Security Requirements\",\n                    titleAr: \"متطلبات الأمان المؤسسي\",\n                    description: \"Enterprise projects need comprehensive security measures\",\n                    descriptionAr: \"المشاريع المؤسسية تحتاج إجراءات أمان شاملة\",\n                    icon: \"\\uD83D\\uDD12\",\n                    priority: \"high\",\n                    category: \"Security\",\n                    categoryAr: \"الأمان\"\n                });\n            }\n            if (!technicalLayer.scalingStrategy) {\n                recommendations.push({\n                    id: \"enterprise-scaling\",\n                    title: \"Scaling Strategy Required\",\n                    titleAr: \"استراتيجية التوسع مطلوبة\",\n                    description: \"Plan for horizontal scaling and load balancing\",\n                    descriptionAr: \"خطط للتوسع الأفقي وتوزيع الأحمال\",\n                    icon: \"\\uD83D\\uDCC8\",\n                    priority: \"high\",\n                    category: \"Performance\",\n                    categoryAr: \"الأداء\"\n                });\n            }\n        }\n        // توصيات بناءً على حجم الفريق\n        if (projectDefinition.teamSize === \"large\" || projectDefinition.teamSize === \"enterprise\") {\n            if (!technicalLayer.monitoringTools) {\n                recommendations.push({\n                    id: \"team-monitoring\",\n                    title: \"Team Monitoring Tools\",\n                    titleAr: \"أدوات مراقبة الفريق\",\n                    description: \"Large teams need comprehensive monitoring and logging\",\n                    descriptionAr: \"الفرق الكبيرة تحتاج مراقبة وتسجيل شامل\",\n                    icon: \"\\uD83D\\uDCCA\",\n                    priority: \"medium\",\n                    category: \"Operations\",\n                    categoryAr: \"العمليات\"\n                });\n            }\n        }\n        // توصيات بناءً على نوع النشر\n        if (projectDefinition.deploymentType === \"cloud\") {\n            if (!technicalLayer.scalingStrategy) {\n                recommendations.push({\n                    id: \"cloud-scaling\",\n                    title: \"Cloud Auto-scaling\",\n                    titleAr: \"التوسع التلقائي السحابي\",\n                    description: \"Leverage cloud auto-scaling capabilities\",\n                    descriptionAr: \"استفد من قدرات التوسع التلقائي السحابي\",\n                    icon: \"☁️\",\n                    priority: \"medium\",\n                    category: \"Infrastructure\",\n                    categoryAr: \"البنية التحتية\"\n                });\n            }\n        }\n        // توصيات بناءً على الميزانية\n        if (projectDefinition.budget === \"startup\") {\n            recommendations.push({\n                id: \"startup-budget\",\n                title: \"Cost-Effective Solutions\",\n                titleAr: \"حلول فعالة من ناحية التكلفة\",\n                description: \"Consider open-source tools and serverless architecture\",\n                descriptionAr: \"فكر في الأدوات مفتوحة المصدر والهندسة بلا خادم\",\n                icon: \"\\uD83D\\uDCB0\",\n                priority: \"high\",\n                category: \"Budget\",\n                categoryAr: \"الميزانية\"\n            });\n        }\n        // توصيات عامة للذكاء الاصطناعي\n        if (projectDefinition.projectType === \"ai-model\" || projectDefinition.name?.toLowerCase().includes(\"ai\")) {\n            if (!projectDefinition.primaryLanguages?.includes(\"python\")) {\n                recommendations.push({\n                    id: \"ai-python\",\n                    title: \"Python for AI Development\",\n                    titleAr: \"Python لتطوير الذكاء الاصطناعي\",\n                    description: \"Python is the most popular language for AI/ML projects\",\n                    descriptionAr: \"Python هي اللغة الأكثر شعبية لمشاريع الذكاء الاصطناعي\",\n                    icon: \"\\uD83D\\uDC0D\",\n                    priority: \"high\",\n                    category: \"Technology\",\n                    categoryAr: \"التقنية\"\n                });\n            }\n        }\n        return recommendations.sort((a, b)=>{\n            const priorityOrder = {\n                high: 3,\n                medium: 2,\n                low: 1\n            };\n            return priorityOrder[b.priority] - priorityOrder[a.priority];\n        });\n    };\n    const recommendations = generateRecommendations();\n    if (recommendations.length === 0) {\n        return null;\n    }\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case \"high\":\n                return \"border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-900/20\";\n            case \"medium\":\n                return \"border-yellow-200 dark:border-yellow-800 bg-yellow-50 dark:bg-yellow-900/20\";\n            case \"low\":\n                return \"border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/20\";\n            default:\n                return \"border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800\";\n        }\n    };\n    const getPriorityLabel = (priority)=>{\n        const labels = {\n            high: {\n                ar: \"عالية\",\n                en: \"High\"\n            },\n            medium: {\n                ar: \"متوسطة\",\n                en: \"Medium\"\n            },\n            low: {\n                ar: \"منخفضة\",\n                en: \"Low\"\n            }\n        };\n        return labels[priority]?.[isArabic ? \"ar\" : \"en\"] || priority;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gradient-to-br from-purple-50 to-pink-100 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl p-6 border border-purple-200 dark:border-purple-800\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `text-center mb-6 ${isArabic ? \"text-right\" : \"text-left\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2\",\n                        children: isArabic ? \"\\uD83C\\uDFAF توصيات ذكية لمشروعك\" : \"\\uD83C\\uDFAF Smart Recommendations for Your Project\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartRecommendations.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                        children: isArabic ? \"اقتراحات مخصصة بناءً على خيارات مشروعك\" : \"Personalized suggestions based on your project options\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartRecommendations.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartRecommendations.tsx\",\n                lineNumber: 230,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: recommendations.map((rec)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `p-4 rounded-lg border ${getPriorityColor(rec.priority)} ${isArabic ? \"text-right\" : \"text-left\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start justify-between mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex items-center gap-2 ${isArabic ? \"flex-row-reverse\" : \"\"}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg\",\n                                                children: rec.icon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartRecommendations.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-gray-900 dark:text-gray-100\",\n                                                children: isArabic ? rec.titleAr : rec.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartRecommendations.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartRecommendations.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: `text-xs px-2 py-1 rounded-full ${rec.priority === \"high\" ? \"bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300\" : rec.priority === \"medium\" ? \"bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300\" : \"bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300\"}`,\n                                                children: getPriorityLabel(rec.priority)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartRecommendations.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                children: isArabic ? rec.categoryAr : rec.category\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartRecommendations.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartRecommendations.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartRecommendations.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                children: isArabic ? rec.descriptionAr : rec.description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartRecommendations.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, rec.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartRecommendations.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartRecommendations.tsx\",\n                lineNumber: 242,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartRecommendations.tsx\",\n        lineNumber: 229,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9TbWFydFJlY29tbWVuZGF0aW9ucy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFFdUQ7QUE2QnhDLFNBQVNDO0lBQ3RCLE1BQU0sRUFBRUMsaUJBQWlCLEVBQUVDLGNBQWMsRUFBRUMsZUFBZSxFQUFFLEdBQUdKLG9FQUFlQTtJQUM5RSxNQUFNSyxXQUFXRCxvQkFBb0I7SUFFckMsTUFBTUUsMEJBQTBCO1FBQzlCLE1BQU1DLGtCQUFvQyxFQUFFO1FBRTVDLCtCQUErQjtRQUMvQixJQUFJTCxrQkFBa0JNLFdBQVcsS0FBSyxjQUFjO1lBQ2xELElBQUksQ0FBQ04sa0JBQWtCTyxlQUFlLEVBQUVDLFNBQVMsY0FBYyxDQUFDUixrQkFBa0JPLGVBQWUsRUFBRUMsU0FBUyxRQUFRO2dCQUNsSEgsZ0JBQWdCSSxJQUFJLENBQUM7b0JBQ25CQyxJQUFJO29CQUNKQyxPQUFPO29CQUNQQyxTQUFTO29CQUNUQyxhQUFhO29CQUNiQyxlQUFlO29CQUNmQyxNQUFNO29CQUNOQyxVQUFVO29CQUNWQyxVQUFVO29CQUNWQyxZQUFZO2dCQUNkO1lBQ0Y7WUFFQSxJQUFJLENBQUNsQixrQkFBa0JtQixnQkFBZ0IsRUFBRUMsS0FBS0MsQ0FBQUEsT0FBUTtvQkFBQztvQkFBVTtvQkFBUztpQkFBTyxDQUFDYixRQUFRLENBQUNhLFFBQVE7Z0JBQ2pHaEIsZ0JBQWdCSSxJQUFJLENBQUM7b0JBQ25CQyxJQUFJO29CQUNKQyxPQUFPO29CQUNQQyxTQUFTO29CQUNUQyxhQUFhO29CQUNiQyxlQUFlO29CQUNmQyxNQUFNO29CQUNOQyxVQUFVO29CQUNWQyxVQUFVO29CQUNWQyxZQUFZO2dCQUNkO1lBQ0Y7UUFDRjtRQUVBLDJDQUEyQztRQUMzQyxJQUFJbEIsa0JBQWtCTSxXQUFXLEtBQUssV0FBVztZQUMvQyxJQUFJLENBQUNOLGtCQUFrQm1CLGdCQUFnQixFQUFFWCxTQUFTLGVBQWU7Z0JBQy9ESCxnQkFBZ0JJLElBQUksQ0FBQztvQkFDbkJDLElBQUk7b0JBQ0pDLE9BQU87b0JBQ1BDLFNBQVM7b0JBQ1RDLGFBQWE7b0JBQ2JDLGVBQWU7b0JBQ2ZDLE1BQU07b0JBQ05DLFVBQVU7b0JBQ1ZDLFVBQVU7b0JBQ1ZDLFlBQVk7Z0JBQ2Q7WUFDRjtZQUVBLElBQUksQ0FBQ2pCLGVBQWVxQixtQkFBbUIsRUFBRTtnQkFDdkNqQixnQkFBZ0JJLElBQUksQ0FBQztvQkFDbkJDLElBQUk7b0JBQ0pDLE9BQU87b0JBQ1BDLFNBQVM7b0JBQ1RDLGFBQWE7b0JBQ2JDLGVBQWU7b0JBQ2ZDLE1BQU07b0JBQ05DLFVBQVU7b0JBQ1ZDLFVBQVU7b0JBQ1ZDLFlBQVk7Z0JBQ2Q7WUFDRjtRQUNGO1FBRUEsaUNBQWlDO1FBQ2pDLElBQUlsQixrQkFBa0J1QixVQUFVLEtBQUssY0FBYztZQUNqRCxJQUFJLENBQUN0QixlQUFldUIsb0JBQW9CLEVBQUU7Z0JBQ3hDbkIsZ0JBQWdCSSxJQUFJLENBQUM7b0JBQ25CQyxJQUFJO29CQUNKQyxPQUFPO29CQUNQQyxTQUFTO29CQUNUQyxhQUFhO29CQUNiQyxlQUFlO29CQUNmQyxNQUFNO29CQUNOQyxVQUFVO29CQUNWQyxVQUFVO29CQUNWQyxZQUFZO2dCQUNkO1lBQ0Y7WUFFQSxJQUFJLENBQUNqQixlQUFld0IsZUFBZSxFQUFFO2dCQUNuQ3BCLGdCQUFnQkksSUFBSSxDQUFDO29CQUNuQkMsSUFBSTtvQkFDSkMsT0FBTztvQkFDUEMsU0FBUztvQkFDVEMsYUFBYTtvQkFDYkMsZUFBZTtvQkFDZkMsTUFBTTtvQkFDTkMsVUFBVTtvQkFDVkMsVUFBVTtvQkFDVkMsWUFBWTtnQkFDZDtZQUNGO1FBQ0Y7UUFFQSw4QkFBOEI7UUFDOUIsSUFBSWxCLGtCQUFrQjBCLFFBQVEsS0FBSyxXQUFXMUIsa0JBQWtCMEIsUUFBUSxLQUFLLGNBQWM7WUFDekYsSUFBSSxDQUFDekIsZUFBZTBCLGVBQWUsRUFBRTtnQkFDbkN0QixnQkFBZ0JJLElBQUksQ0FBQztvQkFDbkJDLElBQUk7b0JBQ0pDLE9BQU87b0JBQ1BDLFNBQVM7b0JBQ1RDLGFBQWE7b0JBQ2JDLGVBQWU7b0JBQ2ZDLE1BQU07b0JBQ05DLFVBQVU7b0JBQ1ZDLFVBQVU7b0JBQ1ZDLFlBQVk7Z0JBQ2Q7WUFDRjtRQUNGO1FBRUEsNkJBQTZCO1FBQzdCLElBQUlsQixrQkFBa0I0QixjQUFjLEtBQUssU0FBUztZQUNoRCxJQUFJLENBQUMzQixlQUFld0IsZUFBZSxFQUFFO2dCQUNuQ3BCLGdCQUFnQkksSUFBSSxDQUFDO29CQUNuQkMsSUFBSTtvQkFDSkMsT0FBTztvQkFDUEMsU0FBUztvQkFDVEMsYUFBYTtvQkFDYkMsZUFBZTtvQkFDZkMsTUFBTTtvQkFDTkMsVUFBVTtvQkFDVkMsVUFBVTtvQkFDVkMsWUFBWTtnQkFDZDtZQUNGO1FBQ0Y7UUFFQSw2QkFBNkI7UUFDN0IsSUFBSWxCLGtCQUFrQjZCLE1BQU0sS0FBSyxXQUFXO1lBQzFDeEIsZ0JBQWdCSSxJQUFJLENBQUM7Z0JBQ25CQyxJQUFJO2dCQUNKQyxPQUFPO2dCQUNQQyxTQUFTO2dCQUNUQyxhQUFhO2dCQUNiQyxlQUFlO2dCQUNmQyxNQUFNO2dCQUNOQyxVQUFVO2dCQUNWQyxVQUFVO2dCQUNWQyxZQUFZO1lBQ2Q7UUFDRjtRQUVBLCtCQUErQjtRQUMvQixJQUFJbEIsa0JBQWtCTSxXQUFXLEtBQUssY0FBY04sa0JBQWtCOEIsSUFBSSxFQUFFQyxjQUFjdkIsU0FBUyxPQUFPO1lBQ3hHLElBQUksQ0FBQ1Isa0JBQWtCbUIsZ0JBQWdCLEVBQUVYLFNBQVMsV0FBVztnQkFDM0RILGdCQUFnQkksSUFBSSxDQUFDO29CQUNuQkMsSUFBSTtvQkFDSkMsT0FBTztvQkFDUEMsU0FBUztvQkFDVEMsYUFBYTtvQkFDYkMsZUFBZTtvQkFDZkMsTUFBTTtvQkFDTkMsVUFBVTtvQkFDVkMsVUFBVTtvQkFDVkMsWUFBWTtnQkFDZDtZQUNGO1FBQ0Y7UUFFQSxPQUFPYixnQkFBZ0IyQixJQUFJLENBQUMsQ0FBQ0MsR0FBR0M7WUFDOUIsTUFBTUMsZ0JBQWdCO2dCQUFFQyxNQUFNO2dCQUFHQyxRQUFRO2dCQUFHQyxLQUFLO1lBQUU7WUFDbkQsT0FBT0gsYUFBYSxDQUFDRCxFQUFFbEIsUUFBUSxDQUFDLEdBQUdtQixhQUFhLENBQUNGLEVBQUVqQixRQUFRLENBQUM7UUFDOUQ7SUFDRjtJQUVBLE1BQU1YLGtCQUFrQkQ7SUFFeEIsSUFBSUMsZ0JBQWdCa0MsTUFBTSxLQUFLLEdBQUc7UUFDaEMsT0FBTztJQUNUO0lBRUEsTUFBTUMsbUJBQW1CLENBQUN4QjtRQUN4QixPQUFRQTtZQUNOLEtBQUs7Z0JBQVEsT0FBTztZQUNwQixLQUFLO2dCQUFVLE9BQU87WUFDdEIsS0FBSztnQkFBTyxPQUFPO1lBQ25CO2dCQUFTLE9BQU87UUFDbEI7SUFDRjtJQUVBLE1BQU15QixtQkFBbUIsQ0FBQ3pCO1FBQ3hCLE1BQU0wQixTQUFTO1lBQ2JOLE1BQU07Z0JBQUVPLElBQUk7Z0JBQVNDLElBQUk7WUFBTztZQUNoQ1AsUUFBUTtnQkFBRU0sSUFBSTtnQkFBVUMsSUFBSTtZQUFTO1lBQ3JDTixLQUFLO2dCQUFFSyxJQUFJO2dCQUFVQyxJQUFJO1lBQU07UUFDakM7UUFDQSxPQUFPRixNQUFNLENBQUMxQixTQUFnQyxFQUFFLENBQUNiLFdBQVcsT0FBTyxLQUFLLElBQUlhO0lBQzlFO0lBRUEscUJBQ0UsOERBQUM2QjtRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0Q7Z0JBQUlDLFdBQVcsQ0FBQyxpQkFBaUIsRUFBRTNDLFdBQVcsZUFBZSxZQUFZLENBQUM7O2tDQUN6RSw4REFBQzRDO3dCQUFHRCxXQUFVO2tDQUNYM0MsV0FBVyxxQ0FBMkI7Ozs7OztrQ0FFekMsOERBQUM2Qzt3QkFBRUYsV0FBVTtrQ0FDVjNDLFdBQ0csMkNBQ0E7Ozs7Ozs7Ozs7OzswQkFLUiw4REFBQzBDO2dCQUFJQyxXQUFVOzBCQUNaekMsZ0JBQWdCNEMsR0FBRyxDQUFDLENBQUNDLG9CQUNwQiw4REFBQ0w7d0JBRUNDLFdBQVcsQ0FBQyxzQkFBc0IsRUFBRU4saUJBQWlCVSxJQUFJbEMsUUFBUSxFQUFFLENBQUMsRUFBRWIsV0FBVyxlQUFlLFlBQVksQ0FBQzs7MENBRTdHLDhEQUFDMEM7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVyxDQUFDLHdCQUF3QixFQUFFM0MsV0FBVyxxQkFBcUIsR0FBRyxDQUFDOzswREFDN0UsOERBQUNnRDtnREFBS0wsV0FBVTswREFBV0ksSUFBSW5DLElBQUk7Ozs7OzswREFDbkMsOERBQUNxQztnREFBR04sV0FBVTswREFDWDNDLFdBQVcrQyxJQUFJdEMsT0FBTyxHQUFHc0MsSUFBSXZDLEtBQUs7Ozs7Ozs7Ozs7OztrREFHdkMsOERBQUNrQzt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNLO2dEQUFLTCxXQUFXLENBQUMsK0JBQStCLEVBQy9DSSxJQUFJbEMsUUFBUSxLQUFLLFNBQVMsaUVBQzFCa0MsSUFBSWxDLFFBQVEsS0FBSyxXQUFXLDZFQUM1Qix1RUFDRCxDQUFDOzBEQUNDeUIsaUJBQWlCUyxJQUFJbEMsUUFBUTs7Ozs7OzBEQUVoQyw4REFBQ21DO2dEQUFLTCxXQUFVOzBEQUNiM0MsV0FBVytDLElBQUloQyxVQUFVLEdBQUdnQyxJQUFJakMsUUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUkvQyw4REFBQytCO2dDQUFFRixXQUFVOzBDQUNWM0MsV0FBVytDLElBQUlwQyxhQUFhLEdBQUdvQyxJQUFJckMsV0FBVzs7Ozs7Ozt1QkF4QjVDcUMsSUFBSXhDLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7QUErQnZCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29udGV4dGtpdC8uL3NyYy9jb21wb25lbnRzL1NtYXJ0UmVjb21tZW5kYXRpb25zLnRzeD9mN2YzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlQ29udGV4dFN0b3JlIH0gZnJvbSAnQC9zdG9yZS9jb250ZXh0U3RvcmUnO1xuaW1wb3J0IHsgXG4gIFBST0pFQ1RfVFlQRVMsIFxuICBUQVJHRVRfUExBVEZPUk1TLCBcbiAgUFJPR1JBTU1JTkdfTEFOR1VBR0VTLCBcbiAgQ09NUExFWElUWV9MRVZFTFMsXG4gIEJVREdFVF9SQU5HRVMsXG4gIFRFQU1fU0laRVMsXG4gIERFUExPWU1FTlRfVFlQRVNcbn0gZnJvbSAnQC9saWIvcHJvamVjdE9wdGlvbnMnO1xuaW1wb3J0IHsgXG4gIEFSQ0hJVEVDVFVSRV9QQVRURVJOUyxcbiAgU0NBTElOR19TVFJBVEVHSUVTLFxuICBTRUNVUklUWV9SRVFVSVJFTUVOVFMsXG4gIFBFUkZPUk1BTkNFX1RBUkdFVFNcbn0gZnJvbSAnQC9saWIvdGVjaG5pY2FsT3B0aW9ucyc7XG5cbmludGVyZmFjZSBSZWNvbW1lbmRhdGlvbiB7XG4gIGlkOiBzdHJpbmc7XG4gIHRpdGxlOiBzdHJpbmc7XG4gIHRpdGxlQXI6IHN0cmluZztcbiAgZGVzY3JpcHRpb246IHN0cmluZztcbiAgZGVzY3JpcHRpb25Bcjogc3RyaW5nO1xuICBpY29uOiBzdHJpbmc7XG4gIHByaW9yaXR5OiAnaGlnaCcgfCAnbWVkaXVtJyB8ICdsb3cnO1xuICBjYXRlZ29yeTogc3RyaW5nO1xuICBjYXRlZ29yeUFyOiBzdHJpbmc7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFNtYXJ0UmVjb21tZW5kYXRpb25zKCkge1xuICBjb25zdCB7IHByb2plY3REZWZpbml0aW9uLCB0ZWNobmljYWxMYXllciwgY3VycmVudExhbmd1YWdlIH0gPSB1c2VDb250ZXh0U3RvcmUoKTtcbiAgY29uc3QgaXNBcmFiaWMgPSBjdXJyZW50TGFuZ3VhZ2UgPT09ICdhcic7XG5cbiAgY29uc3QgZ2VuZXJhdGVSZWNvbW1lbmRhdGlvbnMgPSAoKTogUmVjb21tZW5kYXRpb25bXSA9PiB7XG4gICAgY29uc3QgcmVjb21tZW5kYXRpb25zOiBSZWNvbW1lbmRhdGlvbltdID0gW107XG5cbiAgICAvLyDYqtmI2LXZitin2Kog2KjZhtin2KHZiyDYudmE2Ykg2YbZiNi5INin2YTZhdi02LHZiNi5XG4gICAgaWYgKHByb2plY3REZWZpbml0aW9uLnByb2plY3RUeXBlID09PSAnbW9iaWxlLWFwcCcpIHtcbiAgICAgIGlmICghcHJvamVjdERlZmluaXRpb24udGFyZ2V0UGxhdGZvcm1zPy5pbmNsdWRlcygnYW5kcm9pZCcpICYmICFwcm9qZWN0RGVmaW5pdGlvbi50YXJnZXRQbGF0Zm9ybXM/LmluY2x1ZGVzKCdpb3MnKSkge1xuICAgICAgICByZWNvbW1lbmRhdGlvbnMucHVzaCh7XG4gICAgICAgICAgaWQ6ICdtb2JpbGUtcGxhdGZvcm1zJyxcbiAgICAgICAgICB0aXRsZTogJ1NlbGVjdCBNb2JpbGUgUGxhdGZvcm1zJyxcbiAgICAgICAgICB0aXRsZUFyOiAn2KfYrtiq2LEg2YXZhti12KfYqiDYp9mE2KzZiNin2YQnLFxuICAgICAgICAgIGRlc2NyaXB0aW9uOiAnQ29uc2lkZXIgdGFyZ2V0aW5nIEFuZHJvaWQgYW5kL29yIGlPUyBmb3IgbWF4aW11bSByZWFjaCcsXG4gICAgICAgICAgZGVzY3JpcHRpb25BcjogJ9mB2YPYsSDZgdmKINin2LPYqtmH2K/Yp9mBINij2YbYr9ix2YjZitivINmIL9ij2YggaU9TINmE2YTZiNi12YjZhCDYp9mE2KPZgti12YknLFxuICAgICAgICAgIGljb246ICfwn5OxJyxcbiAgICAgICAgICBwcmlvcml0eTogJ2hpZ2gnLFxuICAgICAgICAgIGNhdGVnb3J5OiAnUGxhdGZvcm0nLFxuICAgICAgICAgIGNhdGVnb3J5QXI6ICfYp9mE2YXZhti12KknXG4gICAgICAgIH0pO1xuICAgICAgfVxuXG4gICAgICBpZiAoIXByb2plY3REZWZpbml0aW9uLnByaW1hcnlMYW5ndWFnZXM/LnNvbWUobGFuZyA9PiBbJ2tvdGxpbicsICdzd2lmdCcsICdkYXJ0J10uaW5jbHVkZXMobGFuZykpKSB7XG4gICAgICAgIHJlY29tbWVuZGF0aW9ucy5wdXNoKHtcbiAgICAgICAgICBpZDogJ21vYmlsZS1sYW5ndWFnZXMnLFxuICAgICAgICAgIHRpdGxlOiAnTW9iaWxlIERldmVsb3BtZW50IExhbmd1YWdlcycsXG4gICAgICAgICAgdGl0bGVBcjogJ9mE2LrYp9iqINiq2LfZiNmK2LEg2KfZhNis2YjYp9mEJyxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogJ0NvbnNpZGVyIEtvdGxpbiBmb3IgQW5kcm9pZCwgU3dpZnQgZm9yIGlPUywgb3IgRGFydC9GbHV0dGVyIGZvciBjcm9zcy1wbGF0Zm9ybScsXG4gICAgICAgICAgZGVzY3JpcHRpb25BcjogJ9mB2YPYsSDZgdmKIEtvdGxpbiDZhNij2YbYr9ix2YjZitiv2IwgU3dpZnQg2YTZgCBpT1PYjCDYo9mIIERhcnQvRmx1dHRlciDZhNmE2YXZhti12KfYqiDYp9mE2YXYqti52K/Yr9ipJyxcbiAgICAgICAgICBpY29uOiAn8J+SuycsXG4gICAgICAgICAgcHJpb3JpdHk6ICdoaWdoJyxcbiAgICAgICAgICBjYXRlZ29yeTogJ1RlY2hub2xvZ3knLFxuICAgICAgICAgIGNhdGVnb3J5QXI6ICfYp9mE2KrZgtmG2YrYqSdcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8g2KrZiNi12YrYp9iqINio2YbYp9ih2Ysg2LnZhNmJINmG2YjYuSDYp9mE2YXYtNix2YjYuSAtINiq2LfYqNmK2YIg2YjZitioXG4gICAgaWYgKHByb2plY3REZWZpbml0aW9uLnByb2plY3RUeXBlID09PSAnd2ViLWFwcCcpIHtcbiAgICAgIGlmICghcHJvamVjdERlZmluaXRpb24ucHJpbWFyeUxhbmd1YWdlcz8uaW5jbHVkZXMoJ2phdmFzY3JpcHQnKSkge1xuICAgICAgICByZWNvbW1lbmRhdGlvbnMucHVzaCh7XG4gICAgICAgICAgaWQ6ICd3ZWItbGFuZ3VhZ2VzJyxcbiAgICAgICAgICB0aXRsZTogJ1dlYiBEZXZlbG9wbWVudCBTdGFjaycsXG4gICAgICAgICAgdGl0bGVBcjogJ9mF2KzZhdmI2LnYqSDYqti32YjZitixINin2YTZiNmK2KgnLFxuICAgICAgICAgIGRlc2NyaXB0aW9uOiAnSmF2YVNjcmlwdC9UeXBlU2NyaXB0IGlzIGVzc2VudGlhbCBmb3IgbW9kZXJuIHdlYiBkZXZlbG9wbWVudCcsXG4gICAgICAgICAgZGVzY3JpcHRpb25BcjogJ0phdmFTY3JpcHQvVHlwZVNjcmlwdCDYttix2YjYsdmKINmE2KrYt9mI2YrYsSDYp9mE2YjZitioINin2YTYrdiv2YrYqycsXG4gICAgICAgICAgaWNvbjogJ/CfjJAnLFxuICAgICAgICAgIHByaW9yaXR5OiAnaGlnaCcsXG4gICAgICAgICAgY2F0ZWdvcnk6ICdUZWNobm9sb2d5JyxcbiAgICAgICAgICBjYXRlZ29yeUFyOiAn2KfZhNiq2YLZhtmK2KknXG4gICAgICAgIH0pO1xuICAgICAgfVxuXG4gICAgICBpZiAoIXRlY2huaWNhbExheWVyLmFyY2hpdGVjdHVyZVBhdHRlcm4pIHtcbiAgICAgICAgcmVjb21tZW5kYXRpb25zLnB1c2goe1xuICAgICAgICAgIGlkOiAnd2ViLWFyY2hpdGVjdHVyZScsXG4gICAgICAgICAgdGl0bGU6ICdDaG9vc2UgQXJjaGl0ZWN0dXJlIFBhdHRlcm4nLFxuICAgICAgICAgIHRpdGxlQXI6ICfYp9iu2KrYsSDZhtmF2Lcg2KfZhNmH2YbYr9iz2Kkg2KfZhNmF2LnZhdin2LHZitipJyxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogJ0NvbnNpZGVyIG1pY3Jvc2VydmljZXMgZm9yIHNjYWxhYmlsaXR5IG9yIG1vbm9saXRoaWMgZm9yIHNpbXBsaWNpdHknLFxuICAgICAgICAgIGRlc2NyaXB0aW9uQXI6ICfZgdmD2LEg2YHZiiDYp9mE2YXYp9mK2YPYsdmI2LPZitix2YHYsyDZhNmE2KrZiNiz2Lkg2KPZiCDYp9mE2KPYrdin2K/ZiiDZhNmE2KjYs9in2LfYqScsXG4gICAgICAgICAgaWNvbjogJ/Cfj5fvuI8nLFxuICAgICAgICAgIHByaW9yaXR5OiAnbWVkaXVtJyxcbiAgICAgICAgICBjYXRlZ29yeTogJ0FyY2hpdGVjdHVyZScsXG4gICAgICAgICAgY2F0ZWdvcnlBcjogJ9in2YTZh9mG2K/Ys9ipINin2YTZhdi52YXYp9ix2YrYqSdcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8g2KrZiNi12YrYp9iqINio2YbYp9ih2Ysg2LnZhNmJINmF2LPYqtmI2Ykg2KfZhNiq2LnZgtmK2K9cbiAgICBpZiAocHJvamVjdERlZmluaXRpb24uY29tcGxleGl0eSA9PT0gJ2VudGVycHJpc2UnKSB7XG4gICAgICBpZiAoIXRlY2huaWNhbExheWVyLnNlY3VyaXR5UmVxdWlyZW1lbnRzKSB7XG4gICAgICAgIHJlY29tbWVuZGF0aW9ucy5wdXNoKHtcbiAgICAgICAgICBpZDogJ2VudGVycHJpc2Utc2VjdXJpdHknLFxuICAgICAgICAgIHRpdGxlOiAnRW50ZXJwcmlzZSBTZWN1cml0eSBSZXF1aXJlbWVudHMnLFxuICAgICAgICAgIHRpdGxlQXI6ICfZhdiq2LfZhNio2KfYqiDYp9mE2KPZhdin2YYg2KfZhNmF2KTYs9iz2YonLFxuICAgICAgICAgIGRlc2NyaXB0aW9uOiAnRW50ZXJwcmlzZSBwcm9qZWN0cyBuZWVkIGNvbXByZWhlbnNpdmUgc2VjdXJpdHkgbWVhc3VyZXMnLFxuICAgICAgICAgIGRlc2NyaXB0aW9uQXI6ICfYp9mE2YXYtNin2LHZiti5INin2YTZhdik2LPYs9mK2Kkg2KrYrdiq2KfYrCDYpdis2LHYp9ih2KfYqiDYo9mF2KfZhiDYtNin2YXZhNipJyxcbiAgICAgICAgICBpY29uOiAn8J+UkicsXG4gICAgICAgICAgcHJpb3JpdHk6ICdoaWdoJyxcbiAgICAgICAgICBjYXRlZ29yeTogJ1NlY3VyaXR5JyxcbiAgICAgICAgICBjYXRlZ29yeUFyOiAn2KfZhNij2YXYp9mGJ1xuICAgICAgICB9KTtcbiAgICAgIH1cblxuICAgICAgaWYgKCF0ZWNobmljYWxMYXllci5zY2FsaW5nU3RyYXRlZ3kpIHtcbiAgICAgICAgcmVjb21tZW5kYXRpb25zLnB1c2goe1xuICAgICAgICAgIGlkOiAnZW50ZXJwcmlzZS1zY2FsaW5nJyxcbiAgICAgICAgICB0aXRsZTogJ1NjYWxpbmcgU3RyYXRlZ3kgUmVxdWlyZWQnLFxuICAgICAgICAgIHRpdGxlQXI6ICfYp9iz2KrYsdin2KrZitis2YrYqSDYp9mE2KrZiNiz2Lkg2YXYt9mE2YjYqNipJyxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogJ1BsYW4gZm9yIGhvcml6b250YWwgc2NhbGluZyBhbmQgbG9hZCBiYWxhbmNpbmcnLFxuICAgICAgICAgIGRlc2NyaXB0aW9uQXI6ICfYrti32Lcg2YTZhNiq2YjYs9i5INin2YTYo9mB2YLZiiDZiNiq2YjYstmK2Lkg2KfZhNij2K3Zhdin2YQnLFxuICAgICAgICAgIGljb246ICfwn5OIJyxcbiAgICAgICAgICBwcmlvcml0eTogJ2hpZ2gnLFxuICAgICAgICAgIGNhdGVnb3J5OiAnUGVyZm9ybWFuY2UnLFxuICAgICAgICAgIGNhdGVnb3J5QXI6ICfYp9mE2KPYr9in2KEnXG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vINiq2YjYtdmK2KfYqiDYqNmG2KfYodmLINi52YTZiSDYrdis2YUg2KfZhNmB2LHZitmCXG4gICAgaWYgKHByb2plY3REZWZpbml0aW9uLnRlYW1TaXplID09PSAnbGFyZ2UnIHx8IHByb2plY3REZWZpbml0aW9uLnRlYW1TaXplID09PSAnZW50ZXJwcmlzZScpIHtcbiAgICAgIGlmICghdGVjaG5pY2FsTGF5ZXIubW9uaXRvcmluZ1Rvb2xzKSB7XG4gICAgICAgIHJlY29tbWVuZGF0aW9ucy5wdXNoKHtcbiAgICAgICAgICBpZDogJ3RlYW0tbW9uaXRvcmluZycsXG4gICAgICAgICAgdGl0bGU6ICdUZWFtIE1vbml0b3JpbmcgVG9vbHMnLFxuICAgICAgICAgIHRpdGxlQXI6ICfYo9iv2YjYp9iqINmF2LHYp9mC2KjYqSDYp9mE2YHYsdmK2YInLFxuICAgICAgICAgIGRlc2NyaXB0aW9uOiAnTGFyZ2UgdGVhbXMgbmVlZCBjb21wcmVoZW5zaXZlIG1vbml0b3JpbmcgYW5kIGxvZ2dpbmcnLFxuICAgICAgICAgIGRlc2NyaXB0aW9uQXI6ICfYp9mE2YHYsdmCINin2YTZg9io2YrYsdipINiq2K3Yqtin2Kwg2YXYsdin2YLYqNipINmI2KrYs9is2YrZhCDYtNin2YXZhCcsXG4gICAgICAgICAgaWNvbjogJ/Cfk4onLFxuICAgICAgICAgIHByaW9yaXR5OiAnbWVkaXVtJyxcbiAgICAgICAgICBjYXRlZ29yeTogJ09wZXJhdGlvbnMnLFxuICAgICAgICAgIGNhdGVnb3J5QXI6ICfYp9mE2LnZhdmE2YrYp9iqJ1xuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDYqtmI2LXZitin2Kog2KjZhtin2KHZiyDYudmE2Ykg2YbZiNi5INin2YTZhti02LFcbiAgICBpZiAocHJvamVjdERlZmluaXRpb24uZGVwbG95bWVudFR5cGUgPT09ICdjbG91ZCcpIHtcbiAgICAgIGlmICghdGVjaG5pY2FsTGF5ZXIuc2NhbGluZ1N0cmF0ZWd5KSB7XG4gICAgICAgIHJlY29tbWVuZGF0aW9ucy5wdXNoKHtcbiAgICAgICAgICBpZDogJ2Nsb3VkLXNjYWxpbmcnLFxuICAgICAgICAgIHRpdGxlOiAnQ2xvdWQgQXV0by1zY2FsaW5nJyxcbiAgICAgICAgICB0aXRsZUFyOiAn2KfZhNiq2YjYs9i5INin2YTYqtmE2YLYp9im2Yog2KfZhNiz2K3Yp9io2YonLFxuICAgICAgICAgIGRlc2NyaXB0aW9uOiAnTGV2ZXJhZ2UgY2xvdWQgYXV0by1zY2FsaW5nIGNhcGFiaWxpdGllcycsXG4gICAgICAgICAgZGVzY3JpcHRpb25BcjogJ9in2LPYqtmB2K8g2YXZhiDZgtiv2LHYp9iqINin2YTYqtmI2LPYuSDYp9mE2KrZhNmC2KfYptmKINin2YTYs9it2KfYqNmKJyxcbiAgICAgICAgICBpY29uOiAn4piB77iPJyxcbiAgICAgICAgICBwcmlvcml0eTogJ21lZGl1bScsXG4gICAgICAgICAgY2F0ZWdvcnk6ICdJbmZyYXN0cnVjdHVyZScsXG4gICAgICAgICAgY2F0ZWdvcnlBcjogJ9in2YTYqNmG2YrYqSDYp9mE2KrYrdiq2YrYqSdcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8g2KrZiNi12YrYp9iqINio2YbYp9ih2Ysg2LnZhNmJINin2YTZhdmK2LLYp9mG2YrYqVxuICAgIGlmIChwcm9qZWN0RGVmaW5pdGlvbi5idWRnZXQgPT09ICdzdGFydHVwJykge1xuICAgICAgcmVjb21tZW5kYXRpb25zLnB1c2goe1xuICAgICAgICBpZDogJ3N0YXJ0dXAtYnVkZ2V0JyxcbiAgICAgICAgdGl0bGU6ICdDb3N0LUVmZmVjdGl2ZSBTb2x1dGlvbnMnLFxuICAgICAgICB0aXRsZUFyOiAn2K3ZhNmI2YQg2YHYudin2YTYqSDZhdmGINmG2KfYrdmK2Kkg2KfZhNiq2YPZhNmB2KknLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ0NvbnNpZGVyIG9wZW4tc291cmNlIHRvb2xzIGFuZCBzZXJ2ZXJsZXNzIGFyY2hpdGVjdHVyZScsXG4gICAgICAgIGRlc2NyaXB0aW9uQXI6ICfZgdmD2LEg2YHZiiDYp9mE2KPYr9mI2KfYqiDZhdmB2KrZiNit2Kkg2KfZhNmF2LXYr9ixINmI2KfZhNmH2YbYr9iz2Kkg2KjZhNinINiu2KfYr9mFJyxcbiAgICAgICAgaWNvbjogJ/CfkrAnLFxuICAgICAgICBwcmlvcml0eTogJ2hpZ2gnLFxuICAgICAgICBjYXRlZ29yeTogJ0J1ZGdldCcsXG4gICAgICAgIGNhdGVnb3J5QXI6ICfYp9mE2YXZitiy2KfZhtmK2KknXG4gICAgICB9KTtcbiAgICB9XG5cbiAgICAvLyDYqtmI2LXZitin2Kog2LnYp9mF2Kkg2YTZhNiw2YPYp9ihINin2YTYp9i12LfZhtin2LnZilxuICAgIGlmIChwcm9qZWN0RGVmaW5pdGlvbi5wcm9qZWN0VHlwZSA9PT0gJ2FpLW1vZGVsJyB8fCBwcm9qZWN0RGVmaW5pdGlvbi5uYW1lPy50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKCdhaScpKSB7XG4gICAgICBpZiAoIXByb2plY3REZWZpbml0aW9uLnByaW1hcnlMYW5ndWFnZXM/LmluY2x1ZGVzKCdweXRob24nKSkge1xuICAgICAgICByZWNvbW1lbmRhdGlvbnMucHVzaCh7XG4gICAgICAgICAgaWQ6ICdhaS1weXRob24nLFxuICAgICAgICAgIHRpdGxlOiAnUHl0aG9uIGZvciBBSSBEZXZlbG9wbWVudCcsXG4gICAgICAgICAgdGl0bGVBcjogJ1B5dGhvbiDZhNiq2LfZiNmK2LEg2KfZhNiw2YPYp9ihINin2YTYp9i12LfZhtin2LnZiicsXG4gICAgICAgICAgZGVzY3JpcHRpb246ICdQeXRob24gaXMgdGhlIG1vc3QgcG9wdWxhciBsYW5ndWFnZSBmb3IgQUkvTUwgcHJvamVjdHMnLFxuICAgICAgICAgIGRlc2NyaXB0aW9uQXI6ICdQeXRob24g2YfZiiDYp9mE2YTYutipINin2YTYo9mD2KvYsSDYtNi52KjZitipINmE2YXYtNin2LHZiti5INin2YTYsNmD2KfYoSDYp9mE2KfYtdi32YbYp9i52YonLFxuICAgICAgICAgIGljb246ICfwn5CNJyxcbiAgICAgICAgICBwcmlvcml0eTogJ2hpZ2gnLFxuICAgICAgICAgIGNhdGVnb3J5OiAnVGVjaG5vbG9neScsXG4gICAgICAgICAgY2F0ZWdvcnlBcjogJ9in2YTYqtmC2YbZitipJ1xuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICByZXR1cm4gcmVjb21tZW5kYXRpb25zLnNvcnQoKGEsIGIpID0+IHtcbiAgICAgIGNvbnN0IHByaW9yaXR5T3JkZXIgPSB7IGhpZ2g6IDMsIG1lZGl1bTogMiwgbG93OiAxIH07XG4gICAgICByZXR1cm4gcHJpb3JpdHlPcmRlcltiLnByaW9yaXR5XSAtIHByaW9yaXR5T3JkZXJbYS5wcmlvcml0eV07XG4gICAgfSk7XG4gIH07XG5cbiAgY29uc3QgcmVjb21tZW5kYXRpb25zID0gZ2VuZXJhdGVSZWNvbW1lbmRhdGlvbnMoKTtcblxuICBpZiAocmVjb21tZW5kYXRpb25zLmxlbmd0aCA9PT0gMCkge1xuICAgIHJldHVybiBudWxsO1xuICB9XG5cbiAgY29uc3QgZ2V0UHJpb3JpdHlDb2xvciA9IChwcmlvcml0eTogc3RyaW5nKSA9PiB7XG4gICAgc3dpdGNoIChwcmlvcml0eSkge1xuICAgICAgY2FzZSAnaGlnaCc6IHJldHVybiAnYm9yZGVyLXJlZC0yMDAgZGFyazpib3JkZXItcmVkLTgwMCBiZy1yZWQtNTAgZGFyazpiZy1yZWQtOTAwLzIwJztcbiAgICAgIGNhc2UgJ21lZGl1bSc6IHJldHVybiAnYm9yZGVyLXllbGxvdy0yMDAgZGFyazpib3JkZXIteWVsbG93LTgwMCBiZy15ZWxsb3ctNTAgZGFyazpiZy15ZWxsb3ctOTAwLzIwJztcbiAgICAgIGNhc2UgJ2xvdyc6IHJldHVybiAnYm9yZGVyLWdyZWVuLTIwMCBkYXJrOmJvcmRlci1ncmVlbi04MDAgYmctZ3JlZW4tNTAgZGFyazpiZy1ncmVlbi05MDAvMjAnO1xuICAgICAgZGVmYXVsdDogcmV0dXJuICdib3JkZXItZ3JheS0yMDAgZGFyazpib3JkZXItZ3JheS03MDAgYmctZ3JheS01MCBkYXJrOmJnLWdyYXktODAwJztcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgZ2V0UHJpb3JpdHlMYWJlbCA9IChwcmlvcml0eTogc3RyaW5nKSA9PiB7XG4gICAgY29uc3QgbGFiZWxzID0ge1xuICAgICAgaGlnaDogeyBhcjogJ9i52KfZhNmK2KknLCBlbjogJ0hpZ2gnIH0sXG4gICAgICBtZWRpdW06IHsgYXI6ICfZhdiq2YjYs9i32KknLCBlbjogJ01lZGl1bScgfSxcbiAgICAgIGxvdzogeyBhcjogJ9mF2YbYrtmB2LbYqScsIGVuOiAnTG93JyB9XG4gICAgfTtcbiAgICByZXR1cm4gbGFiZWxzW3ByaW9yaXR5IGFzIGtleW9mIHR5cGVvZiBsYWJlbHNdPy5baXNBcmFiaWMgPyAnYXInIDogJ2VuJ10gfHwgcHJpb3JpdHk7XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLWJyIGZyb20tcHVycGxlLTUwIHRvLXBpbmstMTAwIGRhcms6ZnJvbS1wdXJwbGUtOTAwLzIwIGRhcms6dG8tcGluay05MDAvMjAgcm91bmRlZC14bCBwLTYgYm9yZGVyIGJvcmRlci1wdXJwbGUtMjAwIGRhcms6Ym9yZGVyLXB1cnBsZS04MDBcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPXtgdGV4dC1jZW50ZXIgbWItNiAke2lzQXJhYmljID8gJ3RleHQtcmlnaHQnIDogJ3RleHQtbGVmdCd9YH0+XG4gICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtZ3JheS0xMDAgbWItMlwiPlxuICAgICAgICAgIHtpc0FyYWJpYyA/ICfwn46vINiq2YjYtdmK2KfYqiDYsNmD2YrYqSDZhNmF2LTYsdmI2LnZgycgOiAn8J+OryBTbWFydCBSZWNvbW1lbmRhdGlvbnMgZm9yIFlvdXIgUHJvamVjdCd9XG4gICAgICAgIDwvaDM+XG4gICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICB7aXNBcmFiaWMgXG4gICAgICAgICAgICA/ICfYp9mC2KrYsdin2K3Yp9iqINmF2K7Ytdi12Kkg2KjZhtin2KHZiyDYudmE2Ykg2K7Zitin2LHYp9iqINmF2LTYsdmI2LnZgydcbiAgICAgICAgICAgIDogJ1BlcnNvbmFsaXplZCBzdWdnZXN0aW9ucyBiYXNlZCBvbiB5b3VyIHByb2plY3Qgb3B0aW9ucydcbiAgICAgICAgICB9XG4gICAgICAgIDwvcD5cbiAgICAgIDwvZGl2PlxuXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICB7cmVjb21tZW5kYXRpb25zLm1hcCgocmVjKSA9PiAoXG4gICAgICAgICAgPGRpdlxuICAgICAgICAgICAga2V5PXtyZWMuaWR9XG4gICAgICAgICAgICBjbGFzc05hbWU9e2BwLTQgcm91bmRlZC1sZyBib3JkZXIgJHtnZXRQcmlvcml0eUNvbG9yKHJlYy5wcmlvcml0eSl9ICR7aXNBcmFiaWMgPyAndGV4dC1yaWdodCcgOiAndGV4dC1sZWZ0J31gfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBqdXN0aWZ5LWJldHdlZW4gbWItMlwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIGdhcC0yICR7aXNBcmFiaWMgPyAnZmxleC1yb3ctcmV2ZXJzZScgOiAnJ31gfT5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWxnXCI+e3JlYy5pY29ufTwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtZ3JheS0xMDBcIj5cbiAgICAgICAgICAgICAgICAgIHtpc0FyYWJpYyA/IHJlYy50aXRsZUFyIDogcmVjLnRpdGxlfVxuICAgICAgICAgICAgICAgIDwvaDQ+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgdGV4dC14cyBweC0yIHB5LTEgcm91bmRlZC1mdWxsICR7XG4gICAgICAgICAgICAgICAgICByZWMucHJpb3JpdHkgPT09ICdoaWdoJyA/ICdiZy1yZWQtMTAwIGRhcms6YmctcmVkLTkwMC8zMCB0ZXh0LXJlZC03MDAgZGFyazp0ZXh0LXJlZC0zMDAnIDpcbiAgICAgICAgICAgICAgICAgIHJlYy5wcmlvcml0eSA9PT0gJ21lZGl1bScgPyAnYmcteWVsbG93LTEwMCBkYXJrOmJnLXllbGxvdy05MDAvMzAgdGV4dC15ZWxsb3ctNzAwIGRhcms6dGV4dC15ZWxsb3ctMzAwJyA6XG4gICAgICAgICAgICAgICAgICAnYmctZ3JlZW4tMTAwIGRhcms6YmctZ3JlZW4tOTAwLzMwIHRleHQtZ3JlZW4tNzAwIGRhcms6dGV4dC1ncmVlbi0zMDAnXG4gICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAge2dldFByaW9yaXR5TGFiZWwocmVjLnByaW9yaXR5KX1cbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwIGRhcms6dGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgICAgICAge2lzQXJhYmljID8gcmVjLmNhdGVnb3J5QXIgOiByZWMuY2F0ZWdvcnl9XG4gICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgICB7aXNBcmFiaWMgPyByZWMuZGVzY3JpcHRpb25BciA6IHJlYy5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKSl9XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VDb250ZXh0U3RvcmUiLCJTbWFydFJlY29tbWVuZGF0aW9ucyIsInByb2plY3REZWZpbml0aW9uIiwidGVjaG5pY2FsTGF5ZXIiLCJjdXJyZW50TGFuZ3VhZ2UiLCJpc0FyYWJpYyIsImdlbmVyYXRlUmVjb21tZW5kYXRpb25zIiwicmVjb21tZW5kYXRpb25zIiwicHJvamVjdFR5cGUiLCJ0YXJnZXRQbGF0Zm9ybXMiLCJpbmNsdWRlcyIsInB1c2giLCJpZCIsInRpdGxlIiwidGl0bGVBciIsImRlc2NyaXB0aW9uIiwiZGVzY3JpcHRpb25BciIsImljb24iLCJwcmlvcml0eSIsImNhdGVnb3J5IiwiY2F0ZWdvcnlBciIsInByaW1hcnlMYW5ndWFnZXMiLCJzb21lIiwibGFuZyIsImFyY2hpdGVjdHVyZVBhdHRlcm4iLCJjb21wbGV4aXR5Iiwic2VjdXJpdHlSZXF1aXJlbWVudHMiLCJzY2FsaW5nU3RyYXRlZ3kiLCJ0ZWFtU2l6ZSIsIm1vbml0b3JpbmdUb29scyIsImRlcGxveW1lbnRUeXBlIiwiYnVkZ2V0IiwibmFtZSIsInRvTG93ZXJDYXNlIiwic29ydCIsImEiLCJiIiwicHJpb3JpdHlPcmRlciIsImhpZ2giLCJtZWRpdW0iLCJsb3ciLCJsZW5ndGgiLCJnZXRQcmlvcml0eUNvbG9yIiwiZ2V0UHJpb3JpdHlMYWJlbCIsImxhYmVscyIsImFyIiwiZW4iLCJkaXYiLCJjbGFzc05hbWUiLCJoMyIsInAiLCJtYXAiLCJyZWMiLCJzcGFuIiwiaDQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SmartRecommendations.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/StoreHydration.tsx":
/*!*******************************************!*\
  !*** ./src/components/StoreHydration.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StoreHydration)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction StoreHydration() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // Manually hydrate the store to prevent hydration mismatches\n        const store = _store_contextStore__WEBPACK_IMPORTED_MODULE_1__.useContextStore.getState();\n        if (false) {}\n    }, []);\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9TdG9yZUh5ZHJhdGlvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs2REFFa0M7QUFDcUI7QUFFeEMsU0FBU0U7SUFDdEJGLGdEQUFTQSxDQUFDO1FBQ1IsNkRBQTZEO1FBQzdELE1BQU1HLFFBQVFGLGdFQUFlQSxDQUFDRyxRQUFRO1FBQ3RDLElBQUksS0FBa0IsRUFBYSxFQUVsQztJQUNILEdBQUcsRUFBRTtJQUVMLE9BQU87QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbnRleHRraXQvLi9zcmMvY29tcG9uZW50cy9TdG9yZUh5ZHJhdGlvbi50c3g/YTI5NyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZUNvbnRleHRTdG9yZSB9IGZyb20gJ0Avc3RvcmUvY29udGV4dFN0b3JlJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gU3RvcmVIeWRyYXRpb24oKSB7XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8gTWFudWFsbHkgaHlkcmF0ZSB0aGUgc3RvcmUgdG8gcHJldmVudCBoeWRyYXRpb24gbWlzbWF0Y2hlc1xuICAgIGNvbnN0IHN0b3JlID0gdXNlQ29udGV4dFN0b3JlLmdldFN0YXRlKCk7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICB1c2VDb250ZXh0U3RvcmUucGVyc2lzdC5yZWh5ZHJhdGUoKTtcbiAgICB9XG4gIH0sIFtdKTtcblxuICByZXR1cm4gbnVsbDtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VDb250ZXh0U3RvcmUiLCJTdG9yZUh5ZHJhdGlvbiIsInN0b3JlIiwiZ2V0U3RhdGUiLCJwZXJzaXN0IiwicmVoeWRyYXRlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/StoreHydration.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ThemeProvider.tsx":
/*!******************************************!*\
  !*** ./src/components/ThemeProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme auto */ \n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ThemeProvider({ children }) {\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"light\");\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize theme from localStorage only, ignore system preference after first load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedTheme = localStorage.getItem(\"contextkit-theme\");\n        if (savedTheme && (savedTheme === \"light\" || savedTheme === \"dark\")) {\n            // إذا كان هناك theme محفوظ، استخدمه\n            setTheme(savedTheme);\n        } else {\n            // فقط في المرة الأولى، استخدم system preference\n            const systemTheme = window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\";\n            setTheme(systemTheme);\n            localStorage.setItem(\"contextkit-theme\", systemTheme);\n        }\n        setMounted(true);\n    }, []);\n    // Apply theme to document immediately\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (mounted) {\n            const root = document.documentElement;\n            // إزالة جميع classes المتعلقة بالـ theme أولاً\n            root.classList.remove(\"light\", \"dark\");\n            // إضافة الـ class الصحيح\n            root.classList.add(theme);\n            // حفظ في localStorage مع مفتاح مخصص\n            localStorage.setItem(\"contextkit-theme\", theme);\n            // تطبيق فوري على body أيضاً\n            document.body.setAttribute(\"data-theme\", theme);\n        }\n    }, [\n        theme,\n        mounted\n    ]);\n    const toggleTheme = ()=>{\n        const newTheme = theme === \"light\" ? \"dark\" : \"light\";\n        setTheme(newTheme);\n        // حفظ فوري في localStorage\n        localStorage.setItem(\"contextkit-theme\", newTheme);\n    };\n    // Prevent hydration mismatch\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: {\n            theme,\n            toggleTheme\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            suppressHydrationWarning: true,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeProvider.tsx\",\n            lineNumber: 69,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeProvider.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        // Return default values instead of throwing error during SSR\n        return {\n            theme: \"light\",\n            toggleTheme: ()=>{}\n        };\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9UaGVtZVByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRXVFO0FBU3ZFLE1BQU1JLDZCQUFlSixvREFBYUEsQ0FBK0JLO0FBRTFELFNBQVNDLGNBQWMsRUFBRUMsUUFBUSxFQUFpQztJQUN2RSxNQUFNLENBQUNDLE9BQU9DLFNBQVMsR0FBR04sK0NBQVFBLENBQVE7SUFDMUMsTUFBTSxDQUFDTyxTQUFTQyxXQUFXLEdBQUdSLCtDQUFRQSxDQUFDO0lBRXZDLHFGQUFxRjtJQUNyRkQsZ0RBQVNBLENBQUM7UUFDUixNQUFNVSxhQUFhQyxhQUFhQyxPQUFPLENBQUM7UUFFeEMsSUFBSUYsY0FBZUEsQ0FBQUEsZUFBZSxXQUFXQSxlQUFlLE1BQUssR0FBSTtZQUNuRSxvQ0FBb0M7WUFDcENILFNBQVNHO1FBQ1gsT0FBTztZQUNMLGdEQUFnRDtZQUNoRCxNQUFNRyxjQUFjQyxPQUFPQyxVQUFVLENBQUMsZ0NBQWdDQyxPQUFPLEdBQUcsU0FBUztZQUN6RlQsU0FBU007WUFDVEYsYUFBYU0sT0FBTyxDQUFDLG9CQUFvQko7UUFDM0M7UUFFQUosV0FBVztJQUNiLEdBQUcsRUFBRTtJQUVMLHNDQUFzQztJQUN0Q1QsZ0RBQVNBLENBQUM7UUFDUixJQUFJUSxTQUFTO1lBQ1gsTUFBTVUsT0FBT0MsU0FBU0MsZUFBZTtZQUVyQywrQ0FBK0M7WUFDL0NGLEtBQUtHLFNBQVMsQ0FBQ0MsTUFBTSxDQUFDLFNBQVM7WUFFL0IseUJBQXlCO1lBQ3pCSixLQUFLRyxTQUFTLENBQUNFLEdBQUcsQ0FBQ2pCO1lBRW5CLG9DQUFvQztZQUNwQ0ssYUFBYU0sT0FBTyxDQUFDLG9CQUFvQlg7WUFFekMsNEJBQTRCO1lBQzVCYSxTQUFTSyxJQUFJLENBQUNDLFlBQVksQ0FBQyxjQUFjbkI7UUFDM0M7SUFDRixHQUFHO1FBQUNBO1FBQU9FO0tBQVE7SUFFbkIsTUFBTWtCLGNBQWM7UUFDbEIsTUFBTUMsV0FBV3JCLFVBQVUsVUFBVSxTQUFTO1FBQzlDQyxTQUFTb0I7UUFFVCwyQkFBMkI7UUFDM0JoQixhQUFhTSxPQUFPLENBQUMsb0JBQW9CVTtJQUMzQztJQUVBLDZCQUE2QjtJQUM3QixJQUFJLENBQUNuQixTQUFTO1FBQ1oscUJBQU87c0JBQUdIOztJQUNaO0lBRUEscUJBQ0UsOERBQUNILGFBQWEwQixRQUFRO1FBQUNDLE9BQU87WUFBRXZCO1lBQU9vQjtRQUFZO2tCQUNqRCw0RUFBQ0k7WUFBSUMsd0JBQXdCO3NCQUMxQjFCOzs7Ozs7Ozs7OztBQUlUO0FBRU8sU0FBUzJCO0lBQ2QsTUFBTUMsVUFBVWxDLGlEQUFVQSxDQUFDRztJQUMzQixJQUFJK0IsWUFBWTlCLFdBQVc7UUFDekIsNkRBQTZEO1FBQzdELE9BQU87WUFDTEcsT0FBTztZQUNQb0IsYUFBYSxLQUFPO1FBQ3RCO0lBQ0Y7SUFDQSxPQUFPTztBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29udGV4dGtpdC8uL3NyYy9jb21wb25lbnRzL1RoZW1lUHJvdmlkZXIudHN4PzZmNDciXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuXG50eXBlIFRoZW1lID0gJ2xpZ2h0JyB8ICdkYXJrJztcblxuaW50ZXJmYWNlIFRoZW1lQ29udGV4dFR5cGUge1xuICB0aGVtZTogVGhlbWU7XG4gIHRvZ2dsZVRoZW1lOiAoKSA9PiB2b2lkO1xufVxuXG5jb25zdCBUaGVtZUNvbnRleHQgPSBjcmVhdGVDb250ZXh0PFRoZW1lQ29udGV4dFR5cGUgfCB1bmRlZmluZWQ+KHVuZGVmaW5lZCk7XG5cbmV4cG9ydCBmdW5jdGlvbiBUaGVtZVByb3ZpZGVyKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlIH0pIHtcbiAgY29uc3QgW3RoZW1lLCBzZXRUaGVtZV0gPSB1c2VTdGF0ZTxUaGVtZT4oJ2xpZ2h0Jyk7XG4gIGNvbnN0IFttb3VudGVkLCBzZXRNb3VudGVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICAvLyBJbml0aWFsaXplIHRoZW1lIGZyb20gbG9jYWxTdG9yYWdlIG9ubHksIGlnbm9yZSBzeXN0ZW0gcHJlZmVyZW5jZSBhZnRlciBmaXJzdCBsb2FkXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3Qgc2F2ZWRUaGVtZSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdjb250ZXh0a2l0LXRoZW1lJykgYXMgVGhlbWU7XG5cbiAgICBpZiAoc2F2ZWRUaGVtZSAmJiAoc2F2ZWRUaGVtZSA9PT0gJ2xpZ2h0JyB8fCBzYXZlZFRoZW1lID09PSAnZGFyaycpKSB7XG4gICAgICAvLyDYpdiw2Kcg2YPYp9mGINmH2YbYp9mDIHRoZW1lINmF2K3ZgdmI2LjYjCDYp9iz2KrYrtiv2YXZh1xuICAgICAgc2V0VGhlbWUoc2F2ZWRUaGVtZSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIC8vINmB2YLYtyDZgdmKINin2YTZhdix2Kkg2KfZhNij2YjZhNmJ2Iwg2KfYs9iq2K7Yr9mFIHN5c3RlbSBwcmVmZXJlbmNlXG4gICAgICBjb25zdCBzeXN0ZW1UaGVtZSA9IHdpbmRvdy5tYXRjaE1lZGlhKCcocHJlZmVycy1jb2xvci1zY2hlbWU6IGRhcmspJykubWF0Y2hlcyA/ICdkYXJrJyA6ICdsaWdodCc7XG4gICAgICBzZXRUaGVtZShzeXN0ZW1UaGVtZSk7XG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnY29udGV4dGtpdC10aGVtZScsIHN5c3RlbVRoZW1lKTtcbiAgICB9XG5cbiAgICBzZXRNb3VudGVkKHRydWUpO1xuICB9LCBbXSk7XG5cbiAgLy8gQXBwbHkgdGhlbWUgdG8gZG9jdW1lbnQgaW1tZWRpYXRlbHlcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAobW91bnRlZCkge1xuICAgICAgY29uc3Qgcm9vdCA9IGRvY3VtZW50LmRvY3VtZW50RWxlbWVudDtcblxuICAgICAgLy8g2KXYstin2YTYqSDYrNmF2YrYuSBjbGFzc2VzINin2YTZhdiq2LnZhNmC2Kkg2KjYp9mE2YAgdGhlbWUg2KPZiNmE2KfZi1xuICAgICAgcm9vdC5jbGFzc0xpc3QucmVtb3ZlKCdsaWdodCcsICdkYXJrJyk7XG5cbiAgICAgIC8vINil2LbYp9mB2Kkg2KfZhNmAIGNsYXNzINin2YTYtdit2YrYrVxuICAgICAgcm9vdC5jbGFzc0xpc3QuYWRkKHRoZW1lKTtcblxuICAgICAgLy8g2K3Zgdi4INmB2YogbG9jYWxTdG9yYWdlINmF2Lkg2YXZgdiq2KfYrSDZhdiu2LXYtVxuICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2NvbnRleHRraXQtdGhlbWUnLCB0aGVtZSk7XG5cbiAgICAgIC8vINiq2LfYqNmK2YIg2YHZiNix2Yog2LnZhNmJIGJvZHkg2KPZiti22KfZi1xuICAgICAgZG9jdW1lbnQuYm9keS5zZXRBdHRyaWJ1dGUoJ2RhdGEtdGhlbWUnLCB0aGVtZSk7XG4gICAgfVxuICB9LCBbdGhlbWUsIG1vdW50ZWRdKTtcblxuICBjb25zdCB0b2dnbGVUaGVtZSA9ICgpID0+IHtcbiAgICBjb25zdCBuZXdUaGVtZSA9IHRoZW1lID09PSAnbGlnaHQnID8gJ2RhcmsnIDogJ2xpZ2h0JztcbiAgICBzZXRUaGVtZShuZXdUaGVtZSk7XG5cbiAgICAvLyDYrdmB2Lgg2YHZiNix2Yog2YHZiiBsb2NhbFN0b3JhZ2VcbiAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnY29udGV4dGtpdC10aGVtZScsIG5ld1RoZW1lKTtcbiAgfTtcblxuICAvLyBQcmV2ZW50IGh5ZHJhdGlvbiBtaXNtYXRjaFxuICBpZiAoIW1vdW50ZWQpIHtcbiAgICByZXR1cm4gPD57Y2hpbGRyZW59PC8+O1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8VGhlbWVDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXt7IHRoZW1lLCB0b2dnbGVUaGVtZSB9fT5cbiAgICAgIDxkaXYgc3VwcHJlc3NIeWRyYXRpb25XYXJuaW5nPlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L2Rpdj5cbiAgICA8L1RoZW1lQ29udGV4dC5Qcm92aWRlcj5cbiAgKTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIHVzZVRoZW1lKCkge1xuICBjb25zdCBjb250ZXh0ID0gdXNlQ29udGV4dChUaGVtZUNvbnRleHQpO1xuICBpZiAoY29udGV4dCA9PT0gdW5kZWZpbmVkKSB7XG4gICAgLy8gUmV0dXJuIGRlZmF1bHQgdmFsdWVzIGluc3RlYWQgb2YgdGhyb3dpbmcgZXJyb3IgZHVyaW5nIFNTUlxuICAgIHJldHVybiB7XG4gICAgICB0aGVtZTogJ2xpZ2h0JyBhcyBUaGVtZSxcbiAgICAgIHRvZ2dsZVRoZW1lOiAoKSA9PiB7fVxuICAgIH07XG4gIH1cbiAgcmV0dXJuIGNvbnRleHQ7XG59XG4iXSwibmFtZXMiOlsiY3JlYXRlQ29udGV4dCIsInVzZUNvbnRleHQiLCJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsIlRoZW1lQ29udGV4dCIsInVuZGVmaW5lZCIsIlRoZW1lUHJvdmlkZXIiLCJjaGlsZHJlbiIsInRoZW1lIiwic2V0VGhlbWUiLCJtb3VudGVkIiwic2V0TW91bnRlZCIsInNhdmVkVGhlbWUiLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwic3lzdGVtVGhlbWUiLCJ3aW5kb3ciLCJtYXRjaE1lZGlhIiwibWF0Y2hlcyIsInNldEl0ZW0iLCJyb290IiwiZG9jdW1lbnQiLCJkb2N1bWVudEVsZW1lbnQiLCJjbGFzc0xpc3QiLCJyZW1vdmUiLCJhZGQiLCJib2R5Iiwic2V0QXR0cmlidXRlIiwidG9nZ2xlVGhlbWUiLCJuZXdUaGVtZSIsIlByb3ZpZGVyIiwidmFsdWUiLCJkaXYiLCJzdXBwcmVzc0h5ZHJhdGlvbldhcm5pbmciLCJ1c2VUaGVtZSIsImNvbnRleHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ThemeProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ThemeToggle.tsx":
/*!****************************************!*\
  !*** ./src/components/ThemeToggle.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ThemeToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _ThemeProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ThemeProvider */ \"(ssr)/./src/components/ThemeProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ThemeToggle() {\n    const { theme, toggleTheme } = (0,_ThemeProvider__WEBPACK_IMPORTED_MODULE_1__.useTheme)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: toggleTheme,\n        className: \"relative w-12 h-12 rounded-xl bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-900 hover:from-blue-100 hover:to-indigo-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden\",\n        \"aria-label\": `Switch to ${theme === \"light\" ? \"dark\" : \"light\"} mode`,\n        title: `Switch to ${theme === \"light\" ? \"dark\" : \"light\"} mode`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: `w-6 h-6 text-amber-500 transition-all duration-500 ease-in-out ${theme === \"light\" ? \"opacity-100 rotate-0 scale-100\" : \"opacity-0 rotate-180 scale-0\"}`,\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: `w-6 h-6 text-blue-400 absolute transition-all duration-500 ease-in-out ${theme === \"dark\" ? \"opacity-100 rotate-0 scale-100\" : \"opacity-0 -rotate-180 scale-0\"}`,\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ThemeToggle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/projectOptions.ts":
/*!***********************************!*\
  !*** ./src/lib/projectOptions.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BUDGET_RANGES: () => (/* binding */ BUDGET_RANGES),\n/* harmony export */   COMPLEXITY_LEVELS: () => (/* binding */ COMPLEXITY_LEVELS),\n/* harmony export */   DEPLOYMENT_TYPES: () => (/* binding */ DEPLOYMENT_TYPES),\n/* harmony export */   GEOGRAPHIC_REGIONS: () => (/* binding */ GEOGRAPHIC_REGIONS),\n/* harmony export */   PROGRAMMING_LANGUAGES: () => (/* binding */ PROGRAMMING_LANGUAGES),\n/* harmony export */   PROJECT_NATURE: () => (/* binding */ PROJECT_NATURE),\n/* harmony export */   PROJECT_TYPES: () => (/* binding */ PROJECT_TYPES),\n/* harmony export */   TARGET_PLATFORMS: () => (/* binding */ TARGET_PLATFORMS),\n/* harmony export */   TEAM_SIZES: () => (/* binding */ TEAM_SIZES)\n/* harmony export */ });\n// خيارات التخصيص المتقدمة للمشاريع\n// أنواع المشاريع\nconst PROJECT_TYPES = [\n    {\n        id: \"web-app\",\n        label: \"Web Application\",\n        labelAr: \"تطبيق ويب\",\n        description: \"Browser-based application accessible via web browsers\",\n        descriptionAr: \"تطبيق يعمل في المتصفح ويمكن الوصول إليه عبر متصفحات الويب\",\n        icon: \"\\uD83C\\uDF10\"\n    },\n    {\n        id: \"mobile-app\",\n        label: \"Mobile Application\",\n        labelAr: \"تطبيق جوال\",\n        description: \"Native or cross-platform mobile application\",\n        descriptionAr: \"تطبيق جوال أصلي أو متعدد المنصات\",\n        icon: \"\\uD83D\\uDCF1\"\n    },\n    {\n        id: \"desktop-app\",\n        label: \"Desktop Application\",\n        labelAr: \"تطبيق سطح المكتب\",\n        description: \"Native desktop application for Windows, macOS, or Linux\",\n        descriptionAr: \"تطبيق سطح مكتب أصلي لـ Windows أو macOS أو Linux\",\n        icon: \"\\uD83D\\uDCBB\"\n    },\n    {\n        id: \"api-service\",\n        label: \"API/Microservice\",\n        labelAr: \"خدمة API/مايكروسيرفس\",\n        description: \"Backend API or microservice architecture\",\n        descriptionAr: \"خدمة API خلفية أو هندسة مايكروسيرفس\",\n        icon: \"\\uD83D\\uDD0C\"\n    },\n    {\n        id: \"ai-model\",\n        label: \"AI/ML Model\",\n        labelAr: \"نموذج ذكاء اصطناعي\",\n        description: \"Machine learning model or AI system\",\n        descriptionAr: \"نموذج تعلم آلي أو نظام ذكاء اصطناعي\",\n        icon: \"\\uD83E\\uDD16\"\n    },\n    {\n        id: \"chatbot\",\n        label: \"Chatbot/Virtual Assistant\",\n        labelAr: \"شات بوت/مساعد افتراضي\",\n        description: \"Conversational AI or chatbot system\",\n        descriptionAr: \"نظام ذكاء اصطناعي محادثة أو شات بوت\",\n        icon: \"\\uD83D\\uDCAC\"\n    },\n    {\n        id: \"data-analytics\",\n        label: \"Data Analytics Platform\",\n        labelAr: \"منصة تحليل البيانات\",\n        description: \"Data processing and analytics solution\",\n        descriptionAr: \"حل معالجة وتحليل البيانات\",\n        icon: \"\\uD83D\\uDCCA\"\n    },\n    {\n        id: \"iot-system\",\n        label: \"IoT System\",\n        labelAr: \"نظام إنترنت الأشياء\",\n        description: \"Internet of Things connected system\",\n        descriptionAr: \"نظام متصل بإنترنت الأشياء\",\n        icon: \"\\uD83C\\uDF10\"\n    }\n];\n// المنصات المستهدفة\nconst TARGET_PLATFORMS = [\n    {\n        id: \"web-browsers\",\n        label: \"Web Browsers\",\n        labelAr: \"متصفحات الويب\",\n        description: \"Chrome, Firefox, Safari, Edge\",\n        descriptionAr: \"كروم، فايرفوكس، سفاري، إيدج\",\n        icon: \"\\uD83C\\uDF10\"\n    },\n    {\n        id: \"android\",\n        label: \"Android\",\n        labelAr: \"أندرويد\",\n        description: \"Android mobile devices\",\n        descriptionAr: \"أجهزة أندرويد المحمولة\",\n        icon: \"\\uD83E\\uDD16\"\n    },\n    {\n        id: \"ios\",\n        label: \"iOS\",\n        labelAr: \"iOS\",\n        description: \"iPhone and iPad devices\",\n        descriptionAr: \"أجهزة آيفون وآيباد\",\n        icon: \"\\uD83C\\uDF4E\"\n    },\n    {\n        id: \"windows\",\n        label: \"Windows\",\n        labelAr: \"ويندوز\",\n        description: \"Windows desktop and server\",\n        descriptionAr: \"سطح مكتب وخادم ويندوز\",\n        icon: \"\\uD83E\\uDE9F\"\n    },\n    {\n        id: \"macos\",\n        label: \"macOS\",\n        labelAr: \"macOS\",\n        description: \"Apple macOS desktop\",\n        descriptionAr: \"سطح مكتب آبل macOS\",\n        icon: \"\\uD83C\\uDF4E\"\n    },\n    {\n        id: \"linux\",\n        label: \"Linux\",\n        labelAr: \"لينكس\",\n        description: \"Linux distributions\",\n        descriptionAr: \"توزيعات لينكس\",\n        icon: \"\\uD83D\\uDC27\"\n    },\n    {\n        id: \"cloud\",\n        label: \"Cloud Platforms\",\n        labelAr: \"المنصات السحابية\",\n        description: \"AWS, Azure, Google Cloud\",\n        descriptionAr: \"AWS، Azure، Google Cloud\",\n        icon: \"☁️\"\n    },\n    {\n        id: \"embedded\",\n        label: \"Embedded Systems\",\n        labelAr: \"الأنظمة المدمجة\",\n        description: \"IoT devices, microcontrollers\",\n        descriptionAr: \"أجهزة إنترنت الأشياء، المتحكمات الدقيقة\",\n        icon: \"\\uD83D\\uDD27\"\n    }\n];\n// لغات البرمجة الأساسية\nconst PROGRAMMING_LANGUAGES = [\n    {\n        id: \"javascript\",\n        label: \"JavaScript/TypeScript\",\n        labelAr: \"جافا سكريبت/تايب سكريبت\",\n        description: \"Modern web development\",\n        descriptionAr: \"تطوير الويب الحديث\",\n        icon: \"\\uD83D\\uDFE8\"\n    },\n    {\n        id: \"python\",\n        label: \"Python\",\n        labelAr: \"بايثون\",\n        description: \"AI/ML, backend, data science\",\n        descriptionAr: \"ذكاء اصطناعي، خلفية، علوم البيانات\",\n        icon: \"\\uD83D\\uDC0D\"\n    },\n    {\n        id: \"java\",\n        label: \"Java\",\n        labelAr: \"جافا\",\n        description: \"Enterprise applications, Android\",\n        descriptionAr: \"تطبيقات المؤسسات، أندرويد\",\n        icon: \"☕\"\n    },\n    {\n        id: \"csharp\",\n        label: \"C#\",\n        labelAr: \"سي شارب\",\n        description: \".NET ecosystem, Windows apps\",\n        descriptionAr: \"نظام .NET، تطبيقات ويندوز\",\n        icon: \"\\uD83D\\uDD37\"\n    },\n    {\n        id: \"swift\",\n        label: \"Swift\",\n        labelAr: \"سويفت\",\n        description: \"iOS and macOS development\",\n        descriptionAr: \"تطوير iOS و macOS\",\n        icon: \"\\uD83E\\uDD89\"\n    },\n    {\n        id: \"kotlin\",\n        label: \"Kotlin\",\n        labelAr: \"كوتلن\",\n        description: \"Android development, JVM\",\n        descriptionAr: \"تطوير أندرويد، JVM\",\n        icon: \"\\uD83D\\uDFE3\"\n    },\n    {\n        id: \"rust\",\n        label: \"Rust\",\n        labelAr: \"رست\",\n        description: \"System programming, performance\",\n        descriptionAr: \"برمجة الأنظمة، الأداء\",\n        icon: \"\\uD83E\\uDD80\"\n    },\n    {\n        id: \"go\",\n        label: \"Go\",\n        labelAr: \"جو\",\n        description: \"Backend services, microservices\",\n        descriptionAr: \"خدمات خلفية، مايكروسيرفس\",\n        icon: \"\\uD83D\\uDC39\"\n    },\n    {\n        id: \"cpp\",\n        label: \"C++\",\n        labelAr: \"سي++\",\n        description: \"High-performance applications\",\n        descriptionAr: \"تطبيقات عالية الأداء\",\n        icon: \"⚡\"\n    },\n    {\n        id: \"php\",\n        label: \"PHP\",\n        labelAr: \"PHP\",\n        description: \"Web development, server-side\",\n        descriptionAr: \"تطوير الويب، جانب الخادم\",\n        icon: \"\\uD83D\\uDC18\"\n    },\n    {\n        id: \"ruby\",\n        label: \"Ruby\",\n        labelAr: \"روبي\",\n        description: \"Web applications, rapid development\",\n        descriptionAr: \"تطبيقات الويب، التطوير السريع\",\n        icon: \"\\uD83D\\uDC8E\"\n    },\n    {\n        id: \"dart\",\n        label: \"Dart/Flutter\",\n        labelAr: \"دارت/فلاتر\",\n        description: \"Cross-platform mobile apps\",\n        descriptionAr: \"تطبيقات جوال متعددة المنصات\",\n        icon: \"\\uD83C\\uDFAF\"\n    }\n];\n// مستويات التعقيد\nconst COMPLEXITY_LEVELS = [\n    {\n        id: \"simple\",\n        label: \"Simple\",\n        labelAr: \"بسيط\",\n        description: \"Basic functionality, minimal features\",\n        descriptionAr: \"وظائف أساسية، ميزات قليلة\",\n        icon: \"\\uD83D\\uDFE2\"\n    },\n    {\n        id: \"moderate\",\n        label: \"Moderate\",\n        labelAr: \"متوسط\",\n        description: \"Standard features, some integrations\",\n        descriptionAr: \"ميزات قياسية، بعض التكاملات\",\n        icon: \"\\uD83D\\uDFE1\"\n    },\n    {\n        id: \"complex\",\n        label: \"Complex\",\n        labelAr: \"معقد\",\n        description: \"Advanced features, multiple integrations\",\n        descriptionAr: \"ميزات متقدمة، تكاملات متعددة\",\n        icon: \"\\uD83D\\uDFE0\"\n    },\n    {\n        id: \"enterprise\",\n        label: \"Enterprise\",\n        labelAr: \"مؤسسي\",\n        description: \"Large-scale, high availability, security\",\n        descriptionAr: \"واسع النطاق، توفر عالي، أمان\",\n        icon: \"\\uD83D\\uDD34\"\n    }\n];\n// نطاقات الميزانية\nconst BUDGET_RANGES = [\n    {\n        id: \"startup\",\n        label: \"Startup Budget\",\n        labelAr: \"ميزانية ناشئة\",\n        description: \"Under $10K\",\n        descriptionAr: \"أقل من 10 آلاف دولار\",\n        icon: \"\\uD83D\\uDCB0\"\n    },\n    {\n        id: \"small\",\n        label: \"Small Project\",\n        labelAr: \"مشروع صغير\",\n        description: \"$10K - $50K\",\n        descriptionAr: \"10-50 ألف دولار\",\n        icon: \"\\uD83D\\uDCB5\"\n    },\n    {\n        id: \"medium\",\n        label: \"Medium Project\",\n        labelAr: \"مشروع متوسط\",\n        description: \"$50K - $200K\",\n        descriptionAr: \"50-200 ألف دولار\",\n        icon: \"\\uD83D\\uDCB8\"\n    },\n    {\n        id: \"large\",\n        label: \"Large Project\",\n        labelAr: \"مشروع كبير\",\n        description: \"$200K - $1M\",\n        descriptionAr: \"200 ألف - مليون دولار\",\n        icon: \"\\uD83D\\uDC8E\"\n    },\n    {\n        id: \"enterprise\",\n        label: \"Enterprise\",\n        labelAr: \"مؤسسي\",\n        description: \"$1M+\",\n        descriptionAr: \"أكثر من مليون دولار\",\n        icon: \"\\uD83C\\uDFE6\"\n    }\n];\n// أحجام الفريق\nconst TEAM_SIZES = [\n    {\n        id: \"solo\",\n        label: \"Solo Developer\",\n        labelAr: \"مطور منفرد\",\n        description: \"1 person\",\n        descriptionAr: \"شخص واحد\",\n        icon: \"\\uD83D\\uDC64\"\n    },\n    {\n        id: \"small\",\n        label: \"Small Team\",\n        labelAr: \"فريق صغير\",\n        description: \"2-5 people\",\n        descriptionAr: \"2-5 أشخاص\",\n        icon: \"\\uD83D\\uDC65\"\n    },\n    {\n        id: \"medium\",\n        label: \"Medium Team\",\n        labelAr: \"فريق متوسط\",\n        description: \"6-15 people\",\n        descriptionAr: \"6-15 شخص\",\n        icon: \"\\uD83D\\uDC68‍\\uD83D\\uDC69‍\\uD83D\\uDC67‍\\uD83D\\uDC66\"\n    },\n    {\n        id: \"large\",\n        label: \"Large Team\",\n        labelAr: \"فريق كبير\",\n        description: \"16-50 people\",\n        descriptionAr: \"16-50 شخص\",\n        icon: \"\\uD83C\\uDFE2\"\n    },\n    {\n        id: \"enterprise\",\n        label: \"Enterprise Team\",\n        labelAr: \"فريق مؤسسي\",\n        description: \"50+ people\",\n        descriptionAr: \"أكثر من 50 شخص\",\n        icon: \"\\uD83C\\uDFED\"\n    }\n];\n// أنواع النشر\nconst DEPLOYMENT_TYPES = [\n    {\n        id: \"cloud\",\n        label: \"Cloud Deployment\",\n        labelAr: \"نشر سحابي\",\n        description: \"AWS, Azure, Google Cloud, Vercel\",\n        descriptionAr: \"AWS، Azure، Google Cloud، Vercel\",\n        icon: \"☁️\"\n    },\n    {\n        id: \"on-premise\",\n        label: \"On-Premise\",\n        labelAr: \"محلي\",\n        description: \"Self-hosted infrastructure\",\n        descriptionAr: \"بنية تحتية ذاتية الاستضافة\",\n        icon: \"\\uD83C\\uDFE2\"\n    },\n    {\n        id: \"hybrid\",\n        label: \"Hybrid\",\n        labelAr: \"هجين\",\n        description: \"Mix of cloud and on-premise\",\n        descriptionAr: \"مزيج من السحابي والمحلي\",\n        icon: \"\\uD83D\\uDD04\"\n    },\n    {\n        id: \"edge\",\n        label: \"Edge Computing\",\n        labelAr: \"حوسبة الحافة\",\n        description: \"Distributed edge deployment\",\n        descriptionAr: \"نشر موزع على الحافة\",\n        icon: \"\\uD83C\\uDF10\"\n    },\n    {\n        id: \"mobile-stores\",\n        label: \"App Stores\",\n        labelAr: \"متاجر التطبيقات\",\n        description: \"Google Play, App Store\",\n        descriptionAr: \"جوجل بلاي، آب ستور\",\n        icon: \"\\uD83D\\uDCF1\"\n    }\n];\n// المناطق الجغرافية والدول\nconst GEOGRAPHIC_REGIONS = [\n    {\n        id: \"morocco\",\n        label: \"Morocco\",\n        labelAr: \"المغرب\",\n        description: \"Kingdom of Morocco - North Africa\",\n        descriptionAr: \"المملكة المغربية - شمال أفريقيا\",\n        icon: \"\\uD83C\\uDDF2\\uD83C\\uDDE6\"\n    },\n    {\n        id: \"middle-east\",\n        label: \"Middle East\",\n        labelAr: \"الشرق الأوسط\",\n        description: \"Middle Eastern countries and regions\",\n        descriptionAr: \"دول ومناطق الشرق الأوسط\",\n        icon: \"\\uD83D\\uDD4C\"\n    },\n    {\n        id: \"north-africa\",\n        label: \"North Africa\",\n        labelAr: \"شمال أفريقيا\",\n        description: \"Northern African countries\",\n        descriptionAr: \"دول شمال أفريقيا\",\n        icon: \"\\uD83C\\uDFDC️\"\n    },\n    {\n        id: \"africa\",\n        label: \"Africa\",\n        labelAr: \"أفريقيا\",\n        description: \"African continent\",\n        descriptionAr: \"القارة الأفريقية\",\n        icon: \"\\uD83C\\uDF0D\"\n    },\n    {\n        id: \"arab-world\",\n        label: \"Arab World\",\n        labelAr: \"العالم العربي\",\n        description: \"Arabic-speaking countries and regions\",\n        descriptionAr: \"الدول والمناطق الناطقة بالعربية\",\n        icon: \"\\uD83C\\uDF19\"\n    },\n    {\n        id: \"europe\",\n        label: \"Europe\",\n        labelAr: \"أوروبا\",\n        description: \"European countries\",\n        descriptionAr: \"الدول الأوروبية\",\n        icon: \"\\uD83C\\uDDEA\\uD83C\\uDDFA\"\n    },\n    {\n        id: \"north-america\",\n        label: \"North America\",\n        labelAr: \"أمريكا الشمالية\",\n        description: \"United States, Canada, and Mexico\",\n        descriptionAr: \"الولايات المتحدة وكندا والمكسيك\",\n        icon: \"\\uD83C\\uDF0E\"\n    },\n    {\n        id: \"asia\",\n        label: \"Asia\",\n        labelAr: \"آسيا\",\n        description: \"Asian countries and regions\",\n        descriptionAr: \"الدول والمناطق الآسيوية\",\n        icon: \"\\uD83C\\uDF0F\"\n    },\n    {\n        id: \"global\",\n        label: \"Global/Worldwide\",\n        labelAr: \"عالمي/في جميع أنحاء العالم\",\n        description: \"Worldwide coverage and availability\",\n        descriptionAr: \"تغطية وتوفر عالمي\",\n        icon: \"\\uD83C\\uDF10\"\n    }\n];\n// طبيعة المشروع ونوع الأعمال\nconst PROJECT_NATURE = [\n    {\n        id: \"graduation-clothing\",\n        label: \"Graduation Clothing\",\n        labelAr: \"لباس التخرج\",\n        description: \"Academic graduation gowns, caps, and accessories\",\n        descriptionAr: \"أثواب التخرج الأكاديمية والقبعات والإكسسوارات\",\n        icon: \"\\uD83C\\uDF93\"\n    },\n    {\n        id: \"fashion-retail\",\n        label: \"Fashion & Retail\",\n        labelAr: \"الأزياء والتجارة\",\n        description: \"Clothing, fashion, and retail business\",\n        descriptionAr: \"الملابس والأزياء وأعمال التجارة\",\n        icon: \"\\uD83D\\uDC57\"\n    },\n    {\n        id: \"education\",\n        label: \"Education\",\n        labelAr: \"التعليم\",\n        description: \"Educational services and platforms\",\n        descriptionAr: \"الخدمات والمنصات التعليمية\",\n        icon: \"\\uD83D\\uDCDA\"\n    },\n    {\n        id: \"healthcare\",\n        label: \"Healthcare\",\n        labelAr: \"الرعاية الصحية\",\n        description: \"Medical and healthcare services\",\n        descriptionAr: \"الخدمات الطبية والرعاية الصحية\",\n        icon: \"\\uD83C\\uDFE5\"\n    },\n    {\n        id: \"fintech\",\n        label: \"Financial Technology\",\n        labelAr: \"التكنولوجيا المالية\",\n        description: \"Banking, payments, and financial services\",\n        descriptionAr: \"الخدمات المصرفية والمدفوعات والخدمات المالية\",\n        icon: \"\\uD83D\\uDCB3\"\n    },\n    {\n        id: \"ecommerce\",\n        label: \"E-commerce\",\n        labelAr: \"التجارة الإلكترونية\",\n        description: \"Online shopping and marketplace platforms\",\n        descriptionAr: \"منصات التسوق الإلكتروني والأسواق\",\n        icon: \"\\uD83D\\uDED2\"\n    },\n    {\n        id: \"food-beverage\",\n        label: \"Food & Beverage\",\n        labelAr: \"الطعام والشراب\",\n        description: \"Restaurant, food delivery, and culinary services\",\n        descriptionAr: \"المطاعم وتوصيل الطعام والخدمات الطهوية\",\n        icon: \"\\uD83C\\uDF7D️\"\n    },\n    {\n        id: \"travel-tourism\",\n        label: \"Travel & Tourism\",\n        labelAr: \"السفر والسياحة\",\n        description: \"Travel booking, tourism, and hospitality\",\n        descriptionAr: \"حجز السفر والسياحة والضيافة\",\n        icon: \"✈️\"\n    },\n    {\n        id: \"real-estate\",\n        label: \"Real Estate\",\n        labelAr: \"العقارات\",\n        description: \"Property management and real estate services\",\n        descriptionAr: \"إدارة الممتلكات وخدمات العقارات\",\n        icon: \"\\uD83C\\uDFE0\"\n    },\n    {\n        id: \"entertainment\",\n        label: \"Entertainment & Media\",\n        labelAr: \"الترفيه والإعلام\",\n        description: \"Gaming, streaming, and media content\",\n        descriptionAr: \"الألعاب والبث والمحتوى الإعلامي\",\n        icon: \"\\uD83C\\uDFAC\"\n    },\n    {\n        id: \"logistics\",\n        label: \"Logistics & Transportation\",\n        labelAr: \"اللوجستيات والنقل\",\n        description: \"Shipping, delivery, and transportation services\",\n        descriptionAr: \"خدمات الشحن والتوصيل والنقل\",\n        icon: \"\\uD83D\\uDE9A\"\n    },\n    {\n        id: \"agriculture\",\n        label: \"Agriculture & Farming\",\n        labelAr: \"الزراعة والفلاحة\",\n        description: \"Agricultural technology and farming solutions\",\n        descriptionAr: \"التكنولوجيا الزراعية وحلول الفلاحة\",\n        icon: \"\\uD83C\\uDF3E\"\n    },\n    {\n        id: \"manufacturing\",\n        label: \"Manufacturing\",\n        labelAr: \"التصنيع\",\n        description: \"Industrial manufacturing and production\",\n        descriptionAr: \"التصنيع الصناعي والإنتاج\",\n        icon: \"\\uD83C\\uDFED\"\n    },\n    {\n        id: \"consulting\",\n        label: \"Consulting & Services\",\n        labelAr: \"الاستشارات والخدمات\",\n        description: \"Professional consulting and business services\",\n        descriptionAr: \"الاستشارات المهنية وخدمات الأعمال\",\n        icon: \"\\uD83D\\uDCBC\"\n    },\n    {\n        id: \"nonprofit\",\n        label: \"Non-Profit & NGO\",\n        labelAr: \"غير ربحي ومنظمات\",\n        description: \"Non-profit organizations and social causes\",\n        descriptionAr: \"المنظمات غير الربحية والقضايا الاجتماعية\",\n        icon: \"\\uD83E\\uDD1D\"\n    },\n    {\n        id: \"other\",\n        label: \"Other\",\n        labelAr: \"أخرى\",\n        description: \"Other business types and industries\",\n        descriptionAr: \"أنواع أعمال وصناعات أخرى\",\n        icon: \"\\uD83D\\uDCCB\"\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/projectOptions.ts\n");

/***/ }),

/***/ "(ssr)/./src/store/contextStore.ts":
/*!***********************************!*\
  !*** ./src/store/contextStore.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useContextStore: () => (/* binding */ useContextStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n\n\n// القيم الافتراضية\nconst initialState = {\n    projectDefinition: {\n        name: \"\",\n        purpose: \"\",\n        targetUsers: \"\",\n        goals: \"\",\n        scope: \"\",\n        timeline: \"\",\n        projectType: \"\",\n        projectNature: \"\",\n        geographicRegion: \"\",\n        targetPlatforms: [],\n        primaryLanguages: [],\n        complexity: \"\",\n        budget: \"\",\n        teamSize: \"\",\n        deploymentType: \"\"\n    },\n    contextMap: {\n        timeContext: \"\",\n        language: \"\",\n        location: \"\",\n        culturalContext: \"\",\n        behavioralAspects: \"\",\n        environmentalFactors: \"\"\n    },\n    emotionalTone: {\n        personality: \"\",\n        communicationStyle: \"\",\n        userExperience: \"\",\n        brandVoice: \"\",\n        emotionalIntelligence: \"\",\n        interactionFlow: \"\"\n    },\n    technicalLayer: {\n        programmingLanguages: \"\",\n        frameworks: \"\",\n        llmModels: \"\",\n        databases: \"\",\n        apis: \"\",\n        infrastructure: \"\",\n        architecturePattern: \"\",\n        scalingStrategy: \"\",\n        securityRequirements: \"\",\n        performanceTargets: \"\",\n        integrationNeeds: \"\",\n        monitoringTools: \"\"\n    },\n    legalRisk: {\n        privacyConcerns: \"\",\n        dataProtection: \"\",\n        compliance: \"\",\n        risks: \"\",\n        mitigation: \"\",\n        ethicalConsiderations: \"\"\n    },\n    currentLanguage: \"ar\",\n    outputFormat: \"markdown\",\n    showAdvancedOptions: true,\n    apiSettings: {\n        providers: [],\n        globalSettings: {\n            temperature: 0.7,\n            topP: 0.9,\n            maxTokens: 1000,\n            timeout: 30000\n        },\n        // Legacy support\n        openaiApiKey: \"\",\n        openrouterApiKey: \"\",\n        customModels: []\n    }\n};\n// إنشاء المتجر مع التخزين المستمر\nconst useContextStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        ...initialState,\n        updateProjectDefinition: (data)=>set((state)=>({\n                    projectDefinition: {\n                        ...state.projectDefinition,\n                        ...data\n                    }\n                })),\n        updateContextMap: (data)=>set((state)=>({\n                    contextMap: {\n                        ...state.contextMap,\n                        ...data\n                    }\n                })),\n        updateEmotionalTone: (data)=>set((state)=>({\n                    emotionalTone: {\n                        ...state.emotionalTone,\n                        ...data\n                    }\n                })),\n        updateTechnicalLayer: (data)=>set((state)=>({\n                    technicalLayer: {\n                        ...state.technicalLayer,\n                        ...data\n                    }\n                })),\n        updateLegalRisk: (data)=>set((state)=>({\n                    legalRisk: {\n                        ...state.legalRisk,\n                        ...data\n                    }\n                })),\n        setLanguage: (lang)=>set({\n                currentLanguage: lang\n            }),\n        setOutputFormat: (format)=>set({\n                outputFormat: format\n            }),\n        setShowAdvancedOptions: (show)=>set({\n                showAdvancedOptions: show\n            }),\n        setApiSettings: (settings)=>set({\n                apiSettings: {\n                    ...settings,\n                    providers: settings.providers || []\n                }\n            }),\n        // LLM Provider management functions\n        addProvider: (provider)=>set((state)=>{\n                const existingProviders = state.apiSettings.providers || [];\n                // تحقق من عدم وجود مقدم خدمة بنفس المعرف\n                if (existingProviders.find((p)=>p.id === provider.id)) {\n                    console.warn(`Provider with id ${provider.id} already exists`);\n                    return state; // لا تضيف مقدم خدمة مكرر\n                }\n                // إضافة القيم الافتراضية للميزات المتقدمة\n                const enhancedProvider = {\n                    ...provider,\n                    priority: provider.priority || 5,\n                    isBackup: provider.isBackup || false,\n                    maxRequestsPerMinute: provider.maxRequestsPerMinute || 60,\n                    timeout: provider.timeout || 30,\n                    retryAttempts: provider.retryAttempts || 3,\n                    stats: provider.stats || {\n                        totalRequests: 0,\n                        successfulRequests: 0,\n                        failedRequests: 0,\n                        averageResponseTime: 0,\n                        totalTokensUsed: 0,\n                        totalCost: 0\n                    }\n                };\n                return {\n                    apiSettings: {\n                        ...state.apiSettings,\n                        providers: [\n                            ...existingProviders,\n                            enhancedProvider\n                        ]\n                    }\n                };\n            }),\n        updateProvider: (providerId, updates)=>set((state)=>({\n                    apiSettings: {\n                        ...state.apiSettings,\n                        providers: (state.apiSettings.providers || []).map((p)=>p.id === providerId ? {\n                                ...p,\n                                ...updates\n                            } : p)\n                    }\n                })),\n        removeProvider: (providerId)=>set((state)=>({\n                    apiSettings: {\n                        ...state.apiSettings,\n                        providers: (state.apiSettings.providers || []).filter((p)=>p.id !== providerId),\n                        defaultProvider: state.apiSettings.defaultProvider === providerId ? undefined : state.apiSettings.defaultProvider\n                    }\n                })),\n        validateProvider: async (providerId)=>{\n            const state = get();\n            const provider = (state.apiSettings.providers || []).find((p)=>p.id === providerId);\n            if (!provider) return false;\n            try {\n                // Update status to pending\n                state.updateProvider(providerId, {\n                    validationStatus: \"pending\",\n                    errorMessage: undefined\n                });\n                // Call validation API\n                const response = await fetch(\"/api/llm/validate\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        providerId: provider.id,\n                        apiKey: provider.apiKey,\n                        baseUrl: provider.baseUrl\n                    })\n                });\n                const result = await response.json();\n                if (result.valid) {\n                    state.updateProvider(providerId, {\n                        validationStatus: \"valid\",\n                        lastValidated: new Date(),\n                        errorMessage: undefined,\n                        isEnabled: true // تفعيل المقدم تلقائياً عند نجاح التحقق\n                    });\n                    return true;\n                } else {\n                    state.updateProvider(providerId, {\n                        validationStatus: \"invalid\",\n                        errorMessage: result.error || \"Validation failed\"\n                    });\n                    return false;\n                }\n            } catch (error) {\n                state.updateProvider(providerId, {\n                    validationStatus: \"error\",\n                    errorMessage: error instanceof Error ? error.message : \"Unknown error\"\n                });\n                return false;\n            }\n        },\n        getProvider: (providerId)=>{\n            const state = get();\n            return (state.apiSettings.providers || []).find((p)=>p.id === providerId);\n        },\n        getActiveProviders: ()=>{\n            const state = get();\n            return (state.apiSettings.providers || []).filter((p)=>p.isEnabled);\n        },\n        setDefaultProvider: (providerId)=>set((state)=>({\n                    apiSettings: {\n                        ...state.apiSettings,\n                        defaultProvider: providerId\n                    }\n                })),\n        // ميزات متقدمة للمزودين\n        getProvidersByPriority: ()=>{\n            const state = get();\n            return (state.apiSettings.providers || []).filter((p)=>p.isEnabled).sort((a, b)=>(b.priority || 5) - (a.priority || 5));\n        },\n        getBackupProviders: ()=>{\n            const state = get();\n            return (state.apiSettings.providers || []).filter((p)=>p.isEnabled && p.isBackup);\n        },\n        updateProviderStats: (id, stats)=>set((state)=>{\n                const providers = state.apiSettings.providers || [];\n                const providerIndex = providers.findIndex((p)=>p.id === id);\n                if (providerIndex !== -1) {\n                    const updatedProviders = [\n                        ...providers\n                    ];\n                    updatedProviders[providerIndex] = {\n                        ...updatedProviders[providerIndex],\n                        stats: {\n                            ...updatedProviders[providerIndex].stats,\n                            ...stats,\n                            lastUsed: new Date()\n                        }\n                    };\n                    return {\n                        apiSettings: {\n                            ...state.apiSettings,\n                            providers: updatedProviders\n                        }\n                    };\n                }\n                return state;\n            }),\n        getBestProvider: (criteria = \"reliability\")=>{\n            const state = get();\n            const activeProviders = (state.apiSettings.providers || []).filter((p)=>p.isEnabled && !p.isBackup);\n            if (activeProviders.length === 0) return undefined;\n            switch(criteria){\n                case \"speed\":\n                    return activeProviders.reduce((best, current)=>{\n                        const bestSpeed = best.stats?.averageResponseTime || Infinity;\n                        const currentSpeed = current.stats?.averageResponseTime || Infinity;\n                        return currentSpeed < bestSpeed ? current : best;\n                    });\n                case \"cost\":\n                    return activeProviders.reduce((best, current)=>{\n                        const bestCost = best.costPerToken || Infinity;\n                        const currentCost = current.costPerToken || Infinity;\n                        return currentCost < bestCost ? current : best;\n                    });\n                case \"reliability\":\n                default:\n                    return activeProviders.reduce((best, current)=>{\n                        const bestReliability = best.stats ? best.stats.successfulRequests / (best.stats.totalRequests || 1) : 0;\n                        const currentReliability = current.stats ? current.stats.successfulRequests / (current.stats.totalRequests || 1) : 0;\n                        return currentReliability > bestReliability ? current : best;\n                    });\n            }\n        },\n        resetAll: ()=>set(initialState),\n        // مسح جميع الإجابات فقط (الاحتفاظ بالإعدادات)\n        clearAllAnswers: ()=>set((state)=>({\n                    ...state,\n                    projectDefinition: {\n                        name: \"\",\n                        purpose: \"\",\n                        targetUsers: \"\",\n                        goals: \"\",\n                        scope: \"\",\n                        timeline: \"\"\n                    },\n                    contextMap: {\n                        timeContext: \"\",\n                        language: \"\",\n                        location: \"\",\n                        culturalContext: \"\",\n                        behavioralAspects: \"\",\n                        environmentalFactors: \"\"\n                    },\n                    emotionalTone: {\n                        personality: \"\",\n                        communicationStyle: \"\",\n                        userExperience: \"\",\n                        brandVoice: \"\",\n                        emotionalIntelligence: \"\",\n                        interactionFlow: \"\"\n                    },\n                    technicalLayer: {\n                        programmingLanguages: \"\",\n                        frameworks: \"\",\n                        llmModels: \"\",\n                        databases: \"\",\n                        apis: \"\",\n                        infrastructure: \"\"\n                    },\n                    legalRisk: {\n                        privacyConcerns: \"\",\n                        dataProtection: \"\",\n                        compliance: \"\",\n                        risks: \"\",\n                        mitigation: \"\",\n                        ethicalConsiderations: \"\"\n                    }\n                })),\n        getModuleData: (module)=>{\n            const state = get();\n            switch(module){\n                case \"project\":\n                    return state.projectDefinition;\n                case \"context\":\n                    return state.contextMap;\n                case \"emotional\":\n                    return state.emotionalTone;\n                case \"technical\":\n                    return state.technicalLayer;\n                case \"legal\":\n                    return state.legalRisk;\n                default:\n                    return {};\n            }\n        },\n        getAllData: ()=>{\n            const state = get();\n            return {\n                projectDefinition: state.projectDefinition,\n                contextMap: state.contextMap,\n                emotionalTone: state.emotionalTone,\n                technicalLayer: state.technicalLayer,\n                legalRisk: state.legalRisk\n            };\n        }\n    }), {\n    name: \"contextkit-storage\",\n    version: 1,\n    skipHydration: true\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvc3RvcmUvY29udGV4dFN0b3JlLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFpQztBQUNZO0FBMEw3QyxtQkFBbUI7QUFDbkIsTUFBTUUsZUFBZTtJQUNuQkMsbUJBQW1CO1FBQ2pCQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEMsYUFBYTtRQUNiQyxPQUFPO1FBQ1BDLE9BQU87UUFDUEMsVUFBVTtRQUNWQyxhQUFhO1FBQ2JDLGVBQWU7UUFDZkMsa0JBQWtCO1FBQ2xCQyxpQkFBaUIsRUFBRTtRQUNuQkMsa0JBQWtCLEVBQUU7UUFDcEJDLFlBQVk7UUFDWkMsUUFBUTtRQUNSQyxVQUFVO1FBQ1ZDLGdCQUFnQjtJQUNsQjtJQUNBQyxZQUFZO1FBQ1ZDLGFBQWE7UUFDYkMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLGlCQUFpQjtRQUNqQkMsbUJBQW1CO1FBQ25CQyxzQkFBc0I7SUFDeEI7SUFDQUMsZUFBZTtRQUNiQyxhQUFhO1FBQ2JDLG9CQUFvQjtRQUNwQkMsZ0JBQWdCO1FBQ2hCQyxZQUFZO1FBQ1pDLHVCQUF1QjtRQUN2QkMsaUJBQWlCO0lBQ25CO0lBQ0FDLGdCQUFnQjtRQUNkQyxzQkFBc0I7UUFDdEJDLFlBQVk7UUFDWkMsV0FBVztRQUNYQyxXQUFXO1FBQ1hDLE1BQU07UUFDTkMsZ0JBQWdCO1FBQ2hCQyxxQkFBcUI7UUFDckJDLGlCQUFpQjtRQUNqQkMsc0JBQXNCO1FBQ3RCQyxvQkFBb0I7UUFDcEJDLGtCQUFrQjtRQUNsQkMsaUJBQWlCO0lBQ25CO0lBQ0FDLFdBQVc7UUFDVEMsaUJBQWlCO1FBQ2pCQyxnQkFBZ0I7UUFDaEJDLFlBQVk7UUFDWkMsT0FBTztRQUNQQyxZQUFZO1FBQ1pDLHVCQUF1QjtJQUN6QjtJQUNBQyxpQkFBaUI7SUFDakJDLGNBQWM7SUFDZEMscUJBQXFCO0lBQ3JCQyxhQUFhO1FBQ1hDLFdBQVcsRUFBRTtRQUNiQyxnQkFBZ0I7WUFDZEMsYUFBYTtZQUNiQyxNQUFNO1lBQ05DLFdBQVc7WUFDWEMsU0FBUztRQUNYO1FBQ0EsaUJBQWlCO1FBQ2pCQyxjQUFjO1FBQ2RDLGtCQUFrQjtRQUNsQkMsY0FBYyxFQUFFO0lBQ2xCO0FBQ0Y7QUFFQSxrQ0FBa0M7QUFDM0IsTUFBTUMsa0JBQWtCbEUsK0NBQU1BLEdBQ25DQywyREFBT0EsQ0FDTCxDQUFDa0UsS0FBS0MsTUFBUztRQUNiLEdBQUdsRSxZQUFZO1FBRWZtRSx5QkFBeUIsQ0FBQ0MsT0FDeEJILElBQUksQ0FBQ0ksUUFBVztvQkFDZHBFLG1CQUFtQjt3QkFBRSxHQUFHb0UsTUFBTXBFLGlCQUFpQjt3QkFBRSxHQUFHbUUsSUFBSTtvQkFBQztnQkFDM0Q7UUFFRkUsa0JBQWtCLENBQUNGLE9BQ2pCSCxJQUFJLENBQUNJLFFBQVc7b0JBQ2RwRCxZQUFZO3dCQUFFLEdBQUdvRCxNQUFNcEQsVUFBVTt3QkFBRSxHQUFHbUQsSUFBSTtvQkFBQztnQkFDN0M7UUFFRkcscUJBQXFCLENBQUNILE9BQ3BCSCxJQUFJLENBQUNJLFFBQVc7b0JBQ2Q3QyxlQUFlO3dCQUFFLEdBQUc2QyxNQUFNN0MsYUFBYTt3QkFBRSxHQUFHNEMsSUFBSTtvQkFBQztnQkFDbkQ7UUFFRkksc0JBQXNCLENBQUNKLE9BQ3JCSCxJQUFJLENBQUNJLFFBQVc7b0JBQ2R0QyxnQkFBZ0I7d0JBQUUsR0FBR3NDLE1BQU10QyxjQUFjO3dCQUFFLEdBQUdxQyxJQUFJO29CQUFDO2dCQUNyRDtRQUVGSyxpQkFBaUIsQ0FBQ0wsT0FDaEJILElBQUksQ0FBQ0ksUUFBVztvQkFDZHpCLFdBQVc7d0JBQUUsR0FBR3lCLE1BQU16QixTQUFTO3dCQUFFLEdBQUd3QixJQUFJO29CQUFDO2dCQUMzQztRQUVGTSxhQUFhLENBQUNDLE9BQVNWLElBQUk7Z0JBQUVkLGlCQUFpQndCO1lBQUs7UUFDbkRDLGlCQUFpQixDQUFDQyxTQUFXWixJQUFJO2dCQUFFYixjQUFjeUI7WUFBTztRQUN4REMsd0JBQXdCLENBQUNDLE9BQVNkLElBQUk7Z0JBQUVaLHFCQUFxQjBCO1lBQUs7UUFDbEVDLGdCQUFnQixDQUFDQyxXQUFhaEIsSUFBSTtnQkFDaENYLGFBQWE7b0JBQ1gsR0FBRzJCLFFBQVE7b0JBQ1gxQixXQUFXMEIsU0FBUzFCLFNBQVMsSUFBSSxFQUFFO2dCQUNyQztZQUNGO1FBRUEsb0NBQW9DO1FBQ3BDMkIsYUFBYSxDQUFDQyxXQUNabEIsSUFBSSxDQUFDSTtnQkFDSCxNQUFNZSxvQkFBb0JmLE1BQU1mLFdBQVcsQ0FBQ0MsU0FBUyxJQUFJLEVBQUU7Z0JBQzNELHlDQUF5QztnQkFDekMsSUFBSTZCLGtCQUFrQkMsSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFQyxFQUFFLEtBQUtKLFNBQVNJLEVBQUUsR0FBRztvQkFDckRDLFFBQVFDLElBQUksQ0FBQyxDQUFDLGlCQUFpQixFQUFFTixTQUFTSSxFQUFFLENBQUMsZUFBZSxDQUFDO29CQUM3RCxPQUFPbEIsT0FBTyx5QkFBeUI7Z0JBQ3pDO2dCQUVBLDBDQUEwQztnQkFDMUMsTUFBTXFCLG1CQUFtQztvQkFDdkMsR0FBR1AsUUFBUTtvQkFDWFEsVUFBVVIsU0FBU1EsUUFBUSxJQUFJO29CQUMvQkMsVUFBVVQsU0FBU1MsUUFBUSxJQUFJO29CQUMvQkMsc0JBQXNCVixTQUFTVSxvQkFBb0IsSUFBSTtvQkFDdkRqQyxTQUFTdUIsU0FBU3ZCLE9BQU8sSUFBSTtvQkFDN0JrQyxlQUFlWCxTQUFTVyxhQUFhLElBQUk7b0JBQ3pDQyxPQUFPWixTQUFTWSxLQUFLLElBQUk7d0JBQ3ZCQyxlQUFlO3dCQUNmQyxvQkFBb0I7d0JBQ3BCQyxnQkFBZ0I7d0JBQ2hCQyxxQkFBcUI7d0JBQ3JCQyxpQkFBaUI7d0JBQ2pCQyxXQUFXO29CQUNiO2dCQUNGO2dCQUVBLE9BQU87b0JBQ0wvQyxhQUFhO3dCQUNYLEdBQUdlLE1BQU1mLFdBQVc7d0JBQ3BCQyxXQUFXOytCQUFJNkI7NEJBQW1CTTt5QkFBaUI7b0JBQ3JEO2dCQUNGO1lBQ0Y7UUFFRlksZ0JBQWdCLENBQUNDLFlBQVlDLFVBQzNCdkMsSUFBSSxDQUFDSSxRQUFXO29CQUNkZixhQUFhO3dCQUNYLEdBQUdlLE1BQU1mLFdBQVc7d0JBQ3BCQyxXQUFXLENBQUNjLE1BQU1mLFdBQVcsQ0FBQ0MsU0FBUyxJQUFJLEVBQUUsRUFBRWtELEdBQUcsQ0FBQ25CLENBQUFBLElBQ2pEQSxFQUFFQyxFQUFFLEtBQUtnQixhQUFhO2dDQUFFLEdBQUdqQixDQUFDO2dDQUFFLEdBQUdrQixPQUFPOzRCQUFDLElBQUlsQjtvQkFFakQ7Z0JBQ0Y7UUFFRm9CLGdCQUFnQixDQUFDSCxhQUNmdEMsSUFBSSxDQUFDSSxRQUFXO29CQUNkZixhQUFhO3dCQUNYLEdBQUdlLE1BQU1mLFdBQVc7d0JBQ3BCQyxXQUFXLENBQUNjLE1BQU1mLFdBQVcsQ0FBQ0MsU0FBUyxJQUFJLEVBQUUsRUFBRW9ELE1BQU0sQ0FBQ3JCLENBQUFBLElBQUtBLEVBQUVDLEVBQUUsS0FBS2dCO3dCQUNwRUssaUJBQWlCdkMsTUFBTWYsV0FBVyxDQUFDc0QsZUFBZSxLQUFLTCxhQUNuRE0sWUFDQXhDLE1BQU1mLFdBQVcsQ0FBQ3NELGVBQWU7b0JBQ3ZDO2dCQUNGO1FBRUZFLGtCQUFrQixPQUFPUDtZQUN2QixNQUFNbEMsUUFBUUg7WUFDZCxNQUFNaUIsV0FBVyxDQUFDZCxNQUFNZixXQUFXLENBQUNDLFNBQVMsSUFBSSxFQUFFLEVBQUU4QixJQUFJLENBQUNDLENBQUFBLElBQUtBLEVBQUVDLEVBQUUsS0FBS2dCO1lBQ3hFLElBQUksQ0FBQ3BCLFVBQVUsT0FBTztZQUV0QixJQUFJO2dCQUNGLDJCQUEyQjtnQkFDM0JkLE1BQU1pQyxjQUFjLENBQUNDLFlBQVk7b0JBQy9CUSxrQkFBa0I7b0JBQ2xCQyxjQUFjSDtnQkFDaEI7Z0JBRUEsc0JBQXNCO2dCQUN0QixNQUFNSSxXQUFXLE1BQU1DLE1BQU0scUJBQXFCO29CQUNoREMsUUFBUTtvQkFDUkMsU0FBUzt3QkFBRSxnQkFBZ0I7b0JBQW1CO29CQUM5Q0MsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO3dCQUNuQmhCLFlBQVlwQixTQUFTSSxFQUFFO3dCQUN2QmlDLFFBQVFyQyxTQUFTcUMsTUFBTTt3QkFDdkJDLFNBQVN0QyxTQUFTc0MsT0FBTztvQkFDM0I7Z0JBQ0Y7Z0JBRUEsTUFBTUMsU0FBUyxNQUFNVCxTQUFTVSxJQUFJO2dCQUVsQyxJQUFJRCxPQUFPRSxLQUFLLEVBQUU7b0JBQ2hCdkQsTUFBTWlDLGNBQWMsQ0FBQ0MsWUFBWTt3QkFDL0JRLGtCQUFrQjt3QkFDbEJjLGVBQWUsSUFBSUM7d0JBQ25CZCxjQUFjSDt3QkFDZGtCLFdBQVcsS0FBSyx3Q0FBd0M7b0JBQzFEO29CQUNBLE9BQU87Z0JBQ1QsT0FBTztvQkFDTDFELE1BQU1pQyxjQUFjLENBQUNDLFlBQVk7d0JBQy9CUSxrQkFBa0I7d0JBQ2xCQyxjQUFjVSxPQUFPTSxLQUFLLElBQUk7b0JBQ2hDO29CQUNBLE9BQU87Z0JBQ1Q7WUFDRixFQUFFLE9BQU9BLE9BQU87Z0JBQ2QzRCxNQUFNaUMsY0FBYyxDQUFDQyxZQUFZO29CQUMvQlEsa0JBQWtCO29CQUNsQkMsY0FBY2dCLGlCQUFpQkMsUUFBUUQsTUFBTUUsT0FBTyxHQUFHO2dCQUN6RDtnQkFDQSxPQUFPO1lBQ1Q7UUFDRjtRQUVBQyxhQUFhLENBQUM1QjtZQUNaLE1BQU1sQyxRQUFRSDtZQUNkLE9BQU8sQ0FBQ0csTUFBTWYsV0FBVyxDQUFDQyxTQUFTLElBQUksRUFBRSxFQUFFOEIsSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFQyxFQUFFLEtBQUtnQjtRQUNoRTtRQUVBNkIsb0JBQW9CO1lBQ2xCLE1BQU0vRCxRQUFRSDtZQUNkLE9BQU8sQ0FBQ0csTUFBTWYsV0FBVyxDQUFDQyxTQUFTLElBQUksRUFBRSxFQUFFb0QsTUFBTSxDQUFDckIsQ0FBQUEsSUFBS0EsRUFBRXlDLFNBQVM7UUFDcEU7UUFFQU0sb0JBQW9CLENBQUM5QixhQUNuQnRDLElBQUksQ0FBQ0ksUUFBVztvQkFDZGYsYUFBYTt3QkFDWCxHQUFHZSxNQUFNZixXQUFXO3dCQUNwQnNELGlCQUFpQkw7b0JBQ25CO2dCQUNGO1FBRUYsd0JBQXdCO1FBQ3hCK0Isd0JBQXdCO1lBQ3RCLE1BQU1qRSxRQUFRSDtZQUNkLE9BQU8sQ0FBQ0csTUFBTWYsV0FBVyxDQUFDQyxTQUFTLElBQUksRUFBRSxFQUN0Q29ELE1BQU0sQ0FBQ3JCLENBQUFBLElBQUtBLEVBQUV5QyxTQUFTLEVBQ3ZCUSxJQUFJLENBQUMsQ0FBQ0MsR0FBR0MsSUFBTSxDQUFDQSxFQUFFOUMsUUFBUSxJQUFJLEtBQU02QyxDQUFBQSxFQUFFN0MsUUFBUSxJQUFJO1FBQ3ZEO1FBRUErQyxvQkFBb0I7WUFDbEIsTUFBTXJFLFFBQVFIO1lBQ2QsT0FBTyxDQUFDRyxNQUFNZixXQUFXLENBQUNDLFNBQVMsSUFBSSxFQUFFLEVBQ3RDb0QsTUFBTSxDQUFDckIsQ0FBQUEsSUFBS0EsRUFBRXlDLFNBQVMsSUFBSXpDLEVBQUVNLFFBQVE7UUFDMUM7UUFFQStDLHFCQUFxQixDQUFDcEQsSUFBSVEsUUFDeEI5QixJQUFJLENBQUNJO2dCQUNILE1BQU1kLFlBQVljLE1BQU1mLFdBQVcsQ0FBQ0MsU0FBUyxJQUFJLEVBQUU7Z0JBQ25ELE1BQU1xRixnQkFBZ0JyRixVQUFVc0YsU0FBUyxDQUFDdkQsQ0FBQUEsSUFBS0EsRUFBRUMsRUFBRSxLQUFLQTtnQkFFeEQsSUFBSXFELGtCQUFrQixDQUFDLEdBQUc7b0JBQ3hCLE1BQU1FLG1CQUFtQjsyQkFBSXZGO3FCQUFVO29CQUN2Q3VGLGdCQUFnQixDQUFDRixjQUFjLEdBQUc7d0JBQ2hDLEdBQUdFLGdCQUFnQixDQUFDRixjQUFjO3dCQUNsQzdDLE9BQU87NEJBQ0wsR0FBRytDLGdCQUFnQixDQUFDRixjQUFjLENBQUM3QyxLQUFLOzRCQUN4QyxHQUFHQSxLQUFLOzRCQUNSZ0QsVUFBVSxJQUFJakI7d0JBQ2hCO29CQUNGO29CQUVBLE9BQU87d0JBQ0x4RSxhQUFhOzRCQUNYLEdBQUdlLE1BQU1mLFdBQVc7NEJBQ3BCQyxXQUFXdUY7d0JBQ2I7b0JBQ0Y7Z0JBQ0Y7Z0JBQ0EsT0FBT3pFO1lBQ1Q7UUFFRjJFLGlCQUFpQixDQUFDQyxXQUFXLGFBQWE7WUFDeEMsTUFBTTVFLFFBQVFIO1lBQ2QsTUFBTWdGLGtCQUFrQixDQUFDN0UsTUFBTWYsV0FBVyxDQUFDQyxTQUFTLElBQUksRUFBRSxFQUN2RG9ELE1BQU0sQ0FBQ3JCLENBQUFBLElBQUtBLEVBQUV5QyxTQUFTLElBQUksQ0FBQ3pDLEVBQUVNLFFBQVE7WUFFekMsSUFBSXNELGdCQUFnQkMsTUFBTSxLQUFLLEdBQUcsT0FBT3RDO1lBRXpDLE9BQVFvQztnQkFDTixLQUFLO29CQUNILE9BQU9DLGdCQUFnQkUsTUFBTSxDQUFDLENBQUNDLE1BQU1DO3dCQUNuQyxNQUFNQyxZQUFZRixLQUFLdEQsS0FBSyxFQUFFSSx1QkFBdUJxRDt3QkFDckQsTUFBTUMsZUFBZUgsUUFBUXZELEtBQUssRUFBRUksdUJBQXVCcUQ7d0JBQzNELE9BQU9DLGVBQWVGLFlBQVlELFVBQVVEO29CQUM5QztnQkFFRixLQUFLO29CQUNILE9BQU9ILGdCQUFnQkUsTUFBTSxDQUFDLENBQUNDLE1BQU1DO3dCQUNuQyxNQUFNSSxXQUFXTCxLQUFLTSxZQUFZLElBQUlIO3dCQUN0QyxNQUFNSSxjQUFjTixRQUFRSyxZQUFZLElBQUlIO3dCQUM1QyxPQUFPSSxjQUFjRixXQUFXSixVQUFVRDtvQkFDNUM7Z0JBRUYsS0FBSztnQkFDTDtvQkFDRSxPQUFPSCxnQkFBZ0JFLE1BQU0sQ0FBQyxDQUFDQyxNQUFNQzt3QkFDbkMsTUFBTU8sa0JBQWtCUixLQUFLdEQsS0FBSyxHQUMvQnNELEtBQUt0RCxLQUFLLENBQUNFLGtCQUFrQixHQUFJb0QsQ0FBQUEsS0FBS3RELEtBQUssQ0FBQ0MsYUFBYSxJQUFJLEtBQU07d0JBQ3RFLE1BQU04RCxxQkFBcUJSLFFBQVF2RCxLQUFLLEdBQ3JDdUQsUUFBUXZELEtBQUssQ0FBQ0Usa0JBQWtCLEdBQUlxRCxDQUFBQSxRQUFRdkQsS0FBSyxDQUFDQyxhQUFhLElBQUksS0FBTTt3QkFDNUUsT0FBTzhELHFCQUFxQkQsa0JBQWtCUCxVQUFVRDtvQkFDMUQ7WUFDSjtRQUNGO1FBRUFVLFVBQVUsSUFBTTlGLElBQUlqRTtRQUVwQiw4Q0FBOEM7UUFDOUNnSyxpQkFBaUIsSUFBTS9GLElBQUksQ0FBQ0ksUUFBVztvQkFDckMsR0FBR0EsS0FBSztvQkFDUnBFLG1CQUFtQjt3QkFDakJDLE1BQU07d0JBQ05DLFNBQVM7d0JBQ1RDLGFBQWE7d0JBQ2JDLE9BQU87d0JBQ1BDLE9BQU87d0JBQ1BDLFVBQVU7b0JBQ1o7b0JBQ0FVLFlBQVk7d0JBQ1ZDLGFBQWE7d0JBQ2JDLFVBQVU7d0JBQ1ZDLFVBQVU7d0JBQ1ZDLGlCQUFpQjt3QkFDakJDLG1CQUFtQjt3QkFDbkJDLHNCQUFzQjtvQkFDeEI7b0JBQ0FDLGVBQWU7d0JBQ2JDLGFBQWE7d0JBQ2JDLG9CQUFvQjt3QkFDcEJDLGdCQUFnQjt3QkFDaEJDLFlBQVk7d0JBQ1pDLHVCQUF1Qjt3QkFDdkJDLGlCQUFpQjtvQkFDbkI7b0JBQ0FDLGdCQUFnQjt3QkFDZEMsc0JBQXNCO3dCQUN0QkMsWUFBWTt3QkFDWkMsV0FBVzt3QkFDWEMsV0FBVzt3QkFDWEMsTUFBTTt3QkFDTkMsZ0JBQWdCO29CQUNsQjtvQkFDQU8sV0FBVzt3QkFDVEMsaUJBQWlCO3dCQUNqQkMsZ0JBQWdCO3dCQUNoQkMsWUFBWTt3QkFDWkMsT0FBTzt3QkFDUEMsWUFBWTt3QkFDWkMsdUJBQXVCO29CQUN6QjtnQkFDRjtRQUVBK0csZUFBZSxDQUFDQztZQUNkLE1BQU03RixRQUFRSDtZQUNkLE9BQVFnRztnQkFDTixLQUFLO29CQUFXLE9BQU83RixNQUFNcEUsaUJBQWlCO2dCQUM5QyxLQUFLO29CQUFXLE9BQU9vRSxNQUFNcEQsVUFBVTtnQkFDdkMsS0FBSztvQkFBYSxPQUFPb0QsTUFBTTdDLGFBQWE7Z0JBQzVDLEtBQUs7b0JBQWEsT0FBTzZDLE1BQU10QyxjQUFjO2dCQUM3QyxLQUFLO29CQUFTLE9BQU9zQyxNQUFNekIsU0FBUztnQkFDcEM7b0JBQVMsT0FBTyxDQUFDO1lBQ25CO1FBQ0Y7UUFFQXVILFlBQVk7WUFDVixNQUFNOUYsUUFBUUg7WUFDZCxPQUFPO2dCQUNMakUsbUJBQW1Cb0UsTUFBTXBFLGlCQUFpQjtnQkFDMUNnQixZQUFZb0QsTUFBTXBELFVBQVU7Z0JBQzVCTyxlQUFlNkMsTUFBTTdDLGFBQWE7Z0JBQ2xDTyxnQkFBZ0JzQyxNQUFNdEMsY0FBYztnQkFDcENhLFdBQVd5QixNQUFNekIsU0FBUztZQUM1QjtRQUNGO0lBQ0YsSUFDQTtJQUNFMUMsTUFBTTtJQUNOa0ssU0FBUztJQUNUQyxlQUFlO0FBQ2pCLElBRUYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb250ZXh0a2l0Ly4vc3JjL3N0b3JlL2NvbnRleHRTdG9yZS50cz9kNzI4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZSB9IGZyb20gJ3p1c3RhbmQnO1xuaW1wb3J0IHsgcGVyc2lzdCB9IGZyb20gJ3p1c3RhbmQvbWlkZGxld2FyZSc7XG5cbi8vINiq2LnYsdmK2YEg2KPZhtmI2KfYuSDYp9mE2KjZitin2YbYp9iqINmE2YPZhCDZhdit2YjYsVxuZXhwb3J0IGludGVyZmFjZSBQcm9qZWN0RGVmaW5pdGlvbiB7XG4gIG5hbWU6IHN0cmluZztcbiAgcHVycG9zZTogc3RyaW5nO1xuICB0YXJnZXRVc2Vyczogc3RyaW5nO1xuICBnb2Fsczogc3RyaW5nO1xuICBzY29wZTogc3RyaW5nO1xuICB0aW1lbGluZTogc3RyaW5nO1xuICAvLyDYrtmK2KfYsdin2Kog2KfZhNiq2K7YtdmK2LUg2KfZhNmF2KrZgtiv2YXYqVxuICBwcm9qZWN0VHlwZTogc3RyaW5nOyAvLyDZhtmI2Lkg2KfZhNmF2LTYsdmI2LkgKNiq2LfYqNmK2YIg2YjZitio2Iwg2KrYt9io2YrZgiDYrNmI2KfZhNiMINiz2LfYrSDYp9mE2YXZg9iq2KjYjCDYpdmE2K4pXG4gIHByb2plY3ROYXR1cmU6IHN0cmluZzsgLy8g2LfYqNmK2LnYqSDYp9mE2YXYtNix2YjYuSAo2YTYqNin2LMg2KfZhNiq2K7Ysdis2Iwg2KfZhNiq2LnZhNmK2YXYjCDYp9mE2LXYrdip2Iwg2KXZhNiuKVxuICBnZW9ncmFwaGljUmVnaW9uOiBzdHJpbmc7IC8vINin2YTZhdmG2LfZgtipINin2YTYrNi62LHYp9mB2YrYqSAo2KfZhNmF2LrYsdio2Iwg2KfZhNi02LHZgiDYp9mE2KPZiNiz2LfYjCDYudin2YTZhdmK2Iwg2KXZhNiuKVxuICB0YXJnZXRQbGF0Zm9ybXM6IHN0cmluZ1tdOyAvLyDYp9mE2YXZhti12KfYqiDYp9mE2YXYs9iq2YfYr9mB2KlcbiAgcHJpbWFyeUxhbmd1YWdlczogc3RyaW5nW107IC8vINmE2LrYp9iqINin2YTYqNix2YXYrNipINin2YTYo9iz2KfYs9mK2KlcbiAgY29tcGxleGl0eTogc3RyaW5nOyAvLyDZhdiz2KrZiNmJINin2YTYqti52YLZitivXG4gIGJ1ZGdldDogc3RyaW5nOyAvLyDYp9mE2YXZitiy2KfZhtmK2Kkg2KfZhNmF2KrZiNmC2LnYqVxuICB0ZWFtU2l6ZTogc3RyaW5nOyAvLyDYrdis2YUg2KfZhNmB2LHZitmCXG4gIGRlcGxveW1lbnRUeXBlOiBzdHJpbmc7IC8vINmG2YjYuSDYp9mE2YbYtNixICjYs9it2KfYqNmK2Iwg2YXYrdmE2YrYjCDZh9is2YrZhilcbn1cblxuZXhwb3J0IGludGVyZmFjZSBDb250ZXh0TWFwIHtcbiAgdGltZUNvbnRleHQ6IHN0cmluZztcbiAgbGFuZ3VhZ2U6IHN0cmluZztcbiAgbG9jYXRpb246IHN0cmluZztcbiAgY3VsdHVyYWxDb250ZXh0OiBzdHJpbmc7XG4gIGJlaGF2aW9yYWxBc3BlY3RzOiBzdHJpbmc7XG4gIGVudmlyb25tZW50YWxGYWN0b3JzOiBzdHJpbmc7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgRW1vdGlvbmFsVG9uZSB7XG4gIHBlcnNvbmFsaXR5OiBzdHJpbmc7XG4gIGNvbW11bmljYXRpb25TdHlsZTogc3RyaW5nO1xuICB1c2VyRXhwZXJpZW5jZTogc3RyaW5nO1xuICBicmFuZFZvaWNlOiBzdHJpbmc7XG4gIGVtb3Rpb25hbEludGVsbGlnZW5jZTogc3RyaW5nO1xuICBpbnRlcmFjdGlvbkZsb3c6IHN0cmluZztcbn1cblxuZXhwb3J0IGludGVyZmFjZSBUZWNobmljYWxMYXllciB7XG4gIHByb2dyYW1taW5nTGFuZ3VhZ2VzOiBzdHJpbmc7XG4gIGZyYW1ld29ya3M6IHN0cmluZztcbiAgbGxtTW9kZWxzOiBzdHJpbmc7XG4gIGRhdGFiYXNlczogc3RyaW5nO1xuICBhcGlzOiBzdHJpbmc7XG4gIGluZnJhc3RydWN0dXJlOiBzdHJpbmc7XG4gIC8vINiu2YrYp9ix2KfYqiDYqtmC2YbZitipINmF2KrZgtiv2YXYqVxuICBhcmNoaXRlY3R1cmVQYXR0ZXJuOiBzdHJpbmc7IC8vINmG2YXYtyDYp9mE2YfZhtiv2LPYqSDYp9mE2YXYudmF2KfYsdmK2KlcbiAgc2NhbGluZ1N0cmF0ZWd5OiBzdHJpbmc7IC8vINin2LPYqtix2KfYqtmK2KzZitipINin2YTYqtmI2LPYuVxuICBzZWN1cml0eVJlcXVpcmVtZW50czogc3RyaW5nOyAvLyDZhdiq2LfZhNio2KfYqiDYp9mE2KPZhdin2YZcbiAgcGVyZm9ybWFuY2VUYXJnZXRzOiBzdHJpbmc7IC8vINij2YfYr9in2YEg2KfZhNij2K/Yp9ihXG4gIGludGVncmF0aW9uTmVlZHM6IHN0cmluZzsgLy8g2KfYrdiq2YrYp9is2KfYqiDYp9mE2KrZg9in2YXZhFxuICBtb25pdG9yaW5nVG9vbHM6IHN0cmluZzsgLy8g2KPYr9mI2KfYqiDYp9mE2YXYsdin2YLYqNipXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgTGVnYWxSaXNrIHtcbiAgcHJpdmFjeUNvbmNlcm5zOiBzdHJpbmc7XG4gIGRhdGFQcm90ZWN0aW9uOiBzdHJpbmc7XG4gIGNvbXBsaWFuY2U6IHN0cmluZztcbiAgcmlza3M6IHN0cmluZztcbiAgbWl0aWdhdGlvbjogc3RyaW5nO1xuICBldGhpY2FsQ29uc2lkZXJhdGlvbnM6IHN0cmluZztcbn1cblxuLy8gTExNIFByb3ZpZGVyIGludGVyZmFjZXNcbmV4cG9ydCBpbnRlcmZhY2UgTExNTW9kZWwge1xuICBpZDogc3RyaW5nO1xuICBuYW1lOiBzdHJpbmc7XG4gIGRlc2NyaXB0aW9uOiBzdHJpbmc7XG4gIGNvbnRleHRMZW5ndGg6IG51bWJlcjtcbiAgcHJpY2luZz86IHN0cmluZztcbiAgaW5wdXRQcmljZT86IG51bWJlcjtcbiAgb3V0cHV0UHJpY2U/OiBudW1iZXI7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgTExNUHJvdmlkZXIge1xuICBpZDogc3RyaW5nO1xuICBuYW1lOiBzdHJpbmc7XG4gIGljb246IHN0cmluZztcbiAgZGVzY3JpcHRpb246IHN0cmluZztcbiAgYmFzZVVybDogc3RyaW5nO1xuICBhcGlLZXlQbGFjZWhvbGRlcjogc3RyaW5nO1xuICBtb2RlbHM6IExMTU1vZGVsW107XG4gIGhlYWRlcnM/OiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+O1xuICBpc0FjdGl2ZTogYm9vbGVhbjtcbiAgc3VwcG9ydHNTdHJlYW1pbmc/OiBib29sZWFuO1xuICBtYXhUb2tlbnM/OiBudW1iZXI7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgUHJvdmlkZXJDb25maWcge1xuICBpZDogc3RyaW5nO1xuICBhcGlLZXk6IHN0cmluZztcbiAgYmFzZVVybD86IHN0cmluZzsgLy8gT3ZlcnJpZGUgZGVmYXVsdCBiYXNlIFVSTFxuICBzZWxlY3RlZE1vZGVsczogc3RyaW5nW107XG4gIGlzRW5hYmxlZDogYm9vbGVhbjtcbiAgbGFzdFZhbGlkYXRlZD86IERhdGU7XG4gIHZhbGlkYXRpb25TdGF0dXM6ICdwZW5kaW5nJyB8ICd2YWxpZCcgfCAnaW52YWxpZCcgfCAnZXJyb3InO1xuICBlcnJvck1lc3NhZ2U/OiBzdHJpbmc7XG4gIGN1c3RvbVNldHRpbmdzPzogUmVjb3JkPHN0cmluZywgYW55PjtcblxuICAvLyDZhdmK2LLYp9iqINmF2KrZgtiv2YXYqVxuICBwcmlvcml0eTogbnVtYmVyOyAvLyDYo9mI2YTZiNmK2Kkg2KfZhNmF2LLZiNivICgxLTEwKVxuICBpc0JhY2t1cDogYm9vbGVhbjsgLy8g2YfZhCDZh9mIINmF2LLZiNivINin2K3YqtmK2KfYt9mKXG4gIG1heFJlcXVlc3RzUGVyTWludXRlPzogbnVtYmVyOyAvLyDYrdivINin2YTYt9mE2KjYp9iqINmB2Yog2KfZhNiv2YLZitmC2KlcbiAgdGltZW91dD86IG51bWJlcjsgLy8g2YXZh9mE2Kkg2KfZhNi32YTYqCDYqNin2YTYq9mI2KfZhtmKXG4gIHJldHJ5QXR0ZW1wdHM/OiBudW1iZXI7IC8vINi52K/YryDZhdit2KfZiNmE2KfYqiDYp9mE2KXYudin2K/YqVxuICBjb3N0UGVyVG9rZW4/OiBudW1iZXI7IC8vINiq2YPZhNmB2Kkg2YPZhCB0b2tlblxuXG4gIC8vINil2K3Ytdin2KbZitin2Kog2KfZhNij2K/Yp9ihXG4gIHN0YXRzPzoge1xuICAgIHRvdGFsUmVxdWVzdHM6IG51bWJlcjtcbiAgICBzdWNjZXNzZnVsUmVxdWVzdHM6IG51bWJlcjtcbiAgICBmYWlsZWRSZXF1ZXN0czogbnVtYmVyO1xuICAgIGF2ZXJhZ2VSZXNwb25zZVRpbWU6IG51bWJlcjtcbiAgICB0b3RhbFRva2Vuc1VzZWQ6IG51bWJlcjtcbiAgICB0b3RhbENvc3Q6IG51bWJlcjtcbiAgICBsYXN0VXNlZD86IERhdGU7XG4gIH07XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgQXBpU2V0dGluZ3Mge1xuICBwcm92aWRlcnM6IFByb3ZpZGVyQ29uZmlnW107XG4gIGRlZmF1bHRQcm92aWRlcj86IHN0cmluZztcbiAgZGVmYXVsdE1vZGVsPzogc3RyaW5nO1xuICBnbG9iYWxTZXR0aW5nczoge1xuICAgIHRlbXBlcmF0dXJlOiBudW1iZXI7XG4gICAgdG9wUDogbnVtYmVyO1xuICAgIG1heFRva2VuczogbnVtYmVyO1xuICAgIHRpbWVvdXQ6IG51bWJlcjtcbiAgfTtcbiAgLy8gTGVnYWN5IHN1cHBvcnQgLSB3aWxsIGJlIG1pZ3JhdGVkXG4gIG9wZW5haUFwaUtleT86IHN0cmluZztcbiAgb3BlbnJvdXRlckFwaUtleT86IHN0cmluZztcbiAgY3VzdG9tTW9kZWxzPzogYW55W107XG59XG5cbi8vINit2KfZhNipINin2YTYqti32KjZitmCINin2YTYsdim2YrYs9mK2KlcbmV4cG9ydCBpbnRlcmZhY2UgQ29udGV4dFN0YXRlIHtcbiAgLy8g2KjZitin2YbYp9iqINmD2YQg2YXYrdmI2LFcbiAgcHJvamVjdERlZmluaXRpb246IFByb2plY3REZWZpbml0aW9uO1xuICBjb250ZXh0TWFwOiBDb250ZXh0TWFwO1xuICBlbW90aW9uYWxUb25lOiBFbW90aW9uYWxUb25lO1xuICB0ZWNobmljYWxMYXllcjogVGVjaG5pY2FsTGF5ZXI7XG4gIGxlZ2FsUmlzazogTGVnYWxSaXNrO1xuXG4gIC8vINil2LnYr9in2K/Yp9iqINin2YTYqti32KjZitmCXG4gIGN1cnJlbnRMYW5ndWFnZTogJ2FyJyB8ICdlbic7XG4gIG91dHB1dEZvcm1hdDogJ21hcmtkb3duJyB8ICdodG1sJyB8ICdqc29uJyB8ICd5YW1sJztcbiAgYXBpU2V0dGluZ3M6IEFwaVNldHRpbmdzO1xuICBzaG93QWR2YW5jZWRPcHRpb25zOiBib29sZWFuO1xuICBcbiAgLy8g2KfZhNil2KzYsdin2KHYp9iqXG4gIHVwZGF0ZVByb2plY3REZWZpbml0aW9uOiAoZGF0YTogUGFydGlhbDxQcm9qZWN0RGVmaW5pdGlvbj4pID0+IHZvaWQ7XG4gIHVwZGF0ZUNvbnRleHRNYXA6IChkYXRhOiBQYXJ0aWFsPENvbnRleHRNYXA+KSA9PiB2b2lkO1xuICB1cGRhdGVFbW90aW9uYWxUb25lOiAoZGF0YTogUGFydGlhbDxFbW90aW9uYWxUb25lPikgPT4gdm9pZDtcbiAgdXBkYXRlVGVjaG5pY2FsTGF5ZXI6IChkYXRhOiBQYXJ0aWFsPFRlY2huaWNhbExheWVyPikgPT4gdm9pZDtcbiAgdXBkYXRlTGVnYWxSaXNrOiAoZGF0YTogUGFydGlhbDxMZWdhbFJpc2s+KSA9PiB2b2lkO1xuICBzZXRMYW5ndWFnZTogKGxhbmc6ICdhcicgfCAnZW4nKSA9PiB2b2lkO1xuICBzZXRPdXRwdXRGb3JtYXQ6IChmb3JtYXQ6ICdtYXJrZG93bicgfCAnaHRtbCcgfCAnanNvbicgfCAneWFtbCcpID0+IHZvaWQ7XG4gIHNldFNob3dBZHZhbmNlZE9wdGlvbnM6IChzaG93OiBib29sZWFuKSA9PiB2b2lkO1xuICBzZXRBcGlTZXR0aW5nczogKHNldHRpbmdzOiBBcGlTZXR0aW5ncykgPT4gdm9pZDtcbiAgY2xlYXJBbGxBbnN3ZXJzOiAoKSA9PiB2b2lkO1xuXG4gIC8vIExMTSBQcm92aWRlciBtYW5hZ2VtZW50XG4gIGFkZFByb3ZpZGVyOiAocHJvdmlkZXI6IFByb3ZpZGVyQ29uZmlnKSA9PiB2b2lkO1xuICB1cGRhdGVQcm92aWRlcjogKHByb3ZpZGVySWQ6IHN0cmluZywgdXBkYXRlczogUGFydGlhbDxQcm92aWRlckNvbmZpZz4pID0+IHZvaWQ7XG4gIHJlbW92ZVByb3ZpZGVyOiAocHJvdmlkZXJJZDogc3RyaW5nKSA9PiB2b2lkO1xuICB2YWxpZGF0ZVByb3ZpZGVyOiAocHJvdmlkZXJJZDogc3RyaW5nKSA9PiBQcm9taXNlPGJvb2xlYW4+O1xuICBnZXRQcm92aWRlcjogKHByb3ZpZGVySWQ6IHN0cmluZykgPT4gUHJvdmlkZXJDb25maWcgfCB1bmRlZmluZWQ7XG4gIGdldEFjdGl2ZVByb3ZpZGVyczogKCkgPT4gUHJvdmlkZXJDb25maWdbXTtcbiAgc2V0RGVmYXVsdFByb3ZpZGVyOiAocHJvdmlkZXJJZDogc3RyaW5nKSA9PiB2b2lkO1xuXG4gIC8vINmF2YrYstin2Kog2YXYqtmC2K/ZhdipINmE2YTZhdiy2YjYr9mK2YZcbiAgZ2V0UHJvdmlkZXJzQnlQcmlvcml0eTogKCkgPT4gUHJvdmlkZXJDb25maWdbXTtcbiAgZ2V0QmFja3VwUHJvdmlkZXJzOiAoKSA9PiBQcm92aWRlckNvbmZpZ1tdO1xuICB1cGRhdGVQcm92aWRlclN0YXRzOiAoaWQ6IHN0cmluZywgc3RhdHM6IFBhcnRpYWw8UHJvdmlkZXJDb25maWdbJ3N0YXRzJ10+KSA9PiB2b2lkO1xuICBnZXRCZXN0UHJvdmlkZXI6IChjcml0ZXJpYT86ICdzcGVlZCcgfCAnY29zdCcgfCAncmVsaWFiaWxpdHknKSA9PiBQcm92aWRlckNvbmZpZyB8IHVuZGVmaW5lZDtcblxuICByZXNldEFsbDogKCkgPT4gdm9pZDtcbiAgXG4gIC8vINmF2LPYp9i52K/Yp9iqINmE2YTYqti12K/ZitixXG4gIGdldE1vZHVsZURhdGE6IChtb2R1bGU6IHN0cmluZykgPT4gYW55O1xuICBnZXRBbGxEYXRhOiAoKSA9PiBhbnk7XG59XG5cbi8vINin2YTZgtmK2YUg2KfZhNin2YHYqtix2KfYttmK2KlcbmNvbnN0IGluaXRpYWxTdGF0ZSA9IHtcbiAgcHJvamVjdERlZmluaXRpb246IHtcbiAgICBuYW1lOiAnJyxcbiAgICBwdXJwb3NlOiAnJyxcbiAgICB0YXJnZXRVc2VyczogJycsXG4gICAgZ29hbHM6ICcnLFxuICAgIHNjb3BlOiAnJyxcbiAgICB0aW1lbGluZTogJycsXG4gICAgcHJvamVjdFR5cGU6ICcnLFxuICAgIHByb2plY3ROYXR1cmU6ICcnLFxuICAgIGdlb2dyYXBoaWNSZWdpb246ICcnLFxuICAgIHRhcmdldFBsYXRmb3JtczogW10sXG4gICAgcHJpbWFyeUxhbmd1YWdlczogW10sXG4gICAgY29tcGxleGl0eTogJycsXG4gICAgYnVkZ2V0OiAnJyxcbiAgICB0ZWFtU2l6ZTogJycsXG4gICAgZGVwbG95bWVudFR5cGU6ICcnXG4gIH0sXG4gIGNvbnRleHRNYXA6IHtcbiAgICB0aW1lQ29udGV4dDogJycsXG4gICAgbGFuZ3VhZ2U6ICcnLFxuICAgIGxvY2F0aW9uOiAnJyxcbiAgICBjdWx0dXJhbENvbnRleHQ6ICcnLFxuICAgIGJlaGF2aW9yYWxBc3BlY3RzOiAnJyxcbiAgICBlbnZpcm9ubWVudGFsRmFjdG9yczogJydcbiAgfSxcbiAgZW1vdGlvbmFsVG9uZToge1xuICAgIHBlcnNvbmFsaXR5OiAnJyxcbiAgICBjb21tdW5pY2F0aW9uU3R5bGU6ICcnLFxuICAgIHVzZXJFeHBlcmllbmNlOiAnJyxcbiAgICBicmFuZFZvaWNlOiAnJyxcbiAgICBlbW90aW9uYWxJbnRlbGxpZ2VuY2U6ICcnLFxuICAgIGludGVyYWN0aW9uRmxvdzogJydcbiAgfSxcbiAgdGVjaG5pY2FsTGF5ZXI6IHtcbiAgICBwcm9ncmFtbWluZ0xhbmd1YWdlczogJycsXG4gICAgZnJhbWV3b3JrczogJycsXG4gICAgbGxtTW9kZWxzOiAnJyxcbiAgICBkYXRhYmFzZXM6ICcnLFxuICAgIGFwaXM6ICcnLFxuICAgIGluZnJhc3RydWN0dXJlOiAnJyxcbiAgICBhcmNoaXRlY3R1cmVQYXR0ZXJuOiAnJyxcbiAgICBzY2FsaW5nU3RyYXRlZ3k6ICcnLFxuICAgIHNlY3VyaXR5UmVxdWlyZW1lbnRzOiAnJyxcbiAgICBwZXJmb3JtYW5jZVRhcmdldHM6ICcnLFxuICAgIGludGVncmF0aW9uTmVlZHM6ICcnLFxuICAgIG1vbml0b3JpbmdUb29sczogJydcbiAgfSxcbiAgbGVnYWxSaXNrOiB7XG4gICAgcHJpdmFjeUNvbmNlcm5zOiAnJyxcbiAgICBkYXRhUHJvdGVjdGlvbjogJycsXG4gICAgY29tcGxpYW5jZTogJycsXG4gICAgcmlza3M6ICcnLFxuICAgIG1pdGlnYXRpb246ICcnLFxuICAgIGV0aGljYWxDb25zaWRlcmF0aW9uczogJydcbiAgfSxcbiAgY3VycmVudExhbmd1YWdlOiAnYXInIGFzIGNvbnN0LFxuICBvdXRwdXRGb3JtYXQ6ICdtYXJrZG93bicgYXMgY29uc3QsXG4gIHNob3dBZHZhbmNlZE9wdGlvbnM6IHRydWUsXG4gIGFwaVNldHRpbmdzOiB7XG4gICAgcHJvdmlkZXJzOiBbXSxcbiAgICBnbG9iYWxTZXR0aW5nczoge1xuICAgICAgdGVtcGVyYXR1cmU6IDAuNyxcbiAgICAgIHRvcFA6IDAuOSxcbiAgICAgIG1heFRva2VuczogMTAwMCxcbiAgICAgIHRpbWVvdXQ6IDMwMDAwXG4gICAgfSxcbiAgICAvLyBMZWdhY3kgc3VwcG9ydFxuICAgIG9wZW5haUFwaUtleTogJycsXG4gICAgb3BlbnJvdXRlckFwaUtleTogJycsXG4gICAgY3VzdG9tTW9kZWxzOiBbXVxuICB9XG59O1xuXG4vLyDYpdmG2LTYp9ihINin2YTZhdiq2KzYsSDZhdi5INin2YTYqtiu2LLZitmGINin2YTZhdiz2KrZhdixXG5leHBvcnQgY29uc3QgdXNlQ29udGV4dFN0b3JlID0gY3JlYXRlPENvbnRleHRTdGF0ZT4oKShcbiAgcGVyc2lzdChcbiAgICAoc2V0LCBnZXQpID0+ICh7XG4gICAgICAuLi5pbml0aWFsU3RhdGUsXG4gICAgICBcbiAgICAgIHVwZGF0ZVByb2plY3REZWZpbml0aW9uOiAoZGF0YSkgPT5cbiAgICAgICAgc2V0KChzdGF0ZSkgPT4gKHtcbiAgICAgICAgICBwcm9qZWN0RGVmaW5pdGlvbjogeyAuLi5zdGF0ZS5wcm9qZWN0RGVmaW5pdGlvbiwgLi4uZGF0YSB9XG4gICAgICAgIH0pKSxcbiAgICAgICAgXG4gICAgICB1cGRhdGVDb250ZXh0TWFwOiAoZGF0YSkgPT5cbiAgICAgICAgc2V0KChzdGF0ZSkgPT4gKHtcbiAgICAgICAgICBjb250ZXh0TWFwOiB7IC4uLnN0YXRlLmNvbnRleHRNYXAsIC4uLmRhdGEgfVxuICAgICAgICB9KSksXG4gICAgICAgIFxuICAgICAgdXBkYXRlRW1vdGlvbmFsVG9uZTogKGRhdGEpID0+XG4gICAgICAgIHNldCgoc3RhdGUpID0+ICh7XG4gICAgICAgICAgZW1vdGlvbmFsVG9uZTogeyAuLi5zdGF0ZS5lbW90aW9uYWxUb25lLCAuLi5kYXRhIH1cbiAgICAgICAgfSkpLFxuICAgICAgICBcbiAgICAgIHVwZGF0ZVRlY2huaWNhbExheWVyOiAoZGF0YSkgPT5cbiAgICAgICAgc2V0KChzdGF0ZSkgPT4gKHtcbiAgICAgICAgICB0ZWNobmljYWxMYXllcjogeyAuLi5zdGF0ZS50ZWNobmljYWxMYXllciwgLi4uZGF0YSB9XG4gICAgICAgIH0pKSxcbiAgICAgICAgXG4gICAgICB1cGRhdGVMZWdhbFJpc2s6IChkYXRhKSA9PlxuICAgICAgICBzZXQoKHN0YXRlKSA9PiAoe1xuICAgICAgICAgIGxlZ2FsUmlzazogeyAuLi5zdGF0ZS5sZWdhbFJpc2ssIC4uLmRhdGEgfVxuICAgICAgICB9KSksXG4gICAgICAgIFxuICAgICAgc2V0TGFuZ3VhZ2U6IChsYW5nKSA9PiBzZXQoeyBjdXJyZW50TGFuZ3VhZ2U6IGxhbmcgfSksXG4gICAgICBzZXRPdXRwdXRGb3JtYXQ6IChmb3JtYXQpID0+IHNldCh7IG91dHB1dEZvcm1hdDogZm9ybWF0IH0pLFxuICAgICAgc2V0U2hvd0FkdmFuY2VkT3B0aW9uczogKHNob3cpID0+IHNldCh7IHNob3dBZHZhbmNlZE9wdGlvbnM6IHNob3cgfSksXG4gICAgICBzZXRBcGlTZXR0aW5nczogKHNldHRpbmdzKSA9PiBzZXQoe1xuICAgICAgICBhcGlTZXR0aW5nczoge1xuICAgICAgICAgIC4uLnNldHRpbmdzLFxuICAgICAgICAgIHByb3ZpZGVyczogc2V0dGluZ3MucHJvdmlkZXJzIHx8IFtdXG4gICAgICAgIH1cbiAgICAgIH0pLFxuXG4gICAgICAvLyBMTE0gUHJvdmlkZXIgbWFuYWdlbWVudCBmdW5jdGlvbnNcbiAgICAgIGFkZFByb3ZpZGVyOiAocHJvdmlkZXIpID0+XG4gICAgICAgIHNldCgoc3RhdGUpID0+IHtcbiAgICAgICAgICBjb25zdCBleGlzdGluZ1Byb3ZpZGVycyA9IHN0YXRlLmFwaVNldHRpbmdzLnByb3ZpZGVycyB8fCBbXTtcbiAgICAgICAgICAvLyDYqtit2YLZgiDZhdmGINi52K/ZhSDZiNis2YjYryDZhdmC2K/ZhSDYrtiv2YXYqSDYqNmG2YHYsyDYp9mE2YXYudix2YFcbiAgICAgICAgICBpZiAoZXhpc3RpbmdQcm92aWRlcnMuZmluZChwID0+IHAuaWQgPT09IHByb3ZpZGVyLmlkKSkge1xuICAgICAgICAgICAgY29uc29sZS53YXJuKGBQcm92aWRlciB3aXRoIGlkICR7cHJvdmlkZXIuaWR9IGFscmVhZHkgZXhpc3RzYCk7XG4gICAgICAgICAgICByZXR1cm4gc3RhdGU7IC8vINmE2Kcg2KrYttmK2YEg2YXZgtiv2YUg2K7Yr9mF2Kkg2YXZg9ix2LFcbiAgICAgICAgICB9XG5cbiAgICAgICAgICAvLyDYpdi22KfZgdipINin2YTZgtmK2YUg2KfZhNin2YHYqtix2KfYttmK2Kkg2YTZhNmF2YrYstin2Kog2KfZhNmF2KrZgtiv2YXYqVxuICAgICAgICAgIGNvbnN0IGVuaGFuY2VkUHJvdmlkZXI6IFByb3ZpZGVyQ29uZmlnID0ge1xuICAgICAgICAgICAgLi4ucHJvdmlkZXIsXG4gICAgICAgICAgICBwcmlvcml0eTogcHJvdmlkZXIucHJpb3JpdHkgfHwgNSxcbiAgICAgICAgICAgIGlzQmFja3VwOiBwcm92aWRlci5pc0JhY2t1cCB8fCBmYWxzZSxcbiAgICAgICAgICAgIG1heFJlcXVlc3RzUGVyTWludXRlOiBwcm92aWRlci5tYXhSZXF1ZXN0c1Blck1pbnV0ZSB8fCA2MCxcbiAgICAgICAgICAgIHRpbWVvdXQ6IHByb3ZpZGVyLnRpbWVvdXQgfHwgMzAsXG4gICAgICAgICAgICByZXRyeUF0dGVtcHRzOiBwcm92aWRlci5yZXRyeUF0dGVtcHRzIHx8IDMsXG4gICAgICAgICAgICBzdGF0czogcHJvdmlkZXIuc3RhdHMgfHwge1xuICAgICAgICAgICAgICB0b3RhbFJlcXVlc3RzOiAwLFxuICAgICAgICAgICAgICBzdWNjZXNzZnVsUmVxdWVzdHM6IDAsXG4gICAgICAgICAgICAgIGZhaWxlZFJlcXVlc3RzOiAwLFxuICAgICAgICAgICAgICBhdmVyYWdlUmVzcG9uc2VUaW1lOiAwLFxuICAgICAgICAgICAgICB0b3RhbFRva2Vuc1VzZWQ6IDAsXG4gICAgICAgICAgICAgIHRvdGFsQ29zdDogMFxuICAgICAgICAgICAgfVxuICAgICAgICAgIH07XG5cbiAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgYXBpU2V0dGluZ3M6IHtcbiAgICAgICAgICAgICAgLi4uc3RhdGUuYXBpU2V0dGluZ3MsXG4gICAgICAgICAgICAgIHByb3ZpZGVyczogWy4uLmV4aXN0aW5nUHJvdmlkZXJzLCBlbmhhbmNlZFByb3ZpZGVyXVxuICAgICAgICAgICAgfVxuICAgICAgICAgIH07XG4gICAgICAgIH0pLFxuXG4gICAgICB1cGRhdGVQcm92aWRlcjogKHByb3ZpZGVySWQsIHVwZGF0ZXMpID0+XG4gICAgICAgIHNldCgoc3RhdGUpID0+ICh7XG4gICAgICAgICAgYXBpU2V0dGluZ3M6IHtcbiAgICAgICAgICAgIC4uLnN0YXRlLmFwaVNldHRpbmdzLFxuICAgICAgICAgICAgcHJvdmlkZXJzOiAoc3RhdGUuYXBpU2V0dGluZ3MucHJvdmlkZXJzIHx8IFtdKS5tYXAocCA9PlxuICAgICAgICAgICAgICBwLmlkID09PSBwcm92aWRlcklkID8geyAuLi5wLCAuLi51cGRhdGVzIH0gOiBwXG4gICAgICAgICAgICApXG4gICAgICAgICAgfVxuICAgICAgICB9KSksXG5cbiAgICAgIHJlbW92ZVByb3ZpZGVyOiAocHJvdmlkZXJJZCkgPT5cbiAgICAgICAgc2V0KChzdGF0ZSkgPT4gKHtcbiAgICAgICAgICBhcGlTZXR0aW5nczoge1xuICAgICAgICAgICAgLi4uc3RhdGUuYXBpU2V0dGluZ3MsXG4gICAgICAgICAgICBwcm92aWRlcnM6IChzdGF0ZS5hcGlTZXR0aW5ncy5wcm92aWRlcnMgfHwgW10pLmZpbHRlcihwID0+IHAuaWQgIT09IHByb3ZpZGVySWQpLFxuICAgICAgICAgICAgZGVmYXVsdFByb3ZpZGVyOiBzdGF0ZS5hcGlTZXR0aW5ncy5kZWZhdWx0UHJvdmlkZXIgPT09IHByb3ZpZGVySWRcbiAgICAgICAgICAgICAgPyB1bmRlZmluZWRcbiAgICAgICAgICAgICAgOiBzdGF0ZS5hcGlTZXR0aW5ncy5kZWZhdWx0UHJvdmlkZXJcbiAgICAgICAgICB9XG4gICAgICAgIH0pKSxcblxuICAgICAgdmFsaWRhdGVQcm92aWRlcjogYXN5bmMgKHByb3ZpZGVySWQpID0+IHtcbiAgICAgICAgY29uc3Qgc3RhdGUgPSBnZXQoKTtcbiAgICAgICAgY29uc3QgcHJvdmlkZXIgPSAoc3RhdGUuYXBpU2V0dGluZ3MucHJvdmlkZXJzIHx8IFtdKS5maW5kKHAgPT4gcC5pZCA9PT0gcHJvdmlkZXJJZCk7XG4gICAgICAgIGlmICghcHJvdmlkZXIpIHJldHVybiBmYWxzZTtcblxuICAgICAgICB0cnkge1xuICAgICAgICAgIC8vIFVwZGF0ZSBzdGF0dXMgdG8gcGVuZGluZ1xuICAgICAgICAgIHN0YXRlLnVwZGF0ZVByb3ZpZGVyKHByb3ZpZGVySWQsIHtcbiAgICAgICAgICAgIHZhbGlkYXRpb25TdGF0dXM6ICdwZW5kaW5nJyxcbiAgICAgICAgICAgIGVycm9yTWVzc2FnZTogdW5kZWZpbmVkXG4gICAgICAgICAgfSk7XG5cbiAgICAgICAgICAvLyBDYWxsIHZhbGlkYXRpb24gQVBJXG4gICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9sbG0vdmFsaWRhdGUnLCB7XG4gICAgICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgICAgIGhlYWRlcnM6IHsgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyB9LFxuICAgICAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICAgICAgICBwcm92aWRlcklkOiBwcm92aWRlci5pZCxcbiAgICAgICAgICAgICAgYXBpS2V5OiBwcm92aWRlci5hcGlLZXksXG4gICAgICAgICAgICAgIGJhc2VVcmw6IHByb3ZpZGVyLmJhc2VVcmxcbiAgICAgICAgICAgIH0pXG4gICAgICAgICAgfSk7XG5cbiAgICAgICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG5cbiAgICAgICAgICBpZiAocmVzdWx0LnZhbGlkKSB7XG4gICAgICAgICAgICBzdGF0ZS51cGRhdGVQcm92aWRlcihwcm92aWRlcklkLCB7XG4gICAgICAgICAgICAgIHZhbGlkYXRpb25TdGF0dXM6ICd2YWxpZCcsXG4gICAgICAgICAgICAgIGxhc3RWYWxpZGF0ZWQ6IG5ldyBEYXRlKCksXG4gICAgICAgICAgICAgIGVycm9yTWVzc2FnZTogdW5kZWZpbmVkLFxuICAgICAgICAgICAgICBpc0VuYWJsZWQ6IHRydWUgLy8g2KrZgdi52YrZhCDYp9mE2YXZgtiv2YUg2KrZhNmC2KfYptmK2KfZiyDYudmG2K8g2YbYrNin2K0g2KfZhNiq2K3ZgtmCXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBzdGF0ZS51cGRhdGVQcm92aWRlcihwcm92aWRlcklkLCB7XG4gICAgICAgICAgICAgIHZhbGlkYXRpb25TdGF0dXM6ICdpbnZhbGlkJyxcbiAgICAgICAgICAgICAgZXJyb3JNZXNzYWdlOiByZXN1bHQuZXJyb3IgfHwgJ1ZhbGlkYXRpb24gZmFpbGVkJ1xuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgICAgfVxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgIHN0YXRlLnVwZGF0ZVByb3ZpZGVyKHByb3ZpZGVySWQsIHtcbiAgICAgICAgICAgIHZhbGlkYXRpb25TdGF0dXM6ICdlcnJvcicsXG4gICAgICAgICAgICBlcnJvck1lc3NhZ2U6IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ1Vua25vd24gZXJyb3InXG4gICAgICAgICAgfSk7XG4gICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICB9XG4gICAgICB9LFxuXG4gICAgICBnZXRQcm92aWRlcjogKHByb3ZpZGVySWQpID0+IHtcbiAgICAgICAgY29uc3Qgc3RhdGUgPSBnZXQoKTtcbiAgICAgICAgcmV0dXJuIChzdGF0ZS5hcGlTZXR0aW5ncy5wcm92aWRlcnMgfHwgW10pLmZpbmQocCA9PiBwLmlkID09PSBwcm92aWRlcklkKTtcbiAgICAgIH0sXG5cbiAgICAgIGdldEFjdGl2ZVByb3ZpZGVyczogKCkgPT4ge1xuICAgICAgICBjb25zdCBzdGF0ZSA9IGdldCgpO1xuICAgICAgICByZXR1cm4gKHN0YXRlLmFwaVNldHRpbmdzLnByb3ZpZGVycyB8fCBbXSkuZmlsdGVyKHAgPT4gcC5pc0VuYWJsZWQpO1xuICAgICAgfSxcblxuICAgICAgc2V0RGVmYXVsdFByb3ZpZGVyOiAocHJvdmlkZXJJZCkgPT5cbiAgICAgICAgc2V0KChzdGF0ZSkgPT4gKHtcbiAgICAgICAgICBhcGlTZXR0aW5nczoge1xuICAgICAgICAgICAgLi4uc3RhdGUuYXBpU2V0dGluZ3MsXG4gICAgICAgICAgICBkZWZhdWx0UHJvdmlkZXI6IHByb3ZpZGVySWRcbiAgICAgICAgICB9XG4gICAgICAgIH0pKSxcblxuICAgICAgLy8g2YXZitiy2KfYqiDZhdiq2YLYr9mF2Kkg2YTZhNmF2LLZiNiv2YrZhlxuICAgICAgZ2V0UHJvdmlkZXJzQnlQcmlvcml0eTogKCkgPT4ge1xuICAgICAgICBjb25zdCBzdGF0ZSA9IGdldCgpO1xuICAgICAgICByZXR1cm4gKHN0YXRlLmFwaVNldHRpbmdzLnByb3ZpZGVycyB8fCBbXSlcbiAgICAgICAgICAuZmlsdGVyKHAgPT4gcC5pc0VuYWJsZWQpXG4gICAgICAgICAgLnNvcnQoKGEsIGIpID0+IChiLnByaW9yaXR5IHx8IDUpIC0gKGEucHJpb3JpdHkgfHwgNSkpO1xuICAgICAgfSxcblxuICAgICAgZ2V0QmFja3VwUHJvdmlkZXJzOiAoKSA9PiB7XG4gICAgICAgIGNvbnN0IHN0YXRlID0gZ2V0KCk7XG4gICAgICAgIHJldHVybiAoc3RhdGUuYXBpU2V0dGluZ3MucHJvdmlkZXJzIHx8IFtdKVxuICAgICAgICAgIC5maWx0ZXIocCA9PiBwLmlzRW5hYmxlZCAmJiBwLmlzQmFja3VwKTtcbiAgICAgIH0sXG5cbiAgICAgIHVwZGF0ZVByb3ZpZGVyU3RhdHM6IChpZCwgc3RhdHMpID0+XG4gICAgICAgIHNldCgoc3RhdGUpID0+IHtcbiAgICAgICAgICBjb25zdCBwcm92aWRlcnMgPSBzdGF0ZS5hcGlTZXR0aW5ncy5wcm92aWRlcnMgfHwgW107XG4gICAgICAgICAgY29uc3QgcHJvdmlkZXJJbmRleCA9IHByb3ZpZGVycy5maW5kSW5kZXgocCA9PiBwLmlkID09PSBpZCk7XG5cbiAgICAgICAgICBpZiAocHJvdmlkZXJJbmRleCAhPT0gLTEpIHtcbiAgICAgICAgICAgIGNvbnN0IHVwZGF0ZWRQcm92aWRlcnMgPSBbLi4ucHJvdmlkZXJzXTtcbiAgICAgICAgICAgIHVwZGF0ZWRQcm92aWRlcnNbcHJvdmlkZXJJbmRleF0gPSB7XG4gICAgICAgICAgICAgIC4uLnVwZGF0ZWRQcm92aWRlcnNbcHJvdmlkZXJJbmRleF0sXG4gICAgICAgICAgICAgIHN0YXRzOiB7XG4gICAgICAgICAgICAgICAgLi4udXBkYXRlZFByb3ZpZGVyc1twcm92aWRlckluZGV4XS5zdGF0cyxcbiAgICAgICAgICAgICAgICAuLi5zdGF0cyxcbiAgICAgICAgICAgICAgICBsYXN0VXNlZDogbmV3IERhdGUoKVxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9O1xuXG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICBhcGlTZXR0aW5nczoge1xuICAgICAgICAgICAgICAgIC4uLnN0YXRlLmFwaVNldHRpbmdzLFxuICAgICAgICAgICAgICAgIHByb3ZpZGVyczogdXBkYXRlZFByb3ZpZGVyc1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9O1xuICAgICAgICAgIH1cbiAgICAgICAgICByZXR1cm4gc3RhdGU7XG4gICAgICAgIH0pLFxuXG4gICAgICBnZXRCZXN0UHJvdmlkZXI6IChjcml0ZXJpYSA9ICdyZWxpYWJpbGl0eScpID0+IHtcbiAgICAgICAgY29uc3Qgc3RhdGUgPSBnZXQoKTtcbiAgICAgICAgY29uc3QgYWN0aXZlUHJvdmlkZXJzID0gKHN0YXRlLmFwaVNldHRpbmdzLnByb3ZpZGVycyB8fCBbXSlcbiAgICAgICAgICAuZmlsdGVyKHAgPT4gcC5pc0VuYWJsZWQgJiYgIXAuaXNCYWNrdXApO1xuXG4gICAgICAgIGlmIChhY3RpdmVQcm92aWRlcnMubGVuZ3RoID09PSAwKSByZXR1cm4gdW5kZWZpbmVkO1xuXG4gICAgICAgIHN3aXRjaCAoY3JpdGVyaWEpIHtcbiAgICAgICAgICBjYXNlICdzcGVlZCc6XG4gICAgICAgICAgICByZXR1cm4gYWN0aXZlUHJvdmlkZXJzLnJlZHVjZSgoYmVzdCwgY3VycmVudCkgPT4ge1xuICAgICAgICAgICAgICBjb25zdCBiZXN0U3BlZWQgPSBiZXN0LnN0YXRzPy5hdmVyYWdlUmVzcG9uc2VUaW1lIHx8IEluZmluaXR5O1xuICAgICAgICAgICAgICBjb25zdCBjdXJyZW50U3BlZWQgPSBjdXJyZW50LnN0YXRzPy5hdmVyYWdlUmVzcG9uc2VUaW1lIHx8IEluZmluaXR5O1xuICAgICAgICAgICAgICByZXR1cm4gY3VycmVudFNwZWVkIDwgYmVzdFNwZWVkID8gY3VycmVudCA6IGJlc3Q7XG4gICAgICAgICAgICB9KTtcblxuICAgICAgICAgIGNhc2UgJ2Nvc3QnOlxuICAgICAgICAgICAgcmV0dXJuIGFjdGl2ZVByb3ZpZGVycy5yZWR1Y2UoKGJlc3QsIGN1cnJlbnQpID0+IHtcbiAgICAgICAgICAgICAgY29uc3QgYmVzdENvc3QgPSBiZXN0LmNvc3RQZXJUb2tlbiB8fCBJbmZpbml0eTtcbiAgICAgICAgICAgICAgY29uc3QgY3VycmVudENvc3QgPSBjdXJyZW50LmNvc3RQZXJUb2tlbiB8fCBJbmZpbml0eTtcbiAgICAgICAgICAgICAgcmV0dXJuIGN1cnJlbnRDb3N0IDwgYmVzdENvc3QgPyBjdXJyZW50IDogYmVzdDtcbiAgICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgY2FzZSAncmVsaWFiaWxpdHknOlxuICAgICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICByZXR1cm4gYWN0aXZlUHJvdmlkZXJzLnJlZHVjZSgoYmVzdCwgY3VycmVudCkgPT4ge1xuICAgICAgICAgICAgICBjb25zdCBiZXN0UmVsaWFiaWxpdHkgPSBiZXN0LnN0YXRzID9cbiAgICAgICAgICAgICAgICAoYmVzdC5zdGF0cy5zdWNjZXNzZnVsUmVxdWVzdHMgLyAoYmVzdC5zdGF0cy50b3RhbFJlcXVlc3RzIHx8IDEpKSA6IDA7XG4gICAgICAgICAgICAgIGNvbnN0IGN1cnJlbnRSZWxpYWJpbGl0eSA9IGN1cnJlbnQuc3RhdHMgP1xuICAgICAgICAgICAgICAgIChjdXJyZW50LnN0YXRzLnN1Y2Nlc3NmdWxSZXF1ZXN0cyAvIChjdXJyZW50LnN0YXRzLnRvdGFsUmVxdWVzdHMgfHwgMSkpIDogMDtcbiAgICAgICAgICAgICAgcmV0dXJuIGN1cnJlbnRSZWxpYWJpbGl0eSA+IGJlc3RSZWxpYWJpbGl0eSA/IGN1cnJlbnQgOiBiZXN0O1xuICAgICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgIH0sXG5cbiAgICAgIHJlc2V0QWxsOiAoKSA9PiBzZXQoaW5pdGlhbFN0YXRlKSxcblxuICAgICAgLy8g2YXYs9itINis2YXZiti5INin2YTYpdis2KfYqNin2Kog2YHZgti3ICjYp9mE2KfYrdiq2YHYp9i4INio2KfZhNil2LnYr9in2K/Yp9iqKVxuICAgICAgY2xlYXJBbGxBbnN3ZXJzOiAoKSA9PiBzZXQoKHN0YXRlKSA9PiAoe1xuICAgICAgICAuLi5zdGF0ZSxcbiAgICAgICAgcHJvamVjdERlZmluaXRpb246IHtcbiAgICAgICAgICBuYW1lOiAnJyxcbiAgICAgICAgICBwdXJwb3NlOiAnJyxcbiAgICAgICAgICB0YXJnZXRVc2VyczogJycsXG4gICAgICAgICAgZ29hbHM6ICcnLFxuICAgICAgICAgIHNjb3BlOiAnJyxcbiAgICAgICAgICB0aW1lbGluZTogJydcbiAgICAgICAgfSxcbiAgICAgICAgY29udGV4dE1hcDoge1xuICAgICAgICAgIHRpbWVDb250ZXh0OiAnJyxcbiAgICAgICAgICBsYW5ndWFnZTogJycsXG4gICAgICAgICAgbG9jYXRpb246ICcnLFxuICAgICAgICAgIGN1bHR1cmFsQ29udGV4dDogJycsXG4gICAgICAgICAgYmVoYXZpb3JhbEFzcGVjdHM6ICcnLFxuICAgICAgICAgIGVudmlyb25tZW50YWxGYWN0b3JzOiAnJ1xuICAgICAgICB9LFxuICAgICAgICBlbW90aW9uYWxUb25lOiB7XG4gICAgICAgICAgcGVyc29uYWxpdHk6ICcnLFxuICAgICAgICAgIGNvbW11bmljYXRpb25TdHlsZTogJycsXG4gICAgICAgICAgdXNlckV4cGVyaWVuY2U6ICcnLFxuICAgICAgICAgIGJyYW5kVm9pY2U6ICcnLFxuICAgICAgICAgIGVtb3Rpb25hbEludGVsbGlnZW5jZTogJycsXG4gICAgICAgICAgaW50ZXJhY3Rpb25GbG93OiAnJ1xuICAgICAgICB9LFxuICAgICAgICB0ZWNobmljYWxMYXllcjoge1xuICAgICAgICAgIHByb2dyYW1taW5nTGFuZ3VhZ2VzOiAnJyxcbiAgICAgICAgICBmcmFtZXdvcmtzOiAnJyxcbiAgICAgICAgICBsbG1Nb2RlbHM6ICcnLFxuICAgICAgICAgIGRhdGFiYXNlczogJycsXG4gICAgICAgICAgYXBpczogJycsXG4gICAgICAgICAgaW5mcmFzdHJ1Y3R1cmU6ICcnXG4gICAgICAgIH0sXG4gICAgICAgIGxlZ2FsUmlzazoge1xuICAgICAgICAgIHByaXZhY3lDb25jZXJuczogJycsXG4gICAgICAgICAgZGF0YVByb3RlY3Rpb246ICcnLFxuICAgICAgICAgIGNvbXBsaWFuY2U6ICcnLFxuICAgICAgICAgIHJpc2tzOiAnJyxcbiAgICAgICAgICBtaXRpZ2F0aW9uOiAnJyxcbiAgICAgICAgICBldGhpY2FsQ29uc2lkZXJhdGlvbnM6ICcnXG4gICAgICAgIH1cbiAgICAgIH0pKSxcbiAgICAgIFxuICAgICAgZ2V0TW9kdWxlRGF0YTogKG1vZHVsZSkgPT4ge1xuICAgICAgICBjb25zdCBzdGF0ZSA9IGdldCgpO1xuICAgICAgICBzd2l0Y2ggKG1vZHVsZSkge1xuICAgICAgICAgIGNhc2UgJ3Byb2plY3QnOiByZXR1cm4gc3RhdGUucHJvamVjdERlZmluaXRpb247XG4gICAgICAgICAgY2FzZSAnY29udGV4dCc6IHJldHVybiBzdGF0ZS5jb250ZXh0TWFwO1xuICAgICAgICAgIGNhc2UgJ2Vtb3Rpb25hbCc6IHJldHVybiBzdGF0ZS5lbW90aW9uYWxUb25lO1xuICAgICAgICAgIGNhc2UgJ3RlY2huaWNhbCc6IHJldHVybiBzdGF0ZS50ZWNobmljYWxMYXllcjtcbiAgICAgICAgICBjYXNlICdsZWdhbCc6IHJldHVybiBzdGF0ZS5sZWdhbFJpc2s7XG4gICAgICAgICAgZGVmYXVsdDogcmV0dXJuIHt9O1xuICAgICAgICB9XG4gICAgICB9LFxuICAgICAgXG4gICAgICBnZXRBbGxEYXRhOiAoKSA9PiB7XG4gICAgICAgIGNvbnN0IHN0YXRlID0gZ2V0KCk7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgcHJvamVjdERlZmluaXRpb246IHN0YXRlLnByb2plY3REZWZpbml0aW9uLFxuICAgICAgICAgIGNvbnRleHRNYXA6IHN0YXRlLmNvbnRleHRNYXAsXG4gICAgICAgICAgZW1vdGlvbmFsVG9uZTogc3RhdGUuZW1vdGlvbmFsVG9uZSxcbiAgICAgICAgICB0ZWNobmljYWxMYXllcjogc3RhdGUudGVjaG5pY2FsTGF5ZXIsXG4gICAgICAgICAgbGVnYWxSaXNrOiBzdGF0ZS5sZWdhbFJpc2tcbiAgICAgICAgfTtcbiAgICAgIH1cbiAgICB9KSxcbiAgICB7XG4gICAgICBuYW1lOiAnY29udGV4dGtpdC1zdG9yYWdlJyxcbiAgICAgIHZlcnNpb246IDEsXG4gICAgICBza2lwSHlkcmF0aW9uOiB0cnVlXG4gICAgfVxuICApXG4pO1xuIl0sIm5hbWVzIjpbImNyZWF0ZSIsInBlcnNpc3QiLCJpbml0aWFsU3RhdGUiLCJwcm9qZWN0RGVmaW5pdGlvbiIsIm5hbWUiLCJwdXJwb3NlIiwidGFyZ2V0VXNlcnMiLCJnb2FscyIsInNjb3BlIiwidGltZWxpbmUiLCJwcm9qZWN0VHlwZSIsInByb2plY3ROYXR1cmUiLCJnZW9ncmFwaGljUmVnaW9uIiwidGFyZ2V0UGxhdGZvcm1zIiwicHJpbWFyeUxhbmd1YWdlcyIsImNvbXBsZXhpdHkiLCJidWRnZXQiLCJ0ZWFtU2l6ZSIsImRlcGxveW1lbnRUeXBlIiwiY29udGV4dE1hcCIsInRpbWVDb250ZXh0IiwibGFuZ3VhZ2UiLCJsb2NhdGlvbiIsImN1bHR1cmFsQ29udGV4dCIsImJlaGF2aW9yYWxBc3BlY3RzIiwiZW52aXJvbm1lbnRhbEZhY3RvcnMiLCJlbW90aW9uYWxUb25lIiwicGVyc29uYWxpdHkiLCJjb21tdW5pY2F0aW9uU3R5bGUiLCJ1c2VyRXhwZXJpZW5jZSIsImJyYW5kVm9pY2UiLCJlbW90aW9uYWxJbnRlbGxpZ2VuY2UiLCJpbnRlcmFjdGlvbkZsb3ciLCJ0ZWNobmljYWxMYXllciIsInByb2dyYW1taW5nTGFuZ3VhZ2VzIiwiZnJhbWV3b3JrcyIsImxsbU1vZGVscyIsImRhdGFiYXNlcyIsImFwaXMiLCJpbmZyYXN0cnVjdHVyZSIsImFyY2hpdGVjdHVyZVBhdHRlcm4iLCJzY2FsaW5nU3RyYXRlZ3kiLCJzZWN1cml0eVJlcXVpcmVtZW50cyIsInBlcmZvcm1hbmNlVGFyZ2V0cyIsImludGVncmF0aW9uTmVlZHMiLCJtb25pdG9yaW5nVG9vbHMiLCJsZWdhbFJpc2siLCJwcml2YWN5Q29uY2VybnMiLCJkYXRhUHJvdGVjdGlvbiIsImNvbXBsaWFuY2UiLCJyaXNrcyIsIm1pdGlnYXRpb24iLCJldGhpY2FsQ29uc2lkZXJhdGlvbnMiLCJjdXJyZW50TGFuZ3VhZ2UiLCJvdXRwdXRGb3JtYXQiLCJzaG93QWR2YW5jZWRPcHRpb25zIiwiYXBpU2V0dGluZ3MiLCJwcm92aWRlcnMiLCJnbG9iYWxTZXR0aW5ncyIsInRlbXBlcmF0dXJlIiwidG9wUCIsIm1heFRva2VucyIsInRpbWVvdXQiLCJvcGVuYWlBcGlLZXkiLCJvcGVucm91dGVyQXBpS2V5IiwiY3VzdG9tTW9kZWxzIiwidXNlQ29udGV4dFN0b3JlIiwic2V0IiwiZ2V0IiwidXBkYXRlUHJvamVjdERlZmluaXRpb24iLCJkYXRhIiwic3RhdGUiLCJ1cGRhdGVDb250ZXh0TWFwIiwidXBkYXRlRW1vdGlvbmFsVG9uZSIsInVwZGF0ZVRlY2huaWNhbExheWVyIiwidXBkYXRlTGVnYWxSaXNrIiwic2V0TGFuZ3VhZ2UiLCJsYW5nIiwic2V0T3V0cHV0Rm9ybWF0IiwiZm9ybWF0Iiwic2V0U2hvd0FkdmFuY2VkT3B0aW9ucyIsInNob3ciLCJzZXRBcGlTZXR0aW5ncyIsInNldHRpbmdzIiwiYWRkUHJvdmlkZXIiLCJwcm92aWRlciIsImV4aXN0aW5nUHJvdmlkZXJzIiwiZmluZCIsInAiLCJpZCIsImNvbnNvbGUiLCJ3YXJuIiwiZW5oYW5jZWRQcm92aWRlciIsInByaW9yaXR5IiwiaXNCYWNrdXAiLCJtYXhSZXF1ZXN0c1Blck1pbnV0ZSIsInJldHJ5QXR0ZW1wdHMiLCJzdGF0cyIsInRvdGFsUmVxdWVzdHMiLCJzdWNjZXNzZnVsUmVxdWVzdHMiLCJmYWlsZWRSZXF1ZXN0cyIsImF2ZXJhZ2VSZXNwb25zZVRpbWUiLCJ0b3RhbFRva2Vuc1VzZWQiLCJ0b3RhbENvc3QiLCJ1cGRhdGVQcm92aWRlciIsInByb3ZpZGVySWQiLCJ1cGRhdGVzIiwibWFwIiwicmVtb3ZlUHJvdmlkZXIiLCJmaWx0ZXIiLCJkZWZhdWx0UHJvdmlkZXIiLCJ1bmRlZmluZWQiLCJ2YWxpZGF0ZVByb3ZpZGVyIiwidmFsaWRhdGlvblN0YXR1cyIsImVycm9yTWVzc2FnZSIsInJlc3BvbnNlIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJhcGlLZXkiLCJiYXNlVXJsIiwicmVzdWx0IiwianNvbiIsInZhbGlkIiwibGFzdFZhbGlkYXRlZCIsIkRhdGUiLCJpc0VuYWJsZWQiLCJlcnJvciIsIkVycm9yIiwibWVzc2FnZSIsImdldFByb3ZpZGVyIiwiZ2V0QWN0aXZlUHJvdmlkZXJzIiwic2V0RGVmYXVsdFByb3ZpZGVyIiwiZ2V0UHJvdmlkZXJzQnlQcmlvcml0eSIsInNvcnQiLCJhIiwiYiIsImdldEJhY2t1cFByb3ZpZGVycyIsInVwZGF0ZVByb3ZpZGVyU3RhdHMiLCJwcm92aWRlckluZGV4IiwiZmluZEluZGV4IiwidXBkYXRlZFByb3ZpZGVycyIsImxhc3RVc2VkIiwiZ2V0QmVzdFByb3ZpZGVyIiwiY3JpdGVyaWEiLCJhY3RpdmVQcm92aWRlcnMiLCJsZW5ndGgiLCJyZWR1Y2UiLCJiZXN0IiwiY3VycmVudCIsImJlc3RTcGVlZCIsIkluZmluaXR5IiwiY3VycmVudFNwZWVkIiwiYmVzdENvc3QiLCJjb3N0UGVyVG9rZW4iLCJjdXJyZW50Q29zdCIsImJlc3RSZWxpYWJpbGl0eSIsImN1cnJlbnRSZWxpYWJpbGl0eSIsInJlc2V0QWxsIiwiY2xlYXJBbGxBbnN3ZXJzIiwiZ2V0TW9kdWxlRGF0YSIsIm1vZHVsZSIsImdldEFsbERhdGEiLCJ2ZXJzaW9uIiwic2tpcEh5ZHJhdGlvbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/store/contextStore.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"22d6d015889c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29udGV4dGtpdC8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/N2UzNSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjIyZDZkMDE1ODg5Y1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/styles/glass-effects.css":
/*!**************************************!*\
  !*** ./src/styles/glass-effects.css ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5e0b1f04c4ea\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL2dsYXNzLWVmZmVjdHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29udGV4dGtpdC8uL3NyYy9zdHlsZXMvZ2xhc3MtZWZmZWN0cy5jc3M/NWRiMyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjVlMGIxZjA0YzRlYVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/glass-effects.css\n");

/***/ }),

/***/ "(rsc)/./src/styles/text-improvements.css":
/*!******************************************!*\
  !*** ./src/styles/text-improvements.css ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"757aecea32c0\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL3RleHQtaW1wcm92ZW1lbnRzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbnRleHRraXQvLi9zcmMvc3R5bGVzL3RleHQtaW1wcm92ZW1lbnRzLmNzcz8wYmQxIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNzU3YWVjZWEzMmMwXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/text-improvements.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _styles_glass_effects_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../styles/glass-effects.css */ \"(rsc)/./src/styles/glass-effects.css\");\n/* harmony import */ var _styles_text_improvements_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../styles/text-improvements.css */ \"(rsc)/./src/styles/text-improvements.css\");\n/* harmony import */ var _components_ThemeProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ThemeProvider */ \"(rsc)/./src/components/ThemeProvider.tsx\");\n/* harmony import */ var _components_StoreHydration__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/StoreHydration */ \"(rsc)/./src/components/StoreHydration.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"Craftery - AI-powered Idea Builder\",\n    description: \"AI-powered platform for building and developing creative ideas\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"antialiased font-arabic\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeProvider__WEBPACK_IMPORTED_MODULE_4__.ThemeProvider, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_StoreHydration__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this),\n                        children\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\app\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\app\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/StoreHydration.tsx":
/*!*******************************************!*\
  !*** ./src/components/StoreHydration.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\StoreHydration.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\StoreHydration.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/ThemeProvider.tsx":
/*!******************************************!*\
  !*** ./src/components/ThemeProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0),
/* harmony export */   useTheme: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\ThemeProvider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\ThemeProvider.tsx#ThemeProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\ThemeProvider.tsx#useTheme`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/zustand","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();