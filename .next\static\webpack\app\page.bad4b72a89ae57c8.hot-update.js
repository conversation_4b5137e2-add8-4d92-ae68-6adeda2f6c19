"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/projectOptions.ts":
/*!***********************************!*\
  !*** ./src/lib/projectOptions.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BUDGET_RANGES: function() { return /* binding */ BUDGET_RANGES; },\n/* harmony export */   COMPLEXITY_LEVELS: function() { return /* binding */ COMPLEXITY_LEVELS; },\n/* harmony export */   DEPLOYMENT_TYPES: function() { return /* binding */ DEPLOYMENT_TYPES; },\n/* harmony export */   GEOGRAPHIC_REGIONS: function() { return /* binding */ GEOGRAPHIC_REGIONS; },\n/* harmony export */   PROGRAMMING_LANGUAGES: function() { return /* binding */ PROGRAMMING_LANGUAGES; },\n/* harmony export */   PROJECT_TYPES: function() { return /* binding */ PROJECT_TYPES; },\n/* harmony export */   TARGET_PLATFORMS: function() { return /* binding */ TARGET_PLATFORMS; },\n/* harmony export */   TEAM_SIZES: function() { return /* binding */ TEAM_SIZES; }\n/* harmony export */ });\n// خيارات التخصيص المتقدمة للمشاريع\n// أنواع المشاريع\nconst PROJECT_TYPES = [\n    {\n        id: \"web-app\",\n        label: \"Web Application\",\n        labelAr: \"تطبيق ويب\",\n        description: \"Browser-based application accessible via web browsers\",\n        descriptionAr: \"تطبيق يعمل في المتصفح ويمكن الوصول إليه عبر متصفحات الويب\",\n        icon: \"\\uD83C\\uDF10\"\n    },\n    {\n        id: \"mobile-app\",\n        label: \"Mobile Application\",\n        labelAr: \"تطبيق جوال\",\n        description: \"Native or cross-platform mobile application\",\n        descriptionAr: \"تطبيق جوال أصلي أو متعدد المنصات\",\n        icon: \"\\uD83D\\uDCF1\"\n    },\n    {\n        id: \"desktop-app\",\n        label: \"Desktop Application\",\n        labelAr: \"تطبيق سطح المكتب\",\n        description: \"Native desktop application for Windows, macOS, or Linux\",\n        descriptionAr: \"تطبيق سطح مكتب أصلي لـ Windows أو macOS أو Linux\",\n        icon: \"\\uD83D\\uDCBB\"\n    },\n    {\n        id: \"api-service\",\n        label: \"API/Microservice\",\n        labelAr: \"خدمة API/مايكروسيرفس\",\n        description: \"Backend API or microservice architecture\",\n        descriptionAr: \"خدمة API خلفية أو هندسة مايكروسيرفس\",\n        icon: \"\\uD83D\\uDD0C\"\n    },\n    {\n        id: \"ai-model\",\n        label: \"AI/ML Model\",\n        labelAr: \"نموذج ذكاء اصطناعي\",\n        description: \"Machine learning model or AI system\",\n        descriptionAr: \"نموذج تعلم آلي أو نظام ذكاء اصطناعي\",\n        icon: \"\\uD83E\\uDD16\"\n    },\n    {\n        id: \"chatbot\",\n        label: \"Chatbot/Virtual Assistant\",\n        labelAr: \"شات بوت/مساعد افتراضي\",\n        description: \"Conversational AI or chatbot system\",\n        descriptionAr: \"نظام ذكاء اصطناعي محادثة أو شات بوت\",\n        icon: \"\\uD83D\\uDCAC\"\n    },\n    {\n        id: \"data-analytics\",\n        label: \"Data Analytics Platform\",\n        labelAr: \"منصة تحليل البيانات\",\n        description: \"Data processing and analytics solution\",\n        descriptionAr: \"حل معالجة وتحليل البيانات\",\n        icon: \"\\uD83D\\uDCCA\"\n    },\n    {\n        id: \"iot-system\",\n        label: \"IoT System\",\n        labelAr: \"نظام إنترنت الأشياء\",\n        description: \"Internet of Things connected system\",\n        descriptionAr: \"نظام متصل بإنترنت الأشياء\",\n        icon: \"\\uD83C\\uDF10\"\n    }\n];\n// المنصات المستهدفة\nconst TARGET_PLATFORMS = [\n    {\n        id: \"web-browsers\",\n        label: \"Web Browsers\",\n        labelAr: \"متصفحات الويب\",\n        description: \"Chrome, Firefox, Safari, Edge\",\n        descriptionAr: \"كروم، فايرفوكس، سفاري، إيدج\",\n        icon: \"\\uD83C\\uDF10\"\n    },\n    {\n        id: \"android\",\n        label: \"Android\",\n        labelAr: \"أندرويد\",\n        description: \"Android mobile devices\",\n        descriptionAr: \"أجهزة أندرويد المحمولة\",\n        icon: \"\\uD83E\\uDD16\"\n    },\n    {\n        id: \"ios\",\n        label: \"iOS\",\n        labelAr: \"iOS\",\n        description: \"iPhone and iPad devices\",\n        descriptionAr: \"أجهزة آيفون وآيباد\",\n        icon: \"\\uD83C\\uDF4E\"\n    },\n    {\n        id: \"windows\",\n        label: \"Windows\",\n        labelAr: \"ويندوز\",\n        description: \"Windows desktop and server\",\n        descriptionAr: \"سطح مكتب وخادم ويندوز\",\n        icon: \"\\uD83E\\uDE9F\"\n    },\n    {\n        id: \"macos\",\n        label: \"macOS\",\n        labelAr: \"macOS\",\n        description: \"Apple macOS desktop\",\n        descriptionAr: \"سطح مكتب آبل macOS\",\n        icon: \"\\uD83C\\uDF4E\"\n    },\n    {\n        id: \"linux\",\n        label: \"Linux\",\n        labelAr: \"لينكس\",\n        description: \"Linux distributions\",\n        descriptionAr: \"توزيعات لينكس\",\n        icon: \"\\uD83D\\uDC27\"\n    },\n    {\n        id: \"cloud\",\n        label: \"Cloud Platforms\",\n        labelAr: \"المنصات السحابية\",\n        description: \"AWS, Azure, Google Cloud\",\n        descriptionAr: \"AWS، Azure، Google Cloud\",\n        icon: \"☁️\"\n    },\n    {\n        id: \"embedded\",\n        label: \"Embedded Systems\",\n        labelAr: \"الأنظمة المدمجة\",\n        description: \"IoT devices, microcontrollers\",\n        descriptionAr: \"أجهزة إنترنت الأشياء، المتحكمات الدقيقة\",\n        icon: \"\\uD83D\\uDD27\"\n    }\n];\n// لغات البرمجة الأساسية\nconst PROGRAMMING_LANGUAGES = [\n    {\n        id: \"javascript\",\n        label: \"JavaScript/TypeScript\",\n        labelAr: \"جافا سكريبت/تايب سكريبت\",\n        description: \"Modern web development\",\n        descriptionAr: \"تطوير الويب الحديث\",\n        icon: \"\\uD83D\\uDFE8\"\n    },\n    {\n        id: \"python\",\n        label: \"Python\",\n        labelAr: \"بايثون\",\n        description: \"AI/ML, backend, data science\",\n        descriptionAr: \"ذكاء اصطناعي، خلفية، علوم البيانات\",\n        icon: \"\\uD83D\\uDC0D\"\n    },\n    {\n        id: \"java\",\n        label: \"Java\",\n        labelAr: \"جافا\",\n        description: \"Enterprise applications, Android\",\n        descriptionAr: \"تطبيقات المؤسسات، أندرويد\",\n        icon: \"☕\"\n    },\n    {\n        id: \"csharp\",\n        label: \"C#\",\n        labelAr: \"سي شارب\",\n        description: \".NET ecosystem, Windows apps\",\n        descriptionAr: \"نظام .NET، تطبيقات ويندوز\",\n        icon: \"\\uD83D\\uDD37\"\n    },\n    {\n        id: \"swift\",\n        label: \"Swift\",\n        labelAr: \"سويفت\",\n        description: \"iOS and macOS development\",\n        descriptionAr: \"تطوير iOS و macOS\",\n        icon: \"\\uD83E\\uDD89\"\n    },\n    {\n        id: \"kotlin\",\n        label: \"Kotlin\",\n        labelAr: \"كوتلن\",\n        description: \"Android development, JVM\",\n        descriptionAr: \"تطوير أندرويد، JVM\",\n        icon: \"\\uD83D\\uDFE3\"\n    },\n    {\n        id: \"rust\",\n        label: \"Rust\",\n        labelAr: \"رست\",\n        description: \"System programming, performance\",\n        descriptionAr: \"برمجة الأنظمة، الأداء\",\n        icon: \"\\uD83E\\uDD80\"\n    },\n    {\n        id: \"go\",\n        label: \"Go\",\n        labelAr: \"جو\",\n        description: \"Backend services, microservices\",\n        descriptionAr: \"خدمات خلفية، مايكروسيرفس\",\n        icon: \"\\uD83D\\uDC39\"\n    },\n    {\n        id: \"cpp\",\n        label: \"C++\",\n        labelAr: \"سي++\",\n        description: \"High-performance applications\",\n        descriptionAr: \"تطبيقات عالية الأداء\",\n        icon: \"⚡\"\n    },\n    {\n        id: \"php\",\n        label: \"PHP\",\n        labelAr: \"PHP\",\n        description: \"Web development, server-side\",\n        descriptionAr: \"تطوير الويب، جانب الخادم\",\n        icon: \"\\uD83D\\uDC18\"\n    },\n    {\n        id: \"ruby\",\n        label: \"Ruby\",\n        labelAr: \"روبي\",\n        description: \"Web applications, rapid development\",\n        descriptionAr: \"تطبيقات الويب، التطوير السريع\",\n        icon: \"\\uD83D\\uDC8E\"\n    },\n    {\n        id: \"dart\",\n        label: \"Dart/Flutter\",\n        labelAr: \"دارت/فلاتر\",\n        description: \"Cross-platform mobile apps\",\n        descriptionAr: \"تطبيقات جوال متعددة المنصات\",\n        icon: \"\\uD83C\\uDFAF\"\n    }\n];\n// مستويات التعقيد\nconst COMPLEXITY_LEVELS = [\n    {\n        id: \"simple\",\n        label: \"Simple\",\n        labelAr: \"بسيط\",\n        description: \"Basic functionality, minimal features\",\n        descriptionAr: \"وظائف أساسية، ميزات قليلة\",\n        icon: \"\\uD83D\\uDFE2\"\n    },\n    {\n        id: \"moderate\",\n        label: \"Moderate\",\n        labelAr: \"متوسط\",\n        description: \"Standard features, some integrations\",\n        descriptionAr: \"ميزات قياسية، بعض التكاملات\",\n        icon: \"\\uD83D\\uDFE1\"\n    },\n    {\n        id: \"complex\",\n        label: \"Complex\",\n        labelAr: \"معقد\",\n        description: \"Advanced features, multiple integrations\",\n        descriptionAr: \"ميزات متقدمة، تكاملات متعددة\",\n        icon: \"\\uD83D\\uDFE0\"\n    },\n    {\n        id: \"enterprise\",\n        label: \"Enterprise\",\n        labelAr: \"مؤسسي\",\n        description: \"Large-scale, high availability, security\",\n        descriptionAr: \"واسع النطاق، توفر عالي، أمان\",\n        icon: \"\\uD83D\\uDD34\"\n    }\n];\n// نطاقات الميزانية\nconst BUDGET_RANGES = [\n    {\n        id: \"startup\",\n        label: \"Startup Budget\",\n        labelAr: \"ميزانية ناشئة\",\n        description: \"Under $10K\",\n        descriptionAr: \"أقل من 10 آلاف دولار\",\n        icon: \"\\uD83D\\uDCB0\"\n    },\n    {\n        id: \"small\",\n        label: \"Small Project\",\n        labelAr: \"مشروع صغير\",\n        description: \"$10K - $50K\",\n        descriptionAr: \"10-50 ألف دولار\",\n        icon: \"\\uD83D\\uDCB5\"\n    },\n    {\n        id: \"medium\",\n        label: \"Medium Project\",\n        labelAr: \"مشروع متوسط\",\n        description: \"$50K - $200K\",\n        descriptionAr: \"50-200 ألف دولار\",\n        icon: \"\\uD83D\\uDCB8\"\n    },\n    {\n        id: \"large\",\n        label: \"Large Project\",\n        labelAr: \"مشروع كبير\",\n        description: \"$200K - $1M\",\n        descriptionAr: \"200 ألف - مليون دولار\",\n        icon: \"\\uD83D\\uDC8E\"\n    },\n    {\n        id: \"enterprise\",\n        label: \"Enterprise\",\n        labelAr: \"مؤسسي\",\n        description: \"$1M+\",\n        descriptionAr: \"أكثر من مليون دولار\",\n        icon: \"\\uD83C\\uDFE6\"\n    }\n];\n// أحجام الفريق\nconst TEAM_SIZES = [\n    {\n        id: \"solo\",\n        label: \"Solo Developer\",\n        labelAr: \"مطور منفرد\",\n        description: \"1 person\",\n        descriptionAr: \"شخص واحد\",\n        icon: \"\\uD83D\\uDC64\"\n    },\n    {\n        id: \"small\",\n        label: \"Small Team\",\n        labelAr: \"فريق صغير\",\n        description: \"2-5 people\",\n        descriptionAr: \"2-5 أشخاص\",\n        icon: \"\\uD83D\\uDC65\"\n    },\n    {\n        id: \"medium\",\n        label: \"Medium Team\",\n        labelAr: \"فريق متوسط\",\n        description: \"6-15 people\",\n        descriptionAr: \"6-15 شخص\",\n        icon: \"\\uD83D\\uDC68‍\\uD83D\\uDC69‍\\uD83D\\uDC67‍\\uD83D\\uDC66\"\n    },\n    {\n        id: \"large\",\n        label: \"Large Team\",\n        labelAr: \"فريق كبير\",\n        description: \"16-50 people\",\n        descriptionAr: \"16-50 شخص\",\n        icon: \"\\uD83C\\uDFE2\"\n    },\n    {\n        id: \"enterprise\",\n        label: \"Enterprise Team\",\n        labelAr: \"فريق مؤسسي\",\n        description: \"50+ people\",\n        descriptionAr: \"أكثر من 50 شخص\",\n        icon: \"\\uD83C\\uDFED\"\n    }\n];\n// أنواع النشر\nconst DEPLOYMENT_TYPES = [\n    {\n        id: \"cloud\",\n        label: \"Cloud Deployment\",\n        labelAr: \"نشر سحابي\",\n        description: \"AWS, Azure, Google Cloud, Vercel\",\n        descriptionAr: \"AWS، Azure، Google Cloud، Vercel\",\n        icon: \"☁️\"\n    },\n    {\n        id: \"on-premise\",\n        label: \"On-Premise\",\n        labelAr: \"محلي\",\n        description: \"Self-hosted infrastructure\",\n        descriptionAr: \"بنية تحتية ذاتية الاستضافة\",\n        icon: \"\\uD83C\\uDFE2\"\n    },\n    {\n        id: \"hybrid\",\n        label: \"Hybrid\",\n        labelAr: \"هجين\",\n        description: \"Mix of cloud and on-premise\",\n        descriptionAr: \"مزيج من السحابي والمحلي\",\n        icon: \"\\uD83D\\uDD04\"\n    },\n    {\n        id: \"edge\",\n        label: \"Edge Computing\",\n        labelAr: \"حوسبة الحافة\",\n        description: \"Distributed edge deployment\",\n        descriptionAr: \"نشر موزع على الحافة\",\n        icon: \"\\uD83C\\uDF10\"\n    },\n    {\n        id: \"mobile-stores\",\n        label: \"App Stores\",\n        labelAr: \"متاجر التطبيقات\",\n        description: \"Google Play, App Store\",\n        descriptionAr: \"جوجل بلاي، آب ستور\",\n        icon: \"\\uD83D\\uDCF1\"\n    }\n];\n// المناطق الجغرافية والدول\nconst GEOGRAPHIC_REGIONS = [\n    {\n        id: \"morocco\",\n        label: \"Morocco\",\n        labelAr: \"المغرب\",\n        description: \"Kingdom of Morocco - North Africa\",\n        descriptionAr: \"المملكة المغربية - شمال أفريقيا\",\n        icon: \"\\uD83C\\uDDF2\\uD83C\\uDDE6\"\n    },\n    {\n        id: \"middle-east\",\n        label: \"Middle East\",\n        labelAr: \"الشرق الأوسط\",\n        description: \"Middle Eastern countries and regions\",\n        descriptionAr: \"دول ومناطق الشرق الأوسط\",\n        icon: \"\\uD83D\\uDD4C\"\n    },\n    {\n        id: \"north-africa\",\n        label: \"North Africa\",\n        labelAr: \"شمال أفريقيا\",\n        description: \"Northern African countries\",\n        descriptionAr: \"دول شمال أفريقيا\",\n        icon: \"\\uD83C\\uDFDC️\"\n    },\n    {\n        id: \"africa\",\n        label: \"Africa\",\n        labelAr: \"أفريقيا\",\n        description: \"African continent\",\n        descriptionAr: \"القارة الأفريقية\",\n        icon: \"\\uD83C\\uDF0D\"\n    },\n    {\n        id: \"arab-world\",\n        label: \"Arab World\",\n        labelAr: \"العالم العربي\",\n        description: \"Arabic-speaking countries and regions\",\n        descriptionAr: \"الدول والمناطق الناطقة بالعربية\",\n        icon: \"\\uD83C\\uDF19\"\n    },\n    {\n        id: \"europe\",\n        label: \"Europe\",\n        labelAr: \"أوروبا\",\n        description: \"European countries\",\n        descriptionAr: \"الدول الأوروبية\",\n        icon: \"\\uD83C\\uDDEA\\uD83C\\uDDFA\"\n    },\n    {\n        id: \"north-america\",\n        label: \"North America\",\n        labelAr: \"أمريكا الشمالية\",\n        description: \"United States, Canada, and Mexico\",\n        descriptionAr: \"الولايات المتحدة وكندا والمكسيك\",\n        icon: \"\\uD83C\\uDF0E\"\n    },\n    {\n        id: \"asia\",\n        label: \"Asia\",\n        labelAr: \"آسيا\",\n        description: \"Asian countries and regions\",\n        descriptionAr: \"الدول والمناطق الآسيوية\",\n        icon: \"\\uD83C\\uDF0F\"\n    },\n    {\n        id: \"global\",\n        label: \"Global/Worldwide\",\n        labelAr: \"عالمي/في جميع أنحاء العالم\",\n        description: \"Worldwide coverage and availability\",\n        descriptionAr: \"تغطية وتوفر عالمي\",\n        icon: \"\\uD83C\\uDF10\"\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/projectOptions.ts\n"));

/***/ })

});