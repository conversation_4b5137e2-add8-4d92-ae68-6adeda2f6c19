"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/project-definition/page",{

/***/ "(app-pages-browser)/./src/components/SmartFieldAssistant.tsx":
/*!************************************************!*\
  !*** ./src/components/SmartFieldAssistant.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SmartFieldAssistant; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Sparkles,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Sparkles,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Sparkles,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wand-sparkles.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction SmartFieldAssistant(param) {\n    let { fieldName, fieldValue, onValueChange, placeholder, context, className = \"\" } = param;\n    _s();\n    const { currentLanguage, getActiveProviders, getAllData } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore)();\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [generatedSuggestions, setGeneratedSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showSuggestions, setShowSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [copiedIndex, setCopiedIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeProviders, setActiveProviders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isArabic = currentLanguage === \"ar\";\n    // تجنب مشاكل الهيدريشن\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n        setActiveProviders(getActiveProviders());\n    }, [\n        getActiveProviders\n    ]);\n    // تحقق من وجود مقدم خدمة صالح\n    const hasValidProvider = mounted && activeProviders.some((p)=>p.apiKey && p.validationStatus === \"valid\" && p.selectedModels && p.selectedModels.length > 0);\n    const translations = {\n        generateWithAI: isArabic ? \"\\uD83D\\uDCC4 توليد بالذكاء الاصطناعي\" : \"\\uD83D\\uDCC4 Generate with AI\",\n        generating: isArabic ? \"جاري التوليد...\" : \"Generating...\",\n        suggestions: isArabic ? \"اقتراحات ذكية\" : \"Smart Suggestions\",\n        useThis: isArabic ? \"استخدام هذا\" : \"Use This\",\n        copy: isArabic ? \"نسخ\" : \"Copy\",\n        copied: isArabic ? \"تم النسخ\" : \"Copied\",\n        noProviders: isArabic ? \"يرجى إعداد مقدم خدمة AI وتحديد النماذج في صفحة الإعدادات أولاً\" : \"Please configure an AI provider and select models in Settings first\",\n        error: isArabic ? \"حدث خطأ أثناء التوليد\" : \"Error occurred during generation\",\n        tryAgain: isArabic ? \"حاول مرة أخرى\" : \"Try Again\",\n        regenerate: isArabic ? \"إعادة توليد\" : \"Regenerate\",\n        fastGeneration: isArabic ? \"توليد سريع (محسّن)\" : \"Fast Generation (Optimized)\",\n        timeout: isArabic ? \"انتهت مهلة الطلب - حاول مرة أخرى\" : \"Request timeout - try again\"\n    };\n    const generateSuggestions = async ()=>{\n        if (!hasValidProvider) {\n            console.warn(\"No valid provider available:\", {\n                activeProviders,\n                hasValidProvider\n            });\n            alert(translations.noProviders);\n            return;\n        }\n        setIsGenerating(true);\n        try {\n            const allContext = getAllData();\n            const provider = activeProviders.find((p)=>p.apiKey && p.validationStatus === \"valid\");\n            console.log(\"Using provider:\", provider === null || provider === void 0 ? void 0 : provider.name, \"with model:\", provider === null || provider === void 0 ? void 0 : provider.selectedModels[0]);\n            if (!provider) {\n                throw new Error(\"No valid provider found\");\n            }\n            // إنشاء prompt ذكي بناءً على السياق والحقل\n            const prompt = createSmartPrompt(fieldName, fieldValue, allContext, isArabic);\n            console.log(\"Generated prompt:\", prompt);\n            const requestBody = {\n                providerId: provider.id,\n                apiKey: provider.apiKey,\n                model: provider.selectedModels[0] || \"gpt-3.5-turbo\",\n                messages: [\n                    {\n                        role: \"user\",\n                        content: prompt\n                    }\n                ],\n                context: allContext,\n                fieldName,\n                language: currentLanguage,\n                temperature: 0.7,\n                maxTokens: 200 // تقليل maxTokens بشكل كبير للسرعة\n            };\n            console.log(\"Sending request to API:\", requestBody);\n            // إضافة timeout للطلب من جانب العميل\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>{\n                controller.abort();\n            }, 35000); // 35 ثانية timeout\n            const response = await fetch(\"/api/llm/generate\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(requestBody),\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            console.log(\"API Response status:\", response.status);\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error(\"API Error:\", errorText);\n                throw new Error(\"API Error: \".concat(response.status, \" - \").concat(errorText));\n            }\n            const result = await response.json();\n            console.log(\"API Result:\", result);\n            if (result.success) {\n                // استخدام أول اقتراح مباشرة في خانة الكتابة\n                const suggestions = parseSuggestions(result.content);\n                console.log(\"Parsed suggestions:\", suggestions);\n                if (suggestions.length > 0) {\n                    // وضع أول اقتراح في خانة الكتابة\n                    onValueChange(suggestions[0]);\n                    // حفظ باقي الاقتراحات للاستخدام لاحقاً\n                    setGeneratedSuggestions(suggestions);\n                } else {\n                    const errorMsg = isArabic ? \"لم يتم العثور على اقتراحات مناسبة\" : \"No suitable suggestions found\";\n                    onValueChange(errorMsg);\n                }\n            } else {\n                throw new Error(result.error || \"Generation failed\");\n            }\n        } catch (error) {\n            console.error(\"Generation error:\", error);\n            let errorMessage = translations.error;\n            if (error instanceof Error) {\n                if (error.name === \"AbortError\") {\n                    errorMessage = translations.timeout;\n                } else if (error.message.includes(\"timeout\")) {\n                    errorMessage = translations.timeout;\n                } else {\n                    errorMessage = \"\".concat(translations.error, \": \").concat(error.message);\n                }\n            }\n            onValueChange(errorMessage);\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const createSmartPrompt = (fieldName, currentValue, context, isArabic)=>{\n        // استخراج الخيارات المتقدمة من السياق\n        const projectDef = (context === null || context === void 0 ? void 0 : context.projectDefinition) || {};\n        const advancedContext = {\n            projectType: projectDef.projectType,\n            projectNature: projectDef.projectNature,\n            geographicRegion: projectDef.geographicRegion,\n            targetPlatforms: projectDef.targetPlatforms || [],\n            primaryLanguages: projectDef.primaryLanguages || [],\n            complexity: projectDef.complexity,\n            deploymentType: projectDef.deploymentType\n        };\n        // إنشاء سياق متقدم للـ prompt مع التركيز على الخيارات الجديدة\n        const contextString = Object.entries(advancedContext).filter((param)=>{\n            let [_, value] = param;\n            return value && (Array.isArray(value) ? value.length > 0 : true);\n        }).map((param)=>{\n            let [key, value] = param;\n            return \"\".concat(key, \": \").concat(Array.isArray(value) ? value.join(\", \") : value);\n        }).join(\", \");\n        // تحسين الـ prompts لتكون أكثر تفصيلاً وذكاءً\n        const fieldPrompts = {\n            // Project Definition Module\n            name: {\n                ar: \"بناءً على السياق المتاح، اقترح 3 أسماء إبداعية ومناسبة للمشروع\",\n                en: \"Based on the available context, suggest 3 creative and suitable project names\"\n            },\n            purpose: {\n                ar: \"اكتب 3 أوصاف مختلفة ومفصلة لغرض المشروع، مع مراعاة الاسم والسياق\",\n                en: \"Write 3 different and detailed project purpose descriptions, considering the name and context\"\n            },\n            targetUsers: {\n                ar: \"حدد 3 مجموعات مختلفة من المستخدمين المستهدفين بناءً على غرض المشروع\",\n                en: \"Define 3 different target user groups based on the project purpose\"\n            },\n            goals: {\n                ar: \"اقترح 3 أهداف محددة وقابلة للقياس للمشروع\",\n                en: \"Suggest 3 specific and measurable project goals\"\n            },\n            scope: {\n                ar: \"حدد 3 نطاقات مختلفة للمشروع (صغير، متوسط، كبير)\",\n                en: \"Define 3 different project scopes (small, medium, large)\"\n            },\n            timeline: {\n                ar: \"اقترح 3 جداول زمنية مختلفة للمشروع\",\n                en: \"Suggest 3 different project timelines\"\n            },\n            // Context Map Module\n            timeContext: {\n                ar: \"حدد 3 سياقات زمنية مختلفة مناسبة للمشروع\",\n                en: \"Define 3 different time contexts suitable for the project\"\n            },\n            language: {\n                ar: \"اقترح 3 استراتيجيات لغوية للمشروع\",\n                en: \"Suggest 3 language strategies for the project\"\n            },\n            location: {\n                ar: \"حدد 3 مواقع جغرافية مستهدفة للمشروع\",\n                en: \"Define 3 target geographical locations for the project\"\n            },\n            culturalContext: {\n                ar: \"اقترح 3 اعتبارات ثقافية مهمة للمشروع\",\n                en: \"Suggest 3 important cultural considerations for the project\"\n            },\n            // Emotional Tone Module\n            personality: {\n                ar: \"اقترح 3 شخصيات مختلفة للمشروع تناسب المستخدمين المستهدفين\",\n                en: \"Suggest 3 different project personalities that suit the target users\"\n            },\n            communicationStyle: {\n                ar: \"حدد 3 أساليب تواصل مختلفة مناسبة للمشروع\",\n                en: \"Define 3 different communication styles suitable for the project\"\n            },\n            // Technical Layer Module\n            programmingLanguages: {\n                ar: \"اقترح 3 لغات برمجة مناسبة للمشروع مع التبرير\",\n                en: \"Suggest 3 suitable programming languages for the project with justification\"\n            },\n            frameworks: {\n                ar: \"حدد 3 إطارات عمل تقنية مناسبة للمشروع\",\n                en: \"Define 3 technical frameworks suitable for the project\"\n            }\n        };\n        const fieldPrompt = fieldPrompts[fieldName];\n        const basePrompt = fieldPrompt ? isArabic ? fieldPrompt.ar : fieldPrompt.en : isArabic ? \"اقترح محتوى ذكي ومناسب لـ \".concat(fieldName) : \"Suggest smart and suitable content for \".concat(fieldName);\n        // بناء سياق أكثر ذكاءً\n        const contextInfo = buildIntelligentContext(context, fieldName, isArabic);\n        const instructions = isArabic ? \"قدم 3 اقتراحات مرقمة ومفصلة، كل اقتراح في سطر منفصل. اجعل كل اقتراح متماسكاً مع السياق العام للمشروع.\" : \"Provide 3 numbered and detailed suggestions, each on a separate line. Make each suggestion coherent with the overall project context.\";\n        return \"\".concat(contextInfo, \"\\n\").concat(basePrompt, \"\\n\").concat(instructions);\n    };\n    // وظيفة لبناء سياق ذكي\n    const buildIntelligentContext = (context, fieldName, isArabic)=>{\n        var _context_projectDefinition, _context_projectDefinition1, _context_projectDefinition2;\n        const contextParts = [];\n        // معلومات المشروع الأساسية\n        if (context === null || context === void 0 ? void 0 : (_context_projectDefinition = context.projectDefinition) === null || _context_projectDefinition === void 0 ? void 0 : _context_projectDefinition.name) {\n            contextParts.push(isArabic ? \"اسم المشروع: \".concat(context.projectDefinition.name) : \"Project Name: \".concat(context.projectDefinition.name));\n        }\n        if (context === null || context === void 0 ? void 0 : (_context_projectDefinition1 = context.projectDefinition) === null || _context_projectDefinition1 === void 0 ? void 0 : _context_projectDefinition1.purpose) {\n            contextParts.push(isArabic ? \"الغرض: \".concat(context.projectDefinition.purpose.substring(0, 100), \"...\") : \"Purpose: \".concat(context.projectDefinition.purpose.substring(0, 100), \"...\"));\n        }\n        if (context === null || context === void 0 ? void 0 : (_context_projectDefinition2 = context.projectDefinition) === null || _context_projectDefinition2 === void 0 ? void 0 : _context_projectDefinition2.targetUsers) {\n            contextParts.push(isArabic ? \"المستخدمون المستهدفون: \".concat(context.projectDefinition.targetUsers.substring(0, 80), \"...\") : \"Target Users: \".concat(context.projectDefinition.targetUsers.substring(0, 80), \"...\"));\n        }\n        // الخيارات المتقدمة\n        const projectDef = (context === null || context === void 0 ? void 0 : context.projectDefinition) || {};\n        if (projectDef.projectType) {\n            contextParts.push(isArabic ? \"نوع المشروع: \".concat(projectDef.projectType) : \"Project Type: \".concat(projectDef.projectType));\n        }\n        // طبيعة المشروع - خيار جديد مهم\n        if (projectDef.projectNature) {\n            contextParts.push(isArabic ? \"طبيعة المشروع: \".concat(projectDef.projectNature) : \"Project Nature: \".concat(projectDef.projectNature));\n        }\n        // المنطقة الجغرافية - خيار جديد مهم\n        if (projectDef.geographicRegion) {\n            contextParts.push(isArabic ? \"المنطقة الجغرافية: \".concat(projectDef.geographicRegion) : \"Geographic Region: \".concat(projectDef.geographicRegion));\n        }\n        if (projectDef.targetPlatforms && projectDef.targetPlatforms.length > 0) {\n            contextParts.push(isArabic ? \"المنصات المستهدفة: \".concat(projectDef.targetPlatforms.join(\", \")) : \"Target Platforms: \".concat(projectDef.targetPlatforms.join(\", \")));\n        }\n        if (projectDef.primaryLanguages && projectDef.primaryLanguages.length > 0) {\n            contextParts.push(isArabic ? \"لغات البرمجة: \".concat(projectDef.primaryLanguages.join(\", \")) : \"Programming Languages: \".concat(projectDef.primaryLanguages.join(\", \")));\n        }\n        if (projectDef.complexity) {\n            contextParts.push(isArabic ? \"مستوى التعقيد: \".concat(projectDef.complexity) : \"Complexity Level: \".concat(projectDef.complexity));\n        }\n        if (projectDef.teamSize) {\n            contextParts.push(isArabic ? \"حجم الفريق: \".concat(projectDef.teamSize) : \"Team Size: \".concat(projectDef.teamSize));\n        }\n        if (projectDef.budget) {\n            contextParts.push(isArabic ? \"نطاق الميزانية: \".concat(projectDef.budget) : \"Budget Range: \".concat(projectDef.budget));\n        }\n        // سياق إضافي حسب المجال\n        if (fieldName.includes(\"technical\") || fieldName.includes(\"programming\") || fieldName.includes(\"frameworks\")) {\n            var _context_technicalLayer;\n            if (context === null || context === void 0 ? void 0 : (_context_technicalLayer = context.technicalLayer) === null || _context_technicalLayer === void 0 ? void 0 : _context_technicalLayer.programmingLanguages) {\n                contextParts.push(isArabic ? \"التقنيات المستخدمة: \".concat(context.technicalLayer.programmingLanguages.substring(0, 60), \"...\") : \"Technologies: \".concat(context.technicalLayer.programmingLanguages.substring(0, 60), \"...\"));\n            }\n        }\n        if (fieldName.includes(\"emotional\") || fieldName.includes(\"personality\") || fieldName.includes(\"communication\")) {\n            var _context_emotionalTone;\n            if (context === null || context === void 0 ? void 0 : (_context_emotionalTone = context.emotionalTone) === null || _context_emotionalTone === void 0 ? void 0 : _context_emotionalTone.personality) {\n                contextParts.push(isArabic ? \"الشخصية المطلوبة: \".concat(context.emotionalTone.personality.substring(0, 60), \"...\") : \"Required Personality: \".concat(context.emotionalTone.personality.substring(0, 60), \"...\"));\n            }\n        }\n        return contextParts.length > 0 ? (isArabic ? \"السياق الحالي:\\n\" : \"Current Context:\\n\") + contextParts.join(\"\\n\") : isArabic ? \"مشروع جديد\" : \"New Project\";\n    };\n    const parseSuggestions = (content)=>{\n        // تقسيم المحتوى إلى اقتراحات منفصلة\n        const lines = content.split(\"\\n\").filter((line)=>line.trim());\n        const suggestions = [];\n        for (const line of lines){\n            // البحث عن الأسطر المرقمة أو التي تبدأ برقم\n            if (/^\\d+[.\\-\\)]\\s*/.test(line.trim()) || /^[•\\-\\*]\\s*/.test(line.trim())) {\n                const cleaned = line.replace(/^\\d+[.\\-\\)]\\s*/, \"\").replace(/^[•\\-\\*]\\s*/, \"\").trim();\n                if (cleaned && cleaned.length > 10) {\n                    suggestions.push(cleaned);\n                }\n            } else if (line.trim().length > 20 && !line.includes(\":\") && suggestions.length < 3) {\n                suggestions.push(line.trim());\n            }\n        }\n        // إذا لم نجد اقتراحات مرقمة، نقسم النص إلى جمل\n        if (suggestions.length === 0) {\n            const sentences = content.split(/[.!?]+/).filter((s)=>s.trim().length > 20);\n            return sentences.slice(0, 3).map((s)=>s.trim());\n        }\n        return suggestions.slice(0, 3);\n    };\n    const copyToClipboard = async (text, index)=>{\n        try {\n            await navigator.clipboard.writeText(text);\n            setCopiedIndex(index);\n            setTimeout(()=>setCopiedIndex(null), 2000);\n        } catch (error) {\n            console.error(\"Failed to copy:\", error);\n        }\n    };\n    const regenerateContent = async ()=>{\n        if (generatedSuggestions.length > 1) {\n            // استخدام الاقتراح التالي إذا كان متوفراً\n            const currentIndex = generatedSuggestions.findIndex((s)=>s === fieldValue);\n            const nextIndex = (currentIndex + 1) % generatedSuggestions.length;\n            onValueChange(generatedSuggestions[nextIndex]);\n        } else {\n            // توليد محتوى جديد\n            await generateSuggestions();\n        }\n    };\n    // تجنب مشاكل الهيدريشن\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-2 \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative flex items-center gap-2 px-4 py-2.5 rounded-lg font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-blue-500/80 to-indigo-600/80 text-white shadow-md opacity-50 font-arabic\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 432,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: [\n                            \"\\uD83D\\uDCC4 \",\n                            isArabic ? \"توليد بالذكاء الاصطناعي\" : \"Generate with AI\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 433,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                lineNumber: 431,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n            lineNumber: 430,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center gap-2 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: generateSuggestions,\n                disabled: isGenerating || !hasValidProvider,\n                className: \"relative flex items-center gap-2 px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group \".concat(hasValidProvider ? \"bg-gradient-to-br from-purple-500/80 via-blue-500/80 to-indigo-600/80 hover:from-purple-600/90 hover:via-blue-600/90 hover:to-indigo-700/90 text-white shadow-lg hover:shadow-xl hover:shadow-purple-500/25 hover:scale-105 active:scale-95\" : \"bg-gray-200/50 dark:bg-gray-700/50 text-gray-400 dark:text-gray-500 cursor-not-allowed border-gray-300/30 dark:border-gray-600/30\", \" \").concat(isGenerating ? \"animate-pulse scale-105\" : \"\", \" \").concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                title: hasValidProvider ? isArabic ? \"توليد سريع محسّن - أقل من 5 ثواني\" : \"Fast optimized generation - under 5 seconds\" : translations.noProviders,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 448,\n                        columnNumber: 9\n                    }, this),\n                    hasValidProvider && !isGenerating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 -skew-x-12 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 group-hover:animate-shimmer\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 452,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex items-center gap-2 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                        children: [\n                            isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-4 h-4 animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 457,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-4 h-4 group-hover:rotate-12 transition-transform duration-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 459,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-arabic font-medium\",\n                                children: isGenerating ? translations.generating : translations.fastGeneration\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 461,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 455,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                lineNumber: 441,\n                columnNumber: 7\n            }, this),\n            fieldValue && generatedSuggestions.length > 0 && !isGenerating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: regenerateContent,\n                className: \"relative flex items-center gap-2 px-3 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-orange-500/80 to-red-500/80 hover:from-orange-600/90 hover:to-red-600/90 text-white shadow-lg hover:shadow-xl hover:shadow-orange-500/25 hover:scale-105 active:scale-95 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                title: isArabic ? \"إعادة توليد\" : \"Regenerate\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 474,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex items-center gap-1 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-4 h-4 group-hover:rotate-180 transition-transform duration-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 476,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-arabic font-medium\",\n                                children: isArabic ? \"إعادة توليد\" : \"Regenerate\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 477,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 475,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                lineNumber: 469,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n        lineNumber: 440,\n        columnNumber: 5\n    }, this);\n}\n_s(SmartFieldAssistant, \"ZrnkxJH8z8I5/fadLS+VIi/I8fM=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore\n    ];\n});\n_c = SmartFieldAssistant;\nvar _c;\n$RefreshReg$(_c, \"SmartFieldAssistant\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/SmartFieldAssistant.tsx\n"));

/***/ })

});