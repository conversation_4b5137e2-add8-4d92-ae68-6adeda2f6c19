'use client';

import { useState } from 'react';
import { useContextStore } from '@/store/contextStore';
import AdvancedOptionsSelector from '@/components/AdvancedOptionsSelector';
import {
  PROJECT_TYPES,
  TARGET_PLATFORMS,
  PROGRAMMING_LANGUAGES,
  COMPLEXITY_LEVELS,
  BUDGET_RANGES,
  TEAM_SIZES,
  DEPLOYMENT_TYPES
} from '@/lib/projectOptions';
import {
  ARCHITECTURE_PATTERNS,
  SCALING_STRATEGIES,
  SECURITY_REQUIREMENTS,
  PERFORMANCE_TARGETS,
  INTEGRATION_NEEDS,
  MONITORING_TOOLS
} from '@/lib/technicalOptions';
import { ChevronDown, ChevronUp, Settings, Sparkles } from 'lucide-react';

interface AdvancedOptionsPanelProps {
  moduleType: 'project-definition' | 'technical-layer';
}

export default function AdvancedOptionsPanel({ moduleType }: AdvancedOptionsPanelProps) {
  const { 
    projectDefinition, 
    technicalLayer, 
    updateProjectDefinition, 
    updateTechnicalLayer,
    showAdvancedOptions,
    currentLanguage 
  } = useContextStore();
  
  const [isExpanded, setIsExpanded] = useState(true);
  const isArabic = currentLanguage === 'ar';

  if (!showAdvancedOptions) {
    return null;
  }

  const handleProjectFieldChange = (field: keyof typeof projectDefinition, value: string) => {
    updateProjectDefinition({ [field]: value });
  };

  const handleProjectArrayFieldChange = (field: keyof typeof projectDefinition, values: string[]) => {
    updateProjectDefinition({ [field]: values });
  };

  const handleTechnicalFieldChange = (field: keyof typeof technicalLayer, value: string) => {
    updateTechnicalLayer({ [field]: value });
  };

  const getTitle = () => {
    if (moduleType === 'project-definition') {
      return isArabic ? 'خيارات التخصيص المتقدمة' : 'Advanced Customization Options';
    } else {
      return isArabic ? 'خيارات تقنية متقدمة' : 'Advanced Technical Options';
    }
  };

  const getIcon = () => {
    return moduleType === 'project-definition' ? '🎯' : '⚙️';
  };

  return (
    <div className="bg-gradient-to-br from-indigo-50 to-purple-100 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-xl border border-indigo-200 dark:border-indigo-800 mb-8 overflow-hidden">
      {/* Header */}
      <div 
        className="p-6 cursor-pointer hover:bg-indigo-100/50 dark:hover:bg-indigo-800/30 transition-colors duration-200"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className={`flex items-center justify-between ${isArabic ? 'flex-row-reverse' : ''}`}>
          <div className={`flex items-center gap-3 ${isArabic ? 'flex-row-reverse' : ''}`}>
            <div className="p-2 bg-indigo-100 dark:bg-indigo-800 rounded-lg">
              <span className="text-xl">{getIcon()}</span>
            </div>
            <div className={isArabic ? 'text-right' : 'text-left'}>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {getTitle()}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {isArabic 
                  ? 'خصص مشروعك بخيارات متقدمة للحصول على نتائج أكثر دقة'
                  : 'Customize your project with advanced options for more precise results'
                }
              </p>
            </div>
          </div>
          
          <div className={`flex items-center gap-2 ${isArabic ? 'flex-row-reverse' : ''}`}>
            <div className="flex items-center gap-1 px-3 py-1 bg-indigo-100 dark:bg-indigo-800 rounded-full">
              <Sparkles className="w-3 h-3 text-indigo-600 dark:text-indigo-400" />
              <span className="text-xs font-medium text-indigo-700 dark:text-indigo-300">
                {isArabic ? 'ذكي' : 'Smart'}
              </span>
            </div>
            {isExpanded ? (
              <ChevronUp className="w-5 h-5 text-gray-500 dark:text-gray-400" />
            ) : (
              <ChevronDown className="w-5 h-5 text-gray-500 dark:text-gray-400" />
            )}
          </div>
        </div>
      </div>

      {/* Content */}
      {isExpanded && (
        <div className="px-6 pb-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
            {moduleType === 'project-definition' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* نوع المشروع */}
                <AdvancedOptionsSelector
                  title="Project Type"
                  titleAr="نوع المشروع"
                  options={PROJECT_TYPES}
                  selectedValues={projectDefinition.projectType ? [projectDefinition.projectType] : []}
                  onSelectionChange={(values) => handleProjectFieldChange('projectType', values[0] || '')}
                  placeholder="Select project type"
                  placeholderAr="اختر نوع المشروع"
                />

                {/* مستوى التعقيد */}
                <AdvancedOptionsSelector
                  title="Complexity Level"
                  titleAr="مستوى التعقيد"
                  options={COMPLEXITY_LEVELS}
                  selectedValues={projectDefinition.complexity ? [projectDefinition.complexity] : []}
                  onSelectionChange={(values) => handleProjectFieldChange('complexity', values[0] || '')}
                  placeholder="Select complexity level"
                  placeholderAr="اختر مستوى التعقيد"
                />

                {/* المنصات المستهدفة */}
                <AdvancedOptionsSelector
                  title="Target Platforms"
                  titleAr="المنصات المستهدفة"
                  options={TARGET_PLATFORMS}
                  selectedValues={projectDefinition.targetPlatforms || []}
                  onSelectionChange={(values) => handleProjectArrayFieldChange('targetPlatforms', values)}
                  multiSelect={true}
                  placeholder="Select target platforms"
                  placeholderAr="اختر المنصات المستهدفة"
                />

                {/* لغات البرمجة الأساسية */}
                <AdvancedOptionsSelector
                  title="Primary Programming Languages"
                  titleAr="لغات البرمجة الأساسية"
                  options={PROGRAMMING_LANGUAGES}
                  selectedValues={projectDefinition.primaryLanguages || []}
                  onSelectionChange={(values) => handleProjectArrayFieldChange('primaryLanguages', values)}
                  multiSelect={true}
                  placeholder="Select programming languages"
                  placeholderAr="اختر لغات البرمجة"
                />

                {/* حجم الفريق */}
                <AdvancedOptionsSelector
                  title="Team Size"
                  titleAr="حجم الفريق"
                  options={TEAM_SIZES}
                  selectedValues={projectDefinition.teamSize ? [projectDefinition.teamSize] : []}
                  onSelectionChange={(values) => handleProjectFieldChange('teamSize', values[0] || '')}
                  placeholder="Select team size"
                  placeholderAr="اختر حجم الفريق"
                />

                {/* نطاق الميزانية */}
                <AdvancedOptionsSelector
                  title="Budget Range"
                  titleAr="نطاق الميزانية"
                  options={BUDGET_RANGES}
                  selectedValues={projectDefinition.budget ? [projectDefinition.budget] : []}
                  onSelectionChange={(values) => handleProjectFieldChange('budget', values[0] || '')}
                  placeholder="Select budget range"
                  placeholderAr="اختر نطاق الميزانية"
                />

                {/* نوع النشر */}
                <div className="md:col-span-2">
                  <AdvancedOptionsSelector
                    title="Deployment Type"
                    titleAr="نوع النشر"
                    options={DEPLOYMENT_TYPES}
                    selectedValues={projectDefinition.deploymentType ? [projectDefinition.deploymentType] : []}
                    onSelectionChange={(values) => handleProjectFieldChange('deploymentType', values[0] || '')}
                    placeholder="Select deployment type"
                    placeholderAr="اختر نوع النشر"
                  />
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* نمط الهندسة المعمارية */}
                <AdvancedOptionsSelector
                  title="Architecture Pattern"
                  titleAr="نمط الهندسة المعمارية"
                  options={ARCHITECTURE_PATTERNS}
                  selectedValues={technicalLayer.architecturePattern ? [technicalLayer.architecturePattern] : []}
                  onSelectionChange={(values) => handleTechnicalFieldChange('architecturePattern', values[0] || '')}
                  placeholder="Select architecture pattern"
                  placeholderAr="اختر نمط الهندسة المعمارية"
                />

                {/* استراتيجية التوسع */}
                <AdvancedOptionsSelector
                  title="Scaling Strategy"
                  titleAr="استراتيجية التوسع"
                  options={SCALING_STRATEGIES}
                  selectedValues={technicalLayer.scalingStrategy ? [technicalLayer.scalingStrategy] : []}
                  onSelectionChange={(values) => handleTechnicalFieldChange('scalingStrategy', values[0] || '')}
                  placeholder="Select scaling strategy"
                  placeholderAr="اختر استراتيجية التوسع"
                />

                {/* متطلبات الأمان */}
                <AdvancedOptionsSelector
                  title="Security Requirements"
                  titleAr="متطلبات الأمان"
                  options={SECURITY_REQUIREMENTS}
                  selectedValues={technicalLayer.securityRequirements ? [technicalLayer.securityRequirements] : []}
                  onSelectionChange={(values) => handleTechnicalFieldChange('securityRequirements', values[0] || '')}
                  placeholder="Select security requirements"
                  placeholderAr="اختر متطلبات الأمان"
                />

                {/* أهداف الأداء */}
                <AdvancedOptionsSelector
                  title="Performance Targets"
                  titleAr="أهداف الأداء"
                  options={PERFORMANCE_TARGETS}
                  selectedValues={technicalLayer.performanceTargets ? [technicalLayer.performanceTargets] : []}
                  onSelectionChange={(values) => handleTechnicalFieldChange('performanceTargets', values[0] || '')}
                  placeholder="Select performance targets"
                  placeholderAr="اختر أهداف الأداء"
                />

                {/* احتياجات التكامل */}
                <AdvancedOptionsSelector
                  title="Integration Needs"
                  titleAr="احتياجات التكامل"
                  options={INTEGRATION_NEEDS}
                  selectedValues={technicalLayer.integrationNeeds ? [technicalLayer.integrationNeeds] : []}
                  onSelectionChange={(values) => handleTechnicalFieldChange('integrationNeeds', values[0] || '')}
                  placeholder="Select integration needs"
                  placeholderAr="اختر احتياجات التكامل"
                />

                {/* أدوات المراقبة */}
                <AdvancedOptionsSelector
                  title="Monitoring Tools"
                  titleAr="أدوات المراقبة"
                  options={MONITORING_TOOLS}
                  selectedValues={technicalLayer.monitoringTools ? [technicalLayer.monitoringTools] : []}
                  onSelectionChange={(values) => handleTechnicalFieldChange('monitoringTools', values[0] || '')}
                  placeholder="Select monitoring tools"
                  placeholderAr="اختر أدوات المراقبة"
                />
              </div>
            )}

            {/* نصيحة ذكية */}
            <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/30 rounded-lg border border-blue-200 dark:border-blue-700">
              <div className="flex items-start gap-2">
                <span className="text-lg">💡</span>
                <div className={isArabic ? 'text-right' : 'text-left'}>
                  <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-1">
                    {isArabic ? 'نصيحة ذكية' : 'Smart Tip'}
                  </h4>
                  <p className="text-sm text-blue-700 dark:text-blue-300">
                    {isArabic 
                      ? 'كلما زادت دقة اختياراتك، كانت اقتراحات الذكاء الاصطناعي أكثر تخصصاً وفائدة لمشروعك.'
                      : 'The more precise your selections, the more specialized and useful AI suggestions will be for your project.'
                    }
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
