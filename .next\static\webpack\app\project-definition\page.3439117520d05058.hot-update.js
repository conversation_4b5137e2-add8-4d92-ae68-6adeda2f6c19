"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/project-definition/page",{

/***/ "(app-pages-browser)/./src/components/SmartFieldAssistant.tsx":
/*!************************************************!*\
  !*** ./src/components/SmartFieldAssistant.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SmartFieldAssistant; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Sparkles,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Sparkles,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Sparkles,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wand-sparkles.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction SmartFieldAssistant(param) {\n    let { fieldName, fieldValue, onValueChange, placeholder, context, className = \"\" } = param;\n    _s();\n    const { currentLanguage, getActiveProviders, getAllData } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore)();\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [generatedSuggestions, setGeneratedSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showSuggestions, setShowSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [copiedIndex, setCopiedIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeProviders, setActiveProviders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isArabic = currentLanguage === \"ar\";\n    // تجنب مشاكل الهيدريشن\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n        setActiveProviders(getActiveProviders());\n    }, [\n        getActiveProviders\n    ]);\n    // تحقق من وجود مقدم خدمة صالح\n    const hasValidProvider = mounted && activeProviders.some((p)=>p.apiKey && p.validationStatus === \"valid\" && p.selectedModels && p.selectedModels.length > 0);\n    const translations = {\n        generateWithAI: isArabic ? \"\\uD83D\\uDCC4 توليد بالذكاء الاصطناعي\" : \"\\uD83D\\uDCC4 Generate with AI\",\n        generating: isArabic ? \"جاري التوليد...\" : \"Generating...\",\n        suggestions: isArabic ? \"اقتراحات ذكية\" : \"Smart Suggestions\",\n        useThis: isArabic ? \"استخدام هذا\" : \"Use This\",\n        copy: isArabic ? \"نسخ\" : \"Copy\",\n        copied: isArabic ? \"تم النسخ\" : \"Copied\",\n        noProviders: isArabic ? \"يرجى إعداد مقدم خدمة AI وتحديد النماذج في صفحة الإعدادات أولاً\" : \"Please configure an AI provider and select models in Settings first\",\n        error: isArabic ? \"حدث خطأ أثناء التوليد\" : \"Error occurred during generation\",\n        tryAgain: isArabic ? \"حاول مرة أخرى\" : \"Try Again\",\n        regenerate: isArabic ? \"إعادة توليد\" : \"Regenerate\",\n        fastGeneration: isArabic ? \"توليد سريع (محسّن)\" : \"Fast Generation (Optimized)\",\n        timeout: isArabic ? \"انتهت مهلة الطلب - حاول مرة أخرى\" : \"Request timeout - try again\"\n    };\n    const generateSuggestions = async ()=>{\n        if (!hasValidProvider) {\n            console.warn(\"No valid provider available:\", {\n                activeProviders,\n                hasValidProvider\n            });\n            alert(translations.noProviders);\n            return;\n        }\n        setIsGenerating(true);\n        try {\n            const allContext = getAllData();\n            const provider = activeProviders.find((p)=>p.apiKey && p.validationStatus === \"valid\");\n            console.log(\"Using provider:\", provider === null || provider === void 0 ? void 0 : provider.name, \"with model:\", provider === null || provider === void 0 ? void 0 : provider.selectedModels[0]);\n            if (!provider) {\n                throw new Error(\"No valid provider found\");\n            }\n            // إنشاء prompt ذكي بناءً على السياق والحقل\n            const prompt = createSmartPrompt(fieldName, fieldValue, allContext, isArabic);\n            console.log(\"Generated prompt:\", prompt);\n            const requestBody = {\n                providerId: provider.id,\n                apiKey: provider.apiKey,\n                model: provider.selectedModels[0] || \"gpt-3.5-turbo\",\n                messages: [\n                    {\n                        role: \"user\",\n                        content: prompt\n                    }\n                ],\n                context: allContext,\n                fieldName,\n                language: currentLanguage,\n                temperature: 0.7,\n                maxTokens: 200 // تقليل maxTokens بشكل كبير للسرعة\n            };\n            console.log(\"Sending request to API:\", requestBody);\n            // إضافة timeout للطلب من جانب العميل\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>{\n                controller.abort();\n            }, 35000); // 35 ثانية timeout\n            const response = await fetch(\"/api/llm/generate\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(requestBody),\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            console.log(\"API Response status:\", response.status);\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error(\"API Error:\", errorText);\n                throw new Error(\"API Error: \".concat(response.status, \" - \").concat(errorText));\n            }\n            const result = await response.json();\n            console.log(\"API Result:\", result);\n            if (result.success) {\n                // استخدام أول اقتراح مباشرة في خانة الكتابة\n                const suggestions = parseSuggestions(result.content);\n                console.log(\"Parsed suggestions:\", suggestions);\n                if (suggestions.length > 0) {\n                    // وضع أول اقتراح في خانة الكتابة\n                    onValueChange(suggestions[0]);\n                    // حفظ باقي الاقتراحات للاستخدام لاحقاً\n                    setGeneratedSuggestions(suggestions);\n                } else {\n                    const errorMsg = isArabic ? \"لم يتم العثور على اقتراحات مناسبة\" : \"No suitable suggestions found\";\n                    onValueChange(errorMsg);\n                }\n            } else {\n                throw new Error(result.error || \"Generation failed\");\n            }\n        } catch (error) {\n            console.error(\"Generation error:\", error);\n            let errorMessage = translations.error;\n            if (error instanceof Error) {\n                if (error.name === \"AbortError\") {\n                    errorMessage = translations.timeout;\n                } else if (error.message.includes(\"timeout\")) {\n                    errorMessage = translations.timeout;\n                } else {\n                    errorMessage = \"\".concat(translations.error, \": \").concat(error.message);\n                }\n            }\n            onValueChange(errorMessage);\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const createSmartPrompt = (fieldName, currentValue, context, isArabic)=>{\n        // استخراج الخيارات المتقدمة من السياق\n        const projectDef = (context === null || context === void 0 ? void 0 : context.projectDefinition) || {};\n        const advancedContext = {\n            projectType: projectDef.projectType,\n            projectNature: projectDef.projectNature,\n            geographicRegion: projectDef.geographicRegion,\n            targetPlatforms: projectDef.targetPlatforms || [],\n            primaryLanguages: projectDef.primaryLanguages || [],\n            complexity: projectDef.complexity,\n            deploymentType: projectDef.deploymentType\n        };\n        // إنشاء سياق متقدم للـ prompt مع التركيز على الخيارات الجديدة\n        const contextString = Object.entries(advancedContext).filter((param)=>{\n            let [_, value] = param;\n            return value && (Array.isArray(value) ? value.length > 0 : true);\n        }).map((param)=>{\n            let [key, value] = param;\n            return \"\".concat(key, \": \").concat(Array.isArray(value) ? value.join(\", \") : value);\n        }).join(\", \");\n        // تحسين الـ prompts لتكون أكثر تفصيلاً وذكاءً\n        const fieldPrompts = {\n            // Project Definition Module\n            name: {\n                ar: \"بناءً على طبيعة المشروع والمنطقة الجغرافية المستهدفة، اقترح 3 أسماء إبداعية ومناسبة للمشروع\",\n                en: \"Based on the project nature and target geographic region, suggest 3 creative and suitable project names\"\n            },\n            purpose: {\n                ar: \"اكتب 3 أوصاف مختلفة ومفصلة لغرض المشروع، مع مراعاة طبيعة المشروع والمنطقة الجغرافية\",\n                en: \"Write 3 different and detailed project purpose descriptions, considering project nature and geographic region\"\n            },\n            targetUsers: {\n                ar: \"حدد 3 مجموعات مختلفة من المستخدمين المستهدفين بناءً على طبيعة المشروع والمنطقة الجغرافية\",\n                en: \"Define 3 different target user groups based on project nature and geographic region\"\n            },\n            goals: {\n                ar: \"اقترح 3 أهداف محددة وقابلة للقياس للمشروع\",\n                en: \"Suggest 3 specific and measurable project goals\"\n            },\n            scope: {\n                ar: \"حدد 3 نطاقات مختلفة للمشروع (صغير، متوسط، كبير)\",\n                en: \"Define 3 different project scopes (small, medium, large)\"\n            },\n            timeline: {\n                ar: \"اقترح 3 جداول زمنية مختلفة للمشروع\",\n                en: \"Suggest 3 different project timelines\"\n            },\n            // Context Map Module\n            timeContext: {\n                ar: \"حدد 3 سياقات زمنية مختلفة مناسبة للمشروع\",\n                en: \"Define 3 different time contexts suitable for the project\"\n            },\n            language: {\n                ar: \"اقترح 3 استراتيجيات لغوية للمشروع\",\n                en: \"Suggest 3 language strategies for the project\"\n            },\n            location: {\n                ar: \"حدد 3 مواقع جغرافية مستهدفة للمشروع\",\n                en: \"Define 3 target geographical locations for the project\"\n            },\n            culturalContext: {\n                ar: \"اقترح 3 اعتبارات ثقافية مهمة للمشروع\",\n                en: \"Suggest 3 important cultural considerations for the project\"\n            },\n            // Emotional Tone Module\n            personality: {\n                ar: \"اقترح 3 شخصيات مختلفة للمشروع تناسب المستخدمين المستهدفين\",\n                en: \"Suggest 3 different project personalities that suit the target users\"\n            },\n            communicationStyle: {\n                ar: \"حدد 3 أساليب تواصل مختلفة مناسبة للمشروع\",\n                en: \"Define 3 different communication styles suitable for the project\"\n            },\n            // Technical Layer Module\n            programmingLanguages: {\n                ar: \"اقترح 3 لغات برمجة مناسبة للمشروع مع التبرير\",\n                en: \"Suggest 3 suitable programming languages for the project with justification\"\n            },\n            frameworks: {\n                ar: \"حدد 3 إطارات عمل تقنية مناسبة للمشروع\",\n                en: \"Define 3 technical frameworks suitable for the project\"\n            }\n        };\n        const fieldPrompt = fieldPrompts[fieldName];\n        const basePrompt = fieldPrompt ? isArabic ? fieldPrompt.ar : fieldPrompt.en : isArabic ? \"اقترح محتوى ذكي ومناسب لـ \".concat(fieldName) : \"Suggest smart and suitable content for \".concat(fieldName);\n        // بناء سياق أكثر ذكاءً\n        const contextInfo = buildIntelligentContext(context, fieldName, isArabic);\n        const instructions = isArabic ? \"قدم 3 اقتراحات مرقمة ومفصلة، كل اقتراح في سطر منفصل. اجعل كل اقتراح متماسكاً مع السياق العام للمشروع.\" : \"Provide 3 numbered and detailed suggestions, each on a separate line. Make each suggestion coherent with the overall project context.\";\n        return \"\".concat(contextInfo, \"\\n\").concat(basePrompt, \"\\n\").concat(instructions);\n    };\n    // وظيفة لبناء سياق ذكي\n    const buildIntelligentContext = (context, fieldName, isArabic)=>{\n        var _context_projectDefinition, _context_projectDefinition1, _context_projectDefinition2;\n        const contextParts = [];\n        // معلومات المشروع الأساسية\n        if (context === null || context === void 0 ? void 0 : (_context_projectDefinition = context.projectDefinition) === null || _context_projectDefinition === void 0 ? void 0 : _context_projectDefinition.name) {\n            contextParts.push(isArabic ? \"اسم المشروع: \".concat(context.projectDefinition.name) : \"Project Name: \".concat(context.projectDefinition.name));\n        }\n        if (context === null || context === void 0 ? void 0 : (_context_projectDefinition1 = context.projectDefinition) === null || _context_projectDefinition1 === void 0 ? void 0 : _context_projectDefinition1.purpose) {\n            contextParts.push(isArabic ? \"الغرض: \".concat(context.projectDefinition.purpose.substring(0, 100), \"...\") : \"Purpose: \".concat(context.projectDefinition.purpose.substring(0, 100), \"...\"));\n        }\n        if (context === null || context === void 0 ? void 0 : (_context_projectDefinition2 = context.projectDefinition) === null || _context_projectDefinition2 === void 0 ? void 0 : _context_projectDefinition2.targetUsers) {\n            contextParts.push(isArabic ? \"المستخدمون المستهدفون: \".concat(context.projectDefinition.targetUsers.substring(0, 80), \"...\") : \"Target Users: \".concat(context.projectDefinition.targetUsers.substring(0, 80), \"...\"));\n        }\n        // الخيارات المتقدمة\n        const projectDef = (context === null || context === void 0 ? void 0 : context.projectDefinition) || {};\n        if (projectDef.projectType) {\n            contextParts.push(isArabic ? \"نوع المشروع: \".concat(projectDef.projectType) : \"Project Type: \".concat(projectDef.projectType));\n        }\n        // طبيعة المشروع - خيار جديد مهم\n        if (projectDef.projectNature) {\n            contextParts.push(isArabic ? \"طبيعة المشروع: \".concat(projectDef.projectNature) : \"Project Nature: \".concat(projectDef.projectNature));\n        }\n        // المنطقة الجغرافية - خيار جديد مهم\n        if (projectDef.geographicRegion) {\n            contextParts.push(isArabic ? \"المنطقة الجغرافية: \".concat(projectDef.geographicRegion) : \"Geographic Region: \".concat(projectDef.geographicRegion));\n        }\n        if (projectDef.targetPlatforms && projectDef.targetPlatforms.length > 0) {\n            contextParts.push(isArabic ? \"المنصات المستهدفة: \".concat(projectDef.targetPlatforms.join(\", \")) : \"Target Platforms: \".concat(projectDef.targetPlatforms.join(\", \")));\n        }\n        if (projectDef.primaryLanguages && projectDef.primaryLanguages.length > 0) {\n            contextParts.push(isArabic ? \"لغات البرمجة: \".concat(projectDef.primaryLanguages.join(\", \")) : \"Programming Languages: \".concat(projectDef.primaryLanguages.join(\", \")));\n        }\n        if (projectDef.complexity) {\n            contextParts.push(isArabic ? \"مستوى التعقيد: \".concat(projectDef.complexity) : \"Complexity Level: \".concat(projectDef.complexity));\n        }\n        if (projectDef.teamSize) {\n            contextParts.push(isArabic ? \"حجم الفريق: \".concat(projectDef.teamSize) : \"Team Size: \".concat(projectDef.teamSize));\n        }\n        if (projectDef.budget) {\n            contextParts.push(isArabic ? \"نطاق الميزانية: \".concat(projectDef.budget) : \"Budget Range: \".concat(projectDef.budget));\n        }\n        // سياق إضافي حسب المجال\n        if (fieldName.includes(\"technical\") || fieldName.includes(\"programming\") || fieldName.includes(\"frameworks\")) {\n            var _context_technicalLayer;\n            if (context === null || context === void 0 ? void 0 : (_context_technicalLayer = context.technicalLayer) === null || _context_technicalLayer === void 0 ? void 0 : _context_technicalLayer.programmingLanguages) {\n                contextParts.push(isArabic ? \"التقنيات المستخدمة: \".concat(context.technicalLayer.programmingLanguages.substring(0, 60), \"...\") : \"Technologies: \".concat(context.technicalLayer.programmingLanguages.substring(0, 60), \"...\"));\n            }\n        }\n        if (fieldName.includes(\"emotional\") || fieldName.includes(\"personality\") || fieldName.includes(\"communication\")) {\n            var _context_emotionalTone;\n            if (context === null || context === void 0 ? void 0 : (_context_emotionalTone = context.emotionalTone) === null || _context_emotionalTone === void 0 ? void 0 : _context_emotionalTone.personality) {\n                contextParts.push(isArabic ? \"الشخصية المطلوبة: \".concat(context.emotionalTone.personality.substring(0, 60), \"...\") : \"Required Personality: \".concat(context.emotionalTone.personality.substring(0, 60), \"...\"));\n            }\n        }\n        return contextParts.length > 0 ? (isArabic ? \"السياق الحالي:\\n\" : \"Current Context:\\n\") + contextParts.join(\"\\n\") : isArabic ? \"مشروع جديد\" : \"New Project\";\n    };\n    const parseSuggestions = (content)=>{\n        // تقسيم المحتوى إلى اقتراحات منفصلة\n        const lines = content.split(\"\\n\").filter((line)=>line.trim());\n        const suggestions = [];\n        for (const line of lines){\n            // البحث عن الأسطر المرقمة أو التي تبدأ برقم\n            if (/^\\d+[.\\-\\)]\\s*/.test(line.trim()) || /^[•\\-\\*]\\s*/.test(line.trim())) {\n                const cleaned = line.replace(/^\\d+[.\\-\\)]\\s*/, \"\").replace(/^[•\\-\\*]\\s*/, \"\").trim();\n                if (cleaned && cleaned.length > 10) {\n                    suggestions.push(cleaned);\n                }\n            } else if (line.trim().length > 20 && !line.includes(\":\") && suggestions.length < 3) {\n                suggestions.push(line.trim());\n            }\n        }\n        // إذا لم نجد اقتراحات مرقمة، نقسم النص إلى جمل\n        if (suggestions.length === 0) {\n            const sentences = content.split(/[.!?]+/).filter((s)=>s.trim().length > 20);\n            return sentences.slice(0, 3).map((s)=>s.trim());\n        }\n        return suggestions.slice(0, 3);\n    };\n    const copyToClipboard = async (text, index)=>{\n        try {\n            await navigator.clipboard.writeText(text);\n            setCopiedIndex(index);\n            setTimeout(()=>setCopiedIndex(null), 2000);\n        } catch (error) {\n            console.error(\"Failed to copy:\", error);\n        }\n    };\n    const regenerateContent = async ()=>{\n        if (generatedSuggestions.length > 1) {\n            // استخدام الاقتراح التالي إذا كان متوفراً\n            const currentIndex = generatedSuggestions.findIndex((s)=>s === fieldValue);\n            const nextIndex = (currentIndex + 1) % generatedSuggestions.length;\n            onValueChange(generatedSuggestions[nextIndex]);\n        } else {\n            // توليد محتوى جديد\n            await generateSuggestions();\n        }\n    };\n    // تجنب مشاكل الهيدريشن\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-2 \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative flex items-center gap-2 px-4 py-2.5 rounded-lg font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-blue-500/80 to-indigo-600/80 text-white shadow-md opacity-50 font-arabic\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 432,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: [\n                            \"\\uD83D\\uDCC4 \",\n                            isArabic ? \"توليد بالذكاء الاصطناعي\" : \"Generate with AI\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 433,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                lineNumber: 431,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n            lineNumber: 430,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center gap-2 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: generateSuggestions,\n                disabled: isGenerating || !hasValidProvider,\n                className: \"relative flex items-center gap-2 px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group \".concat(hasValidProvider ? \"bg-gradient-to-br from-purple-500/80 via-blue-500/80 to-indigo-600/80 hover:from-purple-600/90 hover:via-blue-600/90 hover:to-indigo-700/90 text-white shadow-lg hover:shadow-xl hover:shadow-purple-500/25 hover:scale-105 active:scale-95\" : \"bg-gray-200/50 dark:bg-gray-700/50 text-gray-400 dark:text-gray-500 cursor-not-allowed border-gray-300/30 dark:border-gray-600/30\", \" \").concat(isGenerating ? \"animate-pulse scale-105\" : \"\", \" \").concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                title: hasValidProvider ? isArabic ? \"توليد سريع محسّن - أقل من 5 ثواني\" : \"Fast optimized generation - under 5 seconds\" : translations.noProviders,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 448,\n                        columnNumber: 9\n                    }, this),\n                    hasValidProvider && !isGenerating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 -skew-x-12 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 group-hover:animate-shimmer\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 452,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex items-center gap-2 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                        children: [\n                            isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-4 h-4 animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 457,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-4 h-4 group-hover:rotate-12 transition-transform duration-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 459,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-arabic font-medium\",\n                                children: isGenerating ? translations.generating : translations.fastGeneration\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 461,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 455,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                lineNumber: 441,\n                columnNumber: 7\n            }, this),\n            fieldValue && generatedSuggestions.length > 0 && !isGenerating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: regenerateContent,\n                className: \"relative flex items-center gap-2 px-3 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-orange-500/80 to-red-500/80 hover:from-orange-600/90 hover:to-red-600/90 text-white shadow-lg hover:shadow-xl hover:shadow-orange-500/25 hover:scale-105 active:scale-95 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                title: isArabic ? \"إعادة توليد\" : \"Regenerate\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 474,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex items-center gap-1 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-4 h-4 group-hover:rotate-180 transition-transform duration-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 476,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-arabic font-medium\",\n                                children: isArabic ? \"إعادة توليد\" : \"Regenerate\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 477,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 475,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                lineNumber: 469,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n        lineNumber: 440,\n        columnNumber: 5\n    }, this);\n}\n_s(SmartFieldAssistant, \"ZrnkxJH8z8I5/fadLS+VIi/I8fM=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore\n    ];\n});\n_c = SmartFieldAssistant;\nvar _c;\n$RefreshReg$(_c, \"SmartFieldAssistant\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/SmartFieldAssistant.tsx\n"));

/***/ })

});