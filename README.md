# 🧠 Craftery - AI-powered Idea Builder

**Craftery** is an innovative, multi-page interactive web application designed to streamline the creation of organized, coherent, and actionable context for AI-driven projects.

## ✨ Features

### 🧭 Multi-Module Architecture
- **Project Definition** 🎯 - Define scope, users, and goals
- **Context Map** 🗺️ - Time, language, location, and behavioral aspects
- **Emotional Tone** ✨ - Personality, communication style, and user experience
- **Technical Layer** ⚙️ - Programming languages, frameworks, and infrastructure
- **Legal & Privacy** 🔒 - Compliance, risks, and ethical considerations
- **Final Preview** 📋 - Complete context export and review

### 🎨 User Experience
- **Two-Panel Layout**: Smart questions on the left, real-time outputs on the right
- **Smart Questions**: AI-powered suggestions and guided prompts
- **Multiple Output Formats**: Markdown, HTML, JSON, and YAML
- **Progress Tracking**: Visual progress indicators across all modules
- **Auto-Save**: Automatic data persistence with visual indicators

### 🌐 Multilingual Support
- **Arabic & English**: Full RTL support for Arabic interface
- **Dynamic Language Switching**: Toggle between languages instantly
- **Localized Content**: All text, prompts, and suggestions in both languages

### 🌙 Modern UI/UX
- **Dark/Light Mode**: Seamless theme switching
- **Responsive Design**: Works perfectly on desktop and mobile
- **Smooth Animations**: Polished transitions and interactions
- **Accessibility**: ARIA labels and keyboard navigation

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- npm or yarn

### Installation

```bash
# Clone the repository
git clone https://github.com/your-username/contextkit.git
cd contextkit

# Install dependencies
npm install

# Start development server
npm run dev
```

Visit `http://localhost:3000` to start building your AI context!

## 🏗️ Technology Stack

- **Framework**: Next.js 14 with App Router
- **Styling**: Tailwind CSS
- **State Management**: Zustand with persistence
- **Language**: TypeScript
- **Icons**: Emoji-based design system
- **Deployment**: Vercel-ready

## 📱 Usage

### 1. Start Building Context
Navigate through the modules in order or jump to any specific section:

1. **Project Definition** - Define your AI project's core purpose
2. **Context Map** - Set temporal, linguistic, and geographic context
3. **Emotional Tone** - Define personality and communication style
4. **Technical Layer** - Specify technical requirements and architecture
5. **Legal & Privacy** - Address compliance and ethical considerations
6. **Final Preview** - Export complete context in multiple formats

### 2. Smart Questions
Each module contains intelligent questions with:
- **AI Suggestions** 🧠 - Click to see contextual advice
- **Copy Individual Answers** 📎 - Copy specific responses
- **Prompt Templates** 🚀 - Generate AI prompts from your answers

### 3. Output Formats
Export your context in multiple formats:
- **Markdown** - Perfect for documentation
- **HTML** - Ready for web integration
- **JSON** - Structured data for APIs
- **YAML** - Configuration and deployment

### 4. Progress Tracking
- Visual progress bars for each module
- Overall completion percentage
- Quick navigation between modules
- Reset functionality for starting over

## 🎯 Use Cases

### For AI Developers
- **Project Planning**: Structure AI project requirements
- **Team Alignment**: Share comprehensive project context
- **Documentation**: Generate professional project documentation

### For Product Managers
- **Stakeholder Communication**: Clear project scope and requirements
- **Risk Assessment**: Identify legal and technical challenges
- **Resource Planning**: Technical requirements and timeline planning

### For Consultants
- **Client Onboarding**: Systematic requirement gathering
- **Proposal Generation**: Professional project documentation
- **Knowledge Transfer**: Structured handoff documentation

## 🔧 Configuration

### Environment Variables
Create a `.env.local` file:

```env
# Optional: Analytics
NEXT_PUBLIC_GA_ID=your-google-analytics-id

# Optional: Error Tracking
NEXT_PUBLIC_SENTRY_DSN=your-sentry-dsn
```

### Customization
- **Themes**: Modify `tailwind.config.js` for custom colors
- **Languages**: Add new languages in `src/store/contextStore.ts`
- **Modules**: Extend or modify modules in respective page files

## 📊 Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── project-definition/ # Module pages
│   ├── context-map/
│   ├── emotional-tone/
│   ├── technical-layer/
│   ├── legal-risk/
│   └── final-preview/
├── components/             # Reusable components
│   ├── ModuleLayout.tsx    # Module page layout
│   ├── SmartQuestion.tsx   # Interactive question component
│   ├── OutputPanel.tsx     # Multi-format output display
│   ├── ProgressIndicator.tsx # Progress tracking
│   └── ThemeProvider.tsx   # Theme management
└── store/                  # State management
    └── contextStore.ts     # Zustand store with persistence
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Workflow
1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built with ❤️ using Next.js and Tailwind CSS
- Inspired by the need for better AI project documentation
- Special thanks to the open-source community

## 📞 Support

- **Documentation**: [Wiki](https://github.com/your-username/contextkit/wiki)
- **Issues**: [GitHub Issues](https://github.com/your-username/contextkit/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-username/contextkit/discussions)

---

**Ready to build better AI projects?** [Start with ContextKit](http://localhost:3000) 🚀
