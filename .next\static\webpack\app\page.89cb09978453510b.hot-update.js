"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/projectOptions.ts":
/*!***********************************!*\
  !*** ./src/lib/projectOptions.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BUDGET_RANGES: function() { return /* binding */ BUDGET_RANGES; },\n/* harmony export */   COMPLEXITY_LEVELS: function() { return /* binding */ COMPLEXITY_LEVELS; },\n/* harmony export */   DEPLOYMENT_TYPES: function() { return /* binding */ DEPLOYMENT_TYPES; },\n/* harmony export */   GEOGRAPHIC_REGIONS: function() { return /* binding */ GEOGRAPHIC_REGIONS; },\n/* harmony export */   PROGRAMMING_LANGUAGES: function() { return /* binding */ PROGRAMMING_LANGUAGES; },\n/* harmony export */   PROJECT_NATURE: function() { return /* binding */ PROJECT_NATURE; },\n/* harmony export */   PROJECT_TYPES: function() { return /* binding */ PROJECT_TYPES; },\n/* harmony export */   TARGET_PLATFORMS: function() { return /* binding */ TARGET_PLATFORMS; },\n/* harmony export */   TEAM_SIZES: function() { return /* binding */ TEAM_SIZES; }\n/* harmony export */ });\n// خيارات التخصيص المتقدمة للمشاريع\n// أنواع المشاريع\nconst PROJECT_TYPES = [\n    {\n        id: \"web-app\",\n        label: \"Web Application\",\n        labelAr: \"تطبيق ويب\",\n        description: \"Browser-based application accessible via web browsers\",\n        descriptionAr: \"تطبيق يعمل في المتصفح ويمكن الوصول إليه عبر متصفحات الويب\",\n        icon: \"\\uD83C\\uDF10\"\n    },\n    {\n        id: \"mobile-app\",\n        label: \"Mobile Application\",\n        labelAr: \"تطبيق جوال\",\n        description: \"Native or cross-platform mobile application\",\n        descriptionAr: \"تطبيق جوال أصلي أو متعدد المنصات\",\n        icon: \"\\uD83D\\uDCF1\"\n    },\n    {\n        id: \"desktop-app\",\n        label: \"Desktop Application\",\n        labelAr: \"تطبيق سطح المكتب\",\n        description: \"Native desktop application for Windows, macOS, or Linux\",\n        descriptionAr: \"تطبيق سطح مكتب أصلي لـ Windows أو macOS أو Linux\",\n        icon: \"\\uD83D\\uDCBB\"\n    },\n    {\n        id: \"api-service\",\n        label: \"API/Microservice\",\n        labelAr: \"خدمة API/مايكروسيرفس\",\n        description: \"Backend API or microservice architecture\",\n        descriptionAr: \"خدمة API خلفية أو هندسة مايكروسيرفس\",\n        icon: \"\\uD83D\\uDD0C\"\n    },\n    {\n        id: \"ai-model\",\n        label: \"AI/ML Model\",\n        labelAr: \"نموذج ذكاء اصطناعي\",\n        description: \"Machine learning model or AI system\",\n        descriptionAr: \"نموذج تعلم آلي أو نظام ذكاء اصطناعي\",\n        icon: \"\\uD83E\\uDD16\"\n    },\n    {\n        id: \"chatbot\",\n        label: \"Chatbot/Virtual Assistant\",\n        labelAr: \"شات بوت/مساعد افتراضي\",\n        description: \"Conversational AI or chatbot system\",\n        descriptionAr: \"نظام ذكاء اصطناعي محادثة أو شات بوت\",\n        icon: \"\\uD83D\\uDCAC\"\n    },\n    {\n        id: \"data-analytics\",\n        label: \"Data Analytics Platform\",\n        labelAr: \"منصة تحليل البيانات\",\n        description: \"Data processing and analytics solution\",\n        descriptionAr: \"حل معالجة وتحليل البيانات\",\n        icon: \"\\uD83D\\uDCCA\"\n    },\n    {\n        id: \"iot-system\",\n        label: \"IoT System\",\n        labelAr: \"نظام إنترنت الأشياء\",\n        description: \"Internet of Things connected system\",\n        descriptionAr: \"نظام متصل بإنترنت الأشياء\",\n        icon: \"\\uD83C\\uDF10\"\n    }\n];\n// المنصات المستهدفة\nconst TARGET_PLATFORMS = [\n    {\n        id: \"web-browsers\",\n        label: \"Web Browsers\",\n        labelAr: \"متصفحات الويب\",\n        description: \"Chrome, Firefox, Safari, Edge\",\n        descriptionAr: \"كروم، فايرفوكس، سفاري، إيدج\",\n        icon: \"\\uD83C\\uDF10\"\n    },\n    {\n        id: \"android\",\n        label: \"Android\",\n        labelAr: \"أندرويد\",\n        description: \"Android mobile devices\",\n        descriptionAr: \"أجهزة أندرويد المحمولة\",\n        icon: \"\\uD83E\\uDD16\"\n    },\n    {\n        id: \"ios\",\n        label: \"iOS\",\n        labelAr: \"iOS\",\n        description: \"iPhone and iPad devices\",\n        descriptionAr: \"أجهزة آيفون وآيباد\",\n        icon: \"\\uD83C\\uDF4E\"\n    },\n    {\n        id: \"windows\",\n        label: \"Windows\",\n        labelAr: \"ويندوز\",\n        description: \"Windows desktop and server\",\n        descriptionAr: \"سطح مكتب وخادم ويندوز\",\n        icon: \"\\uD83E\\uDE9F\"\n    },\n    {\n        id: \"macos\",\n        label: \"macOS\",\n        labelAr: \"macOS\",\n        description: \"Apple macOS desktop\",\n        descriptionAr: \"سطح مكتب آبل macOS\",\n        icon: \"\\uD83C\\uDF4E\"\n    },\n    {\n        id: \"linux\",\n        label: \"Linux\",\n        labelAr: \"لينكس\",\n        description: \"Linux distributions\",\n        descriptionAr: \"توزيعات لينكس\",\n        icon: \"\\uD83D\\uDC27\"\n    },\n    {\n        id: \"cloud\",\n        label: \"Cloud Platforms\",\n        labelAr: \"المنصات السحابية\",\n        description: \"AWS, Azure, Google Cloud\",\n        descriptionAr: \"AWS، Azure، Google Cloud\",\n        icon: \"☁️\"\n    },\n    {\n        id: \"embedded\",\n        label: \"Embedded Systems\",\n        labelAr: \"الأنظمة المدمجة\",\n        description: \"IoT devices, microcontrollers\",\n        descriptionAr: \"أجهزة إنترنت الأشياء، المتحكمات الدقيقة\",\n        icon: \"\\uD83D\\uDD27\"\n    }\n];\n// لغات البرمجة الأساسية\nconst PROGRAMMING_LANGUAGES = [\n    {\n        id: \"javascript\",\n        label: \"JavaScript/TypeScript\",\n        labelAr: \"جافا سكريبت/تايب سكريبت\",\n        description: \"Modern web development\",\n        descriptionAr: \"تطوير الويب الحديث\",\n        icon: \"\\uD83D\\uDFE8\"\n    },\n    {\n        id: \"nextjs-tailwind-shadcn\",\n        label: \"Next.js + Tailwind + shadcn/ui\",\n        labelAr: \"Next.js + Tailwind + shadcn/ui\",\n        description: \"Modern React framework with styling and UI components\",\n        descriptionAr: \"إطار عمل React حديث مع التصميم ومكونات واجهة المستخدم\",\n        icon: \"⚡\"\n    },\n    {\n        id: \"python\",\n        label: \"Python\",\n        labelAr: \"بايثون\",\n        description: \"AI/ML, backend, data science\",\n        descriptionAr: \"ذكاء اصطناعي، خلفية، علوم البيانات\",\n        icon: \"\\uD83D\\uDC0D\"\n    },\n    {\n        id: \"java\",\n        label: \"Java\",\n        labelAr: \"جافا\",\n        description: \"Enterprise applications, Android\",\n        descriptionAr: \"تطبيقات المؤسسات، أندرويد\",\n        icon: \"☕\"\n    },\n    {\n        id: \"csharp\",\n        label: \"C#\",\n        labelAr: \"سي شارب\",\n        description: \".NET ecosystem, Windows apps\",\n        descriptionAr: \"نظام .NET، تطبيقات ويندوز\",\n        icon: \"\\uD83D\\uDD37\"\n    },\n    {\n        id: \"swift\",\n        label: \"Swift\",\n        labelAr: \"سويفت\",\n        description: \"iOS and macOS development\",\n        descriptionAr: \"تطوير iOS و macOS\",\n        icon: \"\\uD83E\\uDD89\"\n    },\n    {\n        id: \"kotlin\",\n        label: \"Kotlin\",\n        labelAr: \"كوتلن\",\n        description: \"Android development, JVM\",\n        descriptionAr: \"تطوير أندرويد، JVM\",\n        icon: \"\\uD83D\\uDFE3\"\n    },\n    {\n        id: \"rust\",\n        label: \"Rust\",\n        labelAr: \"رست\",\n        description: \"System programming, performance\",\n        descriptionAr: \"برمجة الأنظمة، الأداء\",\n        icon: \"\\uD83E\\uDD80\"\n    },\n    {\n        id: \"go\",\n        label: \"Go\",\n        labelAr: \"جو\",\n        description: \"Backend services, microservices\",\n        descriptionAr: \"خدمات خلفية، مايكروسيرفس\",\n        icon: \"\\uD83D\\uDC39\"\n    },\n    {\n        id: \"cpp\",\n        label: \"C++\",\n        labelAr: \"سي++\",\n        description: \"High-performance applications\",\n        descriptionAr: \"تطبيقات عالية الأداء\",\n        icon: \"⚡\"\n    },\n    {\n        id: \"php\",\n        label: \"PHP\",\n        labelAr: \"PHP\",\n        description: \"Web development, server-side\",\n        descriptionAr: \"تطوير الويب، جانب الخادم\",\n        icon: \"\\uD83D\\uDC18\"\n    },\n    {\n        id: \"ruby\",\n        label: \"Ruby\",\n        labelAr: \"روبي\",\n        description: \"Web applications, rapid development\",\n        descriptionAr: \"تطبيقات الويب، التطوير السريع\",\n        icon: \"\\uD83D\\uDC8E\"\n    },\n    {\n        id: \"dart\",\n        label: \"Dart/Flutter\",\n        labelAr: \"دارت/فلاتر\",\n        description: \"Cross-platform mobile apps\",\n        descriptionAr: \"تطبيقات جوال متعددة المنصات\",\n        icon: \"\\uD83C\\uDFAF\"\n    }\n];\n// مستويات التعقيد\nconst COMPLEXITY_LEVELS = [\n    {\n        id: \"simple\",\n        label: \"Simple\",\n        labelAr: \"بسيط\",\n        description: \"Basic functionality, minimal features\",\n        descriptionAr: \"وظائف أساسية، ميزات قليلة\",\n        icon: \"\\uD83D\\uDFE2\"\n    },\n    {\n        id: \"moderate\",\n        label: \"Moderate\",\n        labelAr: \"متوسط\",\n        description: \"Standard features, some integrations\",\n        descriptionAr: \"ميزات قياسية، بعض التكاملات\",\n        icon: \"\\uD83D\\uDFE1\"\n    },\n    {\n        id: \"complex\",\n        label: \"Complex\",\n        labelAr: \"معقد\",\n        description: \"Advanced features, multiple integrations\",\n        descriptionAr: \"ميزات متقدمة، تكاملات متعددة\",\n        icon: \"\\uD83D\\uDFE0\"\n    },\n    {\n        id: \"enterprise\",\n        label: \"Enterprise\",\n        labelAr: \"مؤسسي\",\n        description: \"Large-scale, high availability, security\",\n        descriptionAr: \"واسع النطاق، توفر عالي، أمان\",\n        icon: \"\\uD83D\\uDD34\"\n    }\n];\n// نطاقات الميزانية\nconst BUDGET_RANGES = [\n    {\n        id: \"startup\",\n        label: \"Startup Budget\",\n        labelAr: \"ميزانية ناشئة\",\n        description: \"Under $10K\",\n        descriptionAr: \"أقل من 10 آلاف دولار\",\n        icon: \"\\uD83D\\uDCB0\"\n    },\n    {\n        id: \"small\",\n        label: \"Small Project\",\n        labelAr: \"مشروع صغير\",\n        description: \"$10K - $50K\",\n        descriptionAr: \"10-50 ألف دولار\",\n        icon: \"\\uD83D\\uDCB5\"\n    },\n    {\n        id: \"medium\",\n        label: \"Medium Project\",\n        labelAr: \"مشروع متوسط\",\n        description: \"$50K - $200K\",\n        descriptionAr: \"50-200 ألف دولار\",\n        icon: \"\\uD83D\\uDCB8\"\n    },\n    {\n        id: \"large\",\n        label: \"Large Project\",\n        labelAr: \"مشروع كبير\",\n        description: \"$200K - $1M\",\n        descriptionAr: \"200 ألف - مليون دولار\",\n        icon: \"\\uD83D\\uDC8E\"\n    },\n    {\n        id: \"enterprise\",\n        label: \"Enterprise\",\n        labelAr: \"مؤسسي\",\n        description: \"$1M+\",\n        descriptionAr: \"أكثر من مليون دولار\",\n        icon: \"\\uD83C\\uDFE6\"\n    }\n];\n// أحجام الفريق\nconst TEAM_SIZES = [\n    {\n        id: \"solo\",\n        label: \"Solo Developer\",\n        labelAr: \"مطور منفرد\",\n        description: \"1 person\",\n        descriptionAr: \"شخص واحد\",\n        icon: \"\\uD83D\\uDC64\"\n    },\n    {\n        id: \"small\",\n        label: \"Small Team\",\n        labelAr: \"فريق صغير\",\n        description: \"2-5 people\",\n        descriptionAr: \"2-5 أشخاص\",\n        icon: \"\\uD83D\\uDC65\"\n    },\n    {\n        id: \"medium\",\n        label: \"Medium Team\",\n        labelAr: \"فريق متوسط\",\n        description: \"6-15 people\",\n        descriptionAr: \"6-15 شخص\",\n        icon: \"\\uD83D\\uDC68‍\\uD83D\\uDC69‍\\uD83D\\uDC67‍\\uD83D\\uDC66\"\n    },\n    {\n        id: \"large\",\n        label: \"Large Team\",\n        labelAr: \"فريق كبير\",\n        description: \"16-50 people\",\n        descriptionAr: \"16-50 شخص\",\n        icon: \"\\uD83C\\uDFE2\"\n    },\n    {\n        id: \"enterprise\",\n        label: \"Enterprise Team\",\n        labelAr: \"فريق مؤسسي\",\n        description: \"50+ people\",\n        descriptionAr: \"أكثر من 50 شخص\",\n        icon: \"\\uD83C\\uDFED\"\n    }\n];\n// أنواع النشر\nconst DEPLOYMENT_TYPES = [\n    {\n        id: \"cloud\",\n        label: \"Cloud Deployment\",\n        labelAr: \"نشر سحابي\",\n        description: \"AWS, Azure, Google Cloud, Vercel\",\n        descriptionAr: \"AWS، Azure، Google Cloud، Vercel\",\n        icon: \"☁️\"\n    },\n    {\n        id: \"on-premise\",\n        label: \"On-Premise\",\n        labelAr: \"محلي\",\n        description: \"Self-hosted infrastructure\",\n        descriptionAr: \"بنية تحتية ذاتية الاستضافة\",\n        icon: \"\\uD83C\\uDFE2\"\n    },\n    {\n        id: \"hybrid\",\n        label: \"Hybrid\",\n        labelAr: \"هجين\",\n        description: \"Mix of cloud and on-premise\",\n        descriptionAr: \"مزيج من السحابي والمحلي\",\n        icon: \"\\uD83D\\uDD04\"\n    },\n    {\n        id: \"edge\",\n        label: \"Edge Computing\",\n        labelAr: \"حوسبة الحافة\",\n        description: \"Distributed edge deployment\",\n        descriptionAr: \"نشر موزع على الحافة\",\n        icon: \"\\uD83C\\uDF10\"\n    },\n    {\n        id: \"mobile-stores\",\n        label: \"App Stores\",\n        labelAr: \"متاجر التطبيقات\",\n        description: \"Google Play, App Store\",\n        descriptionAr: \"جوجل بلاي، آب ستور\",\n        icon: \"\\uD83D\\uDCF1\"\n    }\n];\n// المناطق الجغرافية والدول\nconst GEOGRAPHIC_REGIONS = [\n    {\n        id: \"morocco\",\n        label: \"Morocco\",\n        labelAr: \"المغرب\",\n        description: \"Kingdom of Morocco - North Africa\",\n        descriptionAr: \"المملكة المغربية - شمال أفريقيا\",\n        icon: \"\\uD83C\\uDDF2\\uD83C\\uDDE6\"\n    },\n    {\n        id: \"middle-east\",\n        label: \"Middle East\",\n        labelAr: \"الشرق الأوسط\",\n        description: \"Middle Eastern countries and regions\",\n        descriptionAr: \"دول ومناطق الشرق الأوسط\",\n        icon: \"\\uD83D\\uDD4C\"\n    },\n    {\n        id: \"north-africa\",\n        label: \"North Africa\",\n        labelAr: \"شمال أفريقيا\",\n        description: \"Northern African countries\",\n        descriptionAr: \"دول شمال أفريقيا\",\n        icon: \"\\uD83C\\uDFDC️\"\n    },\n    {\n        id: \"africa\",\n        label: \"Africa\",\n        labelAr: \"أفريقيا\",\n        description: \"African continent\",\n        descriptionAr: \"القارة الأفريقية\",\n        icon: \"\\uD83C\\uDF0D\"\n    },\n    {\n        id: \"arab-world\",\n        label: \"Arab World\",\n        labelAr: \"العالم العربي\",\n        description: \"Arabic-speaking countries and regions\",\n        descriptionAr: \"الدول والمناطق الناطقة بالعربية\",\n        icon: \"\\uD83C\\uDF19\"\n    },\n    {\n        id: \"europe\",\n        label: \"Europe\",\n        labelAr: \"أوروبا\",\n        description: \"European countries\",\n        descriptionAr: \"الدول الأوروبية\",\n        icon: \"\\uD83C\\uDDEA\\uD83C\\uDDFA\"\n    },\n    {\n        id: \"north-america\",\n        label: \"North America\",\n        labelAr: \"أمريكا الشمالية\",\n        description: \"United States, Canada, and Mexico\",\n        descriptionAr: \"الولايات المتحدة وكندا والمكسيك\",\n        icon: \"\\uD83C\\uDF0E\"\n    },\n    {\n        id: \"asia\",\n        label: \"Asia\",\n        labelAr: \"آسيا\",\n        description: \"Asian countries and regions\",\n        descriptionAr: \"الدول والمناطق الآسيوية\",\n        icon: \"\\uD83C\\uDF0F\"\n    },\n    {\n        id: \"global\",\n        label: \"Global/Worldwide\",\n        labelAr: \"عالمي/في جميع أنحاء العالم\",\n        description: \"Worldwide coverage and availability\",\n        descriptionAr: \"تغطية وتوفر عالمي\",\n        icon: \"\\uD83C\\uDF10\"\n    }\n];\n// طبيعة المشروع ونوع الأعمال\nconst PROJECT_NATURE = [\n    {\n        id: \"graduation-clothing\",\n        label: \"Graduation Clothing\",\n        labelAr: \"لباس التخرج\",\n        description: \"Academic graduation gowns, caps, and accessories\",\n        descriptionAr: \"أثواب التخرج الأكاديمية والقبعات والإكسسوارات\",\n        icon: \"\\uD83C\\uDF93\"\n    },\n    {\n        id: \"fashion-retail\",\n        label: \"Fashion & Retail\",\n        labelAr: \"الأزياء والتجارة\",\n        description: \"Clothing, fashion, and retail business\",\n        descriptionAr: \"الملابس والأزياء وأعمال التجارة\",\n        icon: \"\\uD83D\\uDC57\"\n    },\n    {\n        id: \"education\",\n        label: \"Education\",\n        labelAr: \"التعليم\",\n        description: \"Educational services and platforms\",\n        descriptionAr: \"الخدمات والمنصات التعليمية\",\n        icon: \"\\uD83D\\uDCDA\"\n    },\n    {\n        id: \"healthcare\",\n        label: \"Healthcare\",\n        labelAr: \"الرعاية الصحية\",\n        description: \"Medical and healthcare services\",\n        descriptionAr: \"الخدمات الطبية والرعاية الصحية\",\n        icon: \"\\uD83C\\uDFE5\"\n    },\n    {\n        id: \"fintech\",\n        label: \"Financial Technology\",\n        labelAr: \"التكنولوجيا المالية\",\n        description: \"Banking, payments, and financial services\",\n        descriptionAr: \"الخدمات المصرفية والمدفوعات والخدمات المالية\",\n        icon: \"\\uD83D\\uDCB3\"\n    },\n    {\n        id: \"ecommerce\",\n        label: \"E-commerce\",\n        labelAr: \"التجارة الإلكترونية\",\n        description: \"Online shopping and marketplace platforms\",\n        descriptionAr: \"منصات التسوق الإلكتروني والأسواق\",\n        icon: \"\\uD83D\\uDED2\"\n    },\n    {\n        id: \"food-beverage\",\n        label: \"Food & Beverage\",\n        labelAr: \"الطعام والشراب\",\n        description: \"Restaurant, food delivery, and culinary services\",\n        descriptionAr: \"المطاعم وتوصيل الطعام والخدمات الطهوية\",\n        icon: \"\\uD83C\\uDF7D️\"\n    },\n    {\n        id: \"travel-tourism\",\n        label: \"Travel & Tourism\",\n        labelAr: \"السفر والسياحة\",\n        description: \"Travel booking, tourism, and hospitality\",\n        descriptionAr: \"حجز السفر والسياحة والضيافة\",\n        icon: \"✈️\"\n    },\n    {\n        id: \"real-estate\",\n        label: \"Real Estate\",\n        labelAr: \"العقارات\",\n        description: \"Property management and real estate services\",\n        descriptionAr: \"إدارة الممتلكات وخدمات العقارات\",\n        icon: \"\\uD83C\\uDFE0\"\n    },\n    {\n        id: \"entertainment\",\n        label: \"Entertainment & Media\",\n        labelAr: \"الترفيه والإعلام\",\n        description: \"Gaming, streaming, and media content\",\n        descriptionAr: \"الألعاب والبث والمحتوى الإعلامي\",\n        icon: \"\\uD83C\\uDFAC\"\n    },\n    {\n        id: \"logistics\",\n        label: \"Logistics & Transportation\",\n        labelAr: \"اللوجستيات والنقل\",\n        description: \"Shipping, delivery, and transportation services\",\n        descriptionAr: \"خدمات الشحن والتوصيل والنقل\",\n        icon: \"\\uD83D\\uDE9A\"\n    },\n    {\n        id: \"agriculture\",\n        label: \"Agriculture & Farming\",\n        labelAr: \"الزراعة والفلاحة\",\n        description: \"Agricultural technology and farming solutions\",\n        descriptionAr: \"التكنولوجيا الزراعية وحلول الفلاحة\",\n        icon: \"\\uD83C\\uDF3E\"\n    },\n    {\n        id: \"manufacturing\",\n        label: \"Manufacturing\",\n        labelAr: \"التصنيع\",\n        description: \"Industrial manufacturing and production\",\n        descriptionAr: \"التصنيع الصناعي والإنتاج\",\n        icon: \"\\uD83C\\uDFED\"\n    },\n    {\n        id: \"consulting\",\n        label: \"Consulting & Services\",\n        labelAr: \"الاستشارات والخدمات\",\n        description: \"Professional consulting and business services\",\n        descriptionAr: \"الاستشارات المهنية وخدمات الأعمال\",\n        icon: \"\\uD83D\\uDCBC\"\n    },\n    {\n        id: \"nonprofit\",\n        label: \"Non-Profit & NGO\",\n        labelAr: \"غير ربحي ومنظمات\",\n        description: \"Non-profit organizations and social causes\",\n        descriptionAr: \"المنظمات غير الربحية والقضايا الاجتماعية\",\n        icon: \"\\uD83E\\uDD1D\"\n    },\n    {\n        id: \"other\",\n        label: \"Other\",\n        labelAr: \"أخرى\",\n        description: \"Other business types and industries\",\n        descriptionAr: \"أنواع أعمال وصناعات أخرى\",\n        icon: \"\\uD83D\\uDCCB\"\n    }\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/projectOptions.ts\n"));

/***/ })

});