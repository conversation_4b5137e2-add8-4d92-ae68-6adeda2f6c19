"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/project-definition/page",{

/***/ "(app-pages-browser)/./src/components/SmartFieldAssistant.tsx":
/*!************************************************!*\
  !*** ./src/components/SmartFieldAssistant.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SmartFieldAssistant; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Sparkles,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Sparkles,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Sparkles,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wand-sparkles.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction SmartFieldAssistant(param) {\n    let { fieldName, fieldValue, onValueChange, placeholder, context, className = \"\" } = param;\n    _s();\n    const { currentLanguage, getActiveProviders, getAllData } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore)();\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [generatedSuggestions, setGeneratedSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showSuggestions, setShowSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [copiedIndex, setCopiedIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeProviders, setActiveProviders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isArabic = currentLanguage === \"ar\";\n    // تجنب مشاكل الهيدريشن\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n        setActiveProviders(getActiveProviders());\n    }, [\n        getActiveProviders\n    ]);\n    // تحقق من وجود مقدم خدمة صالح\n    const hasValidProvider = mounted && activeProviders.some((p)=>p.apiKey && p.validationStatus === \"valid\" && p.selectedModels && p.selectedModels.length > 0);\n    const translations = {\n        generateWithAI: isArabic ? \"\\uD83D\\uDCC4 توليد بالذكاء الاصطناعي\" : \"\\uD83D\\uDCC4 Generate with AI\",\n        generating: isArabic ? \"جاري التوليد...\" : \"Generating...\",\n        suggestions: isArabic ? \"اقتراحات ذكية\" : \"Smart Suggestions\",\n        useThis: isArabic ? \"استخدام هذا\" : \"Use This\",\n        copy: isArabic ? \"نسخ\" : \"Copy\",\n        copied: isArabic ? \"تم النسخ\" : \"Copied\",\n        noProviders: isArabic ? \"يرجى إعداد مقدم خدمة AI وتحديد النماذج في صفحة الإعدادات أولاً\" : \"Please configure an AI provider and select models in Settings first\",\n        error: isArabic ? \"حدث خطأ أثناء التوليد\" : \"Error occurred during generation\",\n        tryAgain: isArabic ? \"حاول مرة أخرى\" : \"Try Again\",\n        regenerate: isArabic ? \"إعادة توليد\" : \"Regenerate\",\n        fastGeneration: isArabic ? \"توليد سريع (محسّن)\" : \"Fast Generation (Optimized)\",\n        timeout: isArabic ? \"انتهت مهلة الطلب - حاول مرة أخرى\" : \"Request timeout - try again\"\n    };\n    const generateSuggestions = async ()=>{\n        if (!hasValidProvider) {\n            console.warn(\"No valid provider available:\", {\n                activeProviders,\n                hasValidProvider\n            });\n            alert(translations.noProviders);\n            return;\n        }\n        setIsGenerating(true);\n        try {\n            const allContext = getAllData();\n            const provider = activeProviders.find((p)=>p.apiKey && p.validationStatus === \"valid\");\n            console.log(\"Using provider:\", provider === null || provider === void 0 ? void 0 : provider.name, \"with model:\", provider === null || provider === void 0 ? void 0 : provider.selectedModels[0]);\n            if (!provider) {\n                throw new Error(\"No valid provider found\");\n            }\n            // إنشاء prompt ذكي بناءً على السياق والحقل\n            const prompt = createSmartPrompt(fieldName, fieldValue, allContext, isArabic);\n            console.log(\"Generated prompt:\", prompt);\n            const requestBody = {\n                providerId: provider.id,\n                apiKey: provider.apiKey,\n                model: provider.selectedModels[0] || \"gpt-3.5-turbo\",\n                messages: [\n                    {\n                        role: \"user\",\n                        content: prompt\n                    }\n                ],\n                context: allContext,\n                fieldName,\n                language: currentLanguage,\n                temperature: 0.7,\n                maxTokens: 200 // تقليل maxTokens بشكل كبير للسرعة\n            };\n            console.log(\"Sending request to API:\", requestBody);\n            // إضافة timeout للطلب من جانب العميل\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>{\n                controller.abort();\n            }, 35000); // 35 ثانية timeout\n            const response = await fetch(\"/api/llm/generate\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(requestBody),\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            console.log(\"API Response status:\", response.status);\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error(\"API Error:\", errorText);\n                throw new Error(\"API Error: \".concat(response.status, \" - \").concat(errorText));\n            }\n            const result = await response.json();\n            console.log(\"API Result:\", result);\n            if (result.success) {\n                // استخدام أول اقتراح مباشرة في خانة الكتابة\n                const suggestions = parseSuggestions(result.content);\n                console.log(\"Parsed suggestions:\", suggestions);\n                if (suggestions.length > 0) {\n                    // وضع أول اقتراح في خانة الكتابة\n                    onValueChange(suggestions[0]);\n                    // حفظ باقي الاقتراحات للاستخدام لاحقاً\n                    setGeneratedSuggestions(suggestions);\n                } else {\n                    const errorMsg = isArabic ? \"لم يتم العثور على اقتراحات مناسبة\" : \"No suitable suggestions found\";\n                    onValueChange(errorMsg);\n                }\n            } else {\n                throw new Error(result.error || \"Generation failed\");\n            }\n        } catch (error) {\n            console.error(\"Generation error:\", error);\n            let errorMessage = translations.error;\n            if (error instanceof Error) {\n                if (error.name === \"AbortError\") {\n                    errorMessage = translations.timeout;\n                } else if (error.message.includes(\"timeout\")) {\n                    errorMessage = translations.timeout;\n                } else {\n                    errorMessage = \"\".concat(translations.error, \": \").concat(error.message);\n                }\n            }\n            onValueChange(errorMessage);\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const createSmartPrompt = (fieldName, currentValue, context, isArabic)=>{\n        // استخراج الخيارات المتقدمة من السياق\n        const projectDef = (context === null || context === void 0 ? void 0 : context.projectDefinition) || {};\n        const advancedContext = {\n            projectType: projectDef.projectType,\n            projectNature: projectDef.projectNature,\n            geographicRegion: projectDef.geographicRegion,\n            targetPlatforms: projectDef.targetPlatforms || [],\n            primaryLanguages: projectDef.primaryLanguages || [],\n            complexity: projectDef.complexity,\n            deploymentType: projectDef.deploymentType\n        };\n        // إنشاء سياق متقدم للـ prompt مع التركيز على الخيارات الجديدة\n        const contextString = Object.entries(advancedContext).filter((param)=>{\n            let [_, value] = param;\n            return value && (Array.isArray(value) ? value.length > 0 : true);\n        }).map((param)=>{\n            let [key, value] = param;\n            return \"\".concat(key, \": \").concat(Array.isArray(value) ? value.join(\", \") : value);\n        }).join(\", \");\n        // تحسين الـ prompts لتكون أكثر تفصيلاً وذكاءً\n        const fieldPrompts = {\n            // Project Definition Module\n            name: {\n                ar: \"بناءً على طبيعة المشروع والمنطقة الجغرافية المستهدفة، اقترح 3 أسماء إبداعية ومناسبة للمشروع\",\n                en: \"Based on the project nature and target geographic region, suggest 3 creative and suitable project names\"\n            },\n            purpose: {\n                ar: \"اكتب 3 أوصاف مختلفة ومفصلة لغرض المشروع، مع مراعاة طبيعة المشروع والمنطقة الجغرافية\",\n                en: \"Write 3 different and detailed project purpose descriptions, considering project nature and geographic region\"\n            },\n            targetUsers: {\n                ar: \"حدد 3 مجموعات مختلفة من المستخدمين المستهدفين بناءً على طبيعة المشروع والمنطقة الجغرافية\",\n                en: \"Define 3 different target user groups based on project nature and geographic region\"\n            },\n            goals: {\n                ar: \"اقترح 3 أهداف محددة وقابلة للقياس للمشروع\",\n                en: \"Suggest 3 specific and measurable project goals\"\n            },\n            scope: {\n                ar: \"حدد 3 نطاقات مختلفة للمشروع (صغير، متوسط، كبير)\",\n                en: \"Define 3 different project scopes (small, medium, large)\"\n            },\n            timeline: {\n                ar: \"اقترح 3 جداول زمنية مختلفة للمشروع\",\n                en: \"Suggest 3 different project timelines\"\n            },\n            // Context Map Module\n            timeContext: {\n                ar: \"حدد 3 سياقات زمنية مختلفة مناسبة للمشروع\",\n                en: \"Define 3 different time contexts suitable for the project\"\n            },\n            language: {\n                ar: \"اقترح 3 استراتيجيات لغوية للمشروع\",\n                en: \"Suggest 3 language strategies for the project\"\n            },\n            location: {\n                ar: \"حدد 3 مواقع جغرافية مستهدفة للمشروع\",\n                en: \"Define 3 target geographical locations for the project\"\n            },\n            culturalContext: {\n                ar: \"اقترح 3 اعتبارات ثقافية مهمة للمشروع\",\n                en: \"Suggest 3 important cultural considerations for the project\"\n            },\n            // Emotional Tone Module\n            personality: {\n                ar: \"اقترح 3 شخصيات مختلفة للمشروع تناسب المستخدمين المستهدفين\",\n                en: \"Suggest 3 different project personalities that suit the target users\"\n            },\n            communicationStyle: {\n                ar: \"حدد 3 أساليب تواصل مختلفة مناسبة للمشروع\",\n                en: \"Define 3 different communication styles suitable for the project\"\n            },\n            // Technical Layer Module\n            programmingLanguages: {\n                ar: \"اقترح 3 لغات برمجة مناسبة للمشروع مع التبرير\",\n                en: \"Suggest 3 suitable programming languages for the project with justification\"\n            },\n            frameworks: {\n                ar: \"حدد 3 إطارات عمل تقنية مناسبة للمشروع\",\n                en: \"Define 3 technical frameworks suitable for the project\"\n            }\n        };\n        const fieldPrompt = fieldPrompts[fieldName];\n        const basePrompt = fieldPrompt ? isArabic ? fieldPrompt.ar : fieldPrompt.en : isArabic ? \"اقترح محتوى ذكي ومناسب لـ \".concat(fieldName) : \"Suggest smart and suitable content for \".concat(fieldName);\n        // بناء سياق أكثر ذكاءً\n        const contextInfo = buildIntelligentContext(context, fieldName, isArabic);\n        const instructions = isArabic ? \"قدم 3 اقتراحات مرقمة ومفصلة، كل اقتراح في سطر منفصل. اجعل كل اقتراح متماسكاً مع السياق العام للمشروع.\" : \"Provide 3 numbered and detailed suggestions, each on a separate line. Make each suggestion coherent with the overall project context.\";\n        return \"\".concat(contextInfo, \"\\n\").concat(basePrompt, \"\\n\").concat(instructions);\n    };\n    // وظيفة لبناء سياق ذكي\n    const buildIntelligentContext = (context, fieldName, isArabic)=>{\n        var _context_projectDefinition, _context_projectDefinition1, _context_projectDefinition2;\n        const contextParts = [];\n        const projectDef = (context === null || context === void 0 ? void 0 : context.projectDefinition) || {};\n        // أولوية للخيارات المتقدمة الجديدة - طبيعة المشروع والمنطقة الجغرافية\n        if (projectDef.projectNature) {\n            contextParts.push(isArabic ? \"طبيعة المشروع: \".concat(projectDef.projectNature) : \"Project Nature: \".concat(projectDef.projectNature));\n        }\n        if (projectDef.geographicRegion) {\n            contextParts.push(isArabic ? \"المنطقة الجغرافية: \".concat(projectDef.geographicRegion) : \"Geographic Region: \".concat(projectDef.geographicRegion));\n        }\n        // معلومات المشروع الأساسية\n        if (context === null || context === void 0 ? void 0 : (_context_projectDefinition = context.projectDefinition) === null || _context_projectDefinition === void 0 ? void 0 : _context_projectDefinition.name) {\n            contextParts.push(isArabic ? \"اسم المشروع: \".concat(context.projectDefinition.name) : \"Project Name: \".concat(context.projectDefinition.name));\n        }\n        if (context === null || context === void 0 ? void 0 : (_context_projectDefinition1 = context.projectDefinition) === null || _context_projectDefinition1 === void 0 ? void 0 : _context_projectDefinition1.purpose) {\n            contextParts.push(isArabic ? \"الغرض: \".concat(context.projectDefinition.purpose.substring(0, 100), \"...\") : \"Purpose: \".concat(context.projectDefinition.purpose.substring(0, 100), \"...\"));\n        }\n        if (context === null || context === void 0 ? void 0 : (_context_projectDefinition2 = context.projectDefinition) === null || _context_projectDefinition2 === void 0 ? void 0 : _context_projectDefinition2.targetUsers) {\n            contextParts.push(isArabic ? \"المستخدمون المستهدفون: \".concat(context.projectDefinition.targetUsers.substring(0, 80), \"...\") : \"Target Users: \".concat(context.projectDefinition.targetUsers.substring(0, 80), \"...\"));\n        }\n        // باقي الخيارات المتقدمة\n        if (projectDef.projectType) {\n            contextParts.push(isArabic ? \"نوع المشروع: \".concat(projectDef.projectType) : \"Project Type: \".concat(projectDef.projectType));\n        }\n        // طبيعة المشروع - خيار جديد مهم\n        if (projectDef.projectNature) {\n            contextParts.push(isArabic ? \"طبيعة المشروع: \".concat(projectDef.projectNature) : \"Project Nature: \".concat(projectDef.projectNature));\n        }\n        // المنطقة الجغرافية - خيار جديد مهم\n        if (projectDef.geographicRegion) {\n            contextParts.push(isArabic ? \"المنطقة الجغرافية: \".concat(projectDef.geographicRegion) : \"Geographic Region: \".concat(projectDef.geographicRegion));\n        }\n        if (projectDef.targetPlatforms && projectDef.targetPlatforms.length > 0) {\n            contextParts.push(isArabic ? \"المنصات المستهدفة: \".concat(projectDef.targetPlatforms.join(\", \")) : \"Target Platforms: \".concat(projectDef.targetPlatforms.join(\", \")));\n        }\n        if (projectDef.primaryLanguages && projectDef.primaryLanguages.length > 0) {\n            contextParts.push(isArabic ? \"لغات البرمجة: \".concat(projectDef.primaryLanguages.join(\", \")) : \"Programming Languages: \".concat(projectDef.primaryLanguages.join(\", \")));\n        }\n        if (projectDef.complexity) {\n            contextParts.push(isArabic ? \"مستوى التعقيد: \".concat(projectDef.complexity) : \"Complexity Level: \".concat(projectDef.complexity));\n        }\n        if (projectDef.teamSize) {\n            contextParts.push(isArabic ? \"حجم الفريق: \".concat(projectDef.teamSize) : \"Team Size: \".concat(projectDef.teamSize));\n        }\n        if (projectDef.budget) {\n            contextParts.push(isArabic ? \"نطاق الميزانية: \".concat(projectDef.budget) : \"Budget Range: \".concat(projectDef.budget));\n        }\n        // سياق إضافي حسب المجال\n        if (fieldName.includes(\"technical\") || fieldName.includes(\"programming\") || fieldName.includes(\"frameworks\")) {\n            var _context_technicalLayer;\n            if (context === null || context === void 0 ? void 0 : (_context_technicalLayer = context.technicalLayer) === null || _context_technicalLayer === void 0 ? void 0 : _context_technicalLayer.programmingLanguages) {\n                contextParts.push(isArabic ? \"التقنيات المستخدمة: \".concat(context.technicalLayer.programmingLanguages.substring(0, 60), \"...\") : \"Technologies: \".concat(context.technicalLayer.programmingLanguages.substring(0, 60), \"...\"));\n            }\n        }\n        if (fieldName.includes(\"emotional\") || fieldName.includes(\"personality\") || fieldName.includes(\"communication\")) {\n            var _context_emotionalTone;\n            if (context === null || context === void 0 ? void 0 : (_context_emotionalTone = context.emotionalTone) === null || _context_emotionalTone === void 0 ? void 0 : _context_emotionalTone.personality) {\n                contextParts.push(isArabic ? \"الشخصية المطلوبة: \".concat(context.emotionalTone.personality.substring(0, 60), \"...\") : \"Required Personality: \".concat(context.emotionalTone.personality.substring(0, 60), \"...\"));\n            }\n        }\n        return contextParts.length > 0 ? (isArabic ? \"السياق الحالي:\\n\" : \"Current Context:\\n\") + contextParts.join(\"\\n\") : isArabic ? \"مشروع جديد\" : \"New Project\";\n    };\n    const parseSuggestions = (content)=>{\n        // تقسيم المحتوى إلى اقتراحات منفصلة\n        const lines = content.split(\"\\n\").filter((line)=>line.trim());\n        const suggestions = [];\n        for (const line of lines){\n            // البحث عن الأسطر المرقمة أو التي تبدأ برقم\n            if (/^\\d+[.\\-\\)]\\s*/.test(line.trim()) || /^[•\\-\\*]\\s*/.test(line.trim())) {\n                const cleaned = line.replace(/^\\d+[.\\-\\)]\\s*/, \"\").replace(/^[•\\-\\*]\\s*/, \"\").trim();\n                if (cleaned && cleaned.length > 10) {\n                    suggestions.push(cleaned);\n                }\n            } else if (line.trim().length > 20 && !line.includes(\":\") && suggestions.length < 3) {\n                suggestions.push(line.trim());\n            }\n        }\n        // إذا لم نجد اقتراحات مرقمة، نقسم النص إلى جمل\n        if (suggestions.length === 0) {\n            const sentences = content.split(/[.!?]+/).filter((s)=>s.trim().length > 20);\n            return sentences.slice(0, 3).map((s)=>s.trim());\n        }\n        return suggestions.slice(0, 3);\n    };\n    const copyToClipboard = async (text, index)=>{\n        try {\n            await navigator.clipboard.writeText(text);\n            setCopiedIndex(index);\n            setTimeout(()=>setCopiedIndex(null), 2000);\n        } catch (error) {\n            console.error(\"Failed to copy:\", error);\n        }\n    };\n    const regenerateContent = async ()=>{\n        if (generatedSuggestions.length > 1) {\n            // استخدام الاقتراح التالي إذا كان متوفراً\n            const currentIndex = generatedSuggestions.findIndex((s)=>s === fieldValue);\n            const nextIndex = (currentIndex + 1) % generatedSuggestions.length;\n            onValueChange(generatedSuggestions[nextIndex]);\n        } else {\n            // توليد محتوى جديد\n            await generateSuggestions();\n        }\n    };\n    // تجنب مشاكل الهيدريشن\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-2 \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative flex items-center gap-2 px-4 py-2.5 rounded-lg font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-blue-500/80 to-indigo-600/80 text-white shadow-md opacity-50 font-arabic\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 447,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: [\n                            \"\\uD83D\\uDCC4 \",\n                            isArabic ? \"توليد بالذكاء الاصطناعي\" : \"Generate with AI\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 448,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                lineNumber: 446,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n            lineNumber: 445,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center gap-2 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: generateSuggestions,\n                disabled: isGenerating || !hasValidProvider,\n                className: \"relative flex items-center gap-2 px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group \".concat(hasValidProvider ? \"bg-gradient-to-br from-purple-500/80 via-blue-500/80 to-indigo-600/80 hover:from-purple-600/90 hover:via-blue-600/90 hover:to-indigo-700/90 text-white shadow-lg hover:shadow-xl hover:shadow-purple-500/25 hover:scale-105 active:scale-95\" : \"bg-gray-200/50 dark:bg-gray-700/50 text-gray-400 dark:text-gray-500 cursor-not-allowed border-gray-300/30 dark:border-gray-600/30\", \" \").concat(isGenerating ? \"animate-pulse scale-105\" : \"\", \" \").concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                title: hasValidProvider ? isArabic ? \"توليد سريع محسّن - أقل من 5 ثواني\" : \"Fast optimized generation - under 5 seconds\" : translations.noProviders,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 463,\n                        columnNumber: 9\n                    }, this),\n                    hasValidProvider && !isGenerating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 -skew-x-12 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 group-hover:animate-shimmer\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 467,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex items-center gap-2 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                        children: [\n                            isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-4 h-4 animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 472,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-4 h-4 group-hover:rotate-12 transition-transform duration-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 474,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-arabic font-medium\",\n                                children: isGenerating ? translations.generating : translations.fastGeneration\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 476,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 470,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                lineNumber: 456,\n                columnNumber: 7\n            }, this),\n            fieldValue && generatedSuggestions.length > 0 && !isGenerating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: regenerateContent,\n                className: \"relative flex items-center gap-2 px-3 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-orange-500/80 to-red-500/80 hover:from-orange-600/90 hover:to-red-600/90 text-white shadow-lg hover:shadow-xl hover:shadow-orange-500/25 hover:scale-105 active:scale-95 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                title: isArabic ? \"إعادة توليد\" : \"Regenerate\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 489,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex items-center gap-1 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-4 h-4 group-hover:rotate-180 transition-transform duration-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 491,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-arabic font-medium\",\n                                children: isArabic ? \"إعادة توليد\" : \"Regenerate\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 492,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 490,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                lineNumber: 484,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n        lineNumber: 455,\n        columnNumber: 5\n    }, this);\n}\n_s(SmartFieldAssistant, \"ZrnkxJH8z8I5/fadLS+VIi/I8fM=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore\n    ];\n});\n_c = SmartFieldAssistant;\nvar _c;\n$RefreshReg$(_c, \"SmartFieldAssistant\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/SmartFieldAssistant.tsx\n"));

/***/ })

});