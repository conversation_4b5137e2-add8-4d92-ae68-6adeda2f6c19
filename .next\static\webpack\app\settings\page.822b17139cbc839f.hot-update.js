"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/settings/page",{

/***/ "(app-pages-browser)/./src/app/settings/page.tsx":
/*!***********************************!*\
  !*** ./src/app/settings/page.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SettingsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* harmony import */ var _lib_llmProviders__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/llmProviders */ \"(app-pages-browser)/./src/lib/llmProviders.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowRight,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/test-tube.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ThemeToggle__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ThemeToggle */ \"(app-pages-browser)/./src/components/ThemeToggle.tsx\");\n/* harmony import */ var _components_LanguageToggle__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/LanguageToggle */ \"(app-pages-browser)/./src/components/LanguageToggle.tsx\");\n/* harmony import */ var _components_TestAIGeneration__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/TestAIGeneration */ \"(app-pages-browser)/./src/components/TestAIGeneration.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction SettingsPage() {\n    _s();\n    const { currentLanguage, apiSettings, showAdvancedOptions, setShowAdvancedOptions, addProvider, updateProvider, removeProvider, validateProvider, getProvider } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_3__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    const [showKeys, setShowKeys] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [validationStates, setValidationStates] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [showAddProvider, setShowAddProvider] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [selectedProviderId, setSelectedProviderId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [errorMessage, setErrorMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [expandedProviders, setExpandedProviders] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [selectedModels, setSelectedModels] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [newCustomModel, setNewCustomModel] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    // تهيئة النماذج المحددة عند تحميل الصفحة\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const configuredProviders = apiSettings.providers || [];\n        const initialModels = {};\n        configuredProviders.forEach((provider)=>{\n            initialModels[provider.id] = provider.selectedModels || [];\n        });\n        setSelectedModels(initialModels);\n    }, [\n        apiSettings.providers\n    ]);\n    const translations = {\n        title: isArabic ? \"إعدادات نماذج الذكاء الاصطناعي\" : \"LLM API Settings\",\n        subtitle: isArabic ? \"قم بتكوين مفاتيح واجهة برمجة التطبيقات ونماذج الذكاء الاصطناعي\" : \"Configure your API keys and AI models\",\n        providers: isArabic ? \"مقدمو خدمات الذكاء الاصطناعي\" : \"LLM Providers\",\n        addProvider: isArabic ? \"إضافة مقدم خدمة جديد\" : \"Add Provider\",\n        apiKey: isArabic ? \"مفتاح واجهة برمجة التطبيقات\" : \"API Key\",\n        baseUrl: isArabic ? \"الرابط الأساسي للخدمة\" : \"Base URL\",\n        testConnection: isArabic ? \"اختبار الاتصال\" : \"Test Connection\",\n        validating: isArabic ? \"جاري التحقق من الاتصال...\" : \"Validating...\",\n        valid: isArabic ? \"الاتصال صحيح\" : \"Valid\",\n        invalid: isArabic ? \"الاتصال غير صحيح\" : \"Invalid\",\n        error: isArabic ? \"حدث خطأ في الاتصال\" : \"Error\",\n        models: isArabic ? \"النماذج المتوفرة\" : \"Available Models\",\n        selectedModels: isArabic ? \"النماذج المختارة\" : \"Selected Models\",\n        addCustomModel: isArabic ? \"إضافة نموذج مخصص\" : \"Add Custom Model\",\n        customModelName: isArabic ? \"اسم النموذج المخصص\" : \"Custom Model Name\",\n        editModels: isArabic ? \"تحرير النماذج\" : \"Edit Models\",\n        saveModels: isArabic ? \"حفظ التغييرات\" : \"Save Models\",\n        noModelsSelected: isArabic ? \"لم يتم اختيار أي نماذج بعد\" : \"No models selected\",\n        cancel: isArabic ? \"إلغاء العملية\" : \"Cancel\",\n        add: isArabic ? \"إضافة\" : \"Add\",\n        backToHome: isArabic ? \"العودة إلى الصفحة الرئيسية\" : \"Back to Home\",\n        active: isArabic ? \"مفعل\" : \"Active\",\n        selectProvider: isArabic ? \"اختر مقدم الخدمة\" : \"Select Provider\",\n        noProvidersConfigured: isArabic ? \"لم يتم تكوين أي مقدم خدمة حتى الآن\" : \"No providers configured yet\",\n        providerAlreadyExists: isArabic ? \"مقدم الخدمة موجود مسبقاً\" : \"Provider already exists\",\n        pleaseSelectProvider: isArabic ? \"يرجى اختيار مقدم خدمة من القائمة\" : \"Please select a provider\",\n        providerNotFound: isArabic ? \"لم يتم العثور على مقدم الخدمة\" : \"Provider not found\",\n        errorAddingProvider: isArabic ? \"حدث خطأ أثناء إضافة مقدم الخدمة\" : \"Error adding provider\",\n        // إعدادات عامة\n        generalSettings: isArabic ? \"الإعدادات العامة\" : \"General Settings\",\n        advancedOptions: isArabic ? \"الخيارات المتقدمة\" : \"Advanced Options\",\n        showAdvancedOptions: isArabic ? \"إظهار الخيارات المتقدمة\" : \"Show Advanced Options\",\n        advancedOptionsDescription: isArabic ? \"عرض خيارات التخصيص المتقدمة في صفحات المشروع\" : \"Display advanced customization options in project pages\"\n    };\n    // دوال إدارة النماذج\n    const toggleModelSelection = (providerId, modelId)=>{\n        setSelectedModels((prev)=>{\n            const currentModels = prev[providerId] || [];\n            const isSelected = currentModels.includes(modelId);\n            return {\n                ...prev,\n                [providerId]: isSelected ? currentModels.filter((id)=>id !== modelId) : [\n                    ...currentModels,\n                    modelId\n                ]\n            };\n        });\n    };\n    const addCustomModel = (providerId)=>{\n        if (newCustomModel.trim()) {\n            setSelectedModels((prev)=>{\n                const currentModels = prev[providerId] || [];\n                return {\n                    ...prev,\n                    [providerId]: [\n                        ...currentModels,\n                        newCustomModel.trim()\n                    ]\n                };\n            });\n            setNewCustomModel(\"\");\n        }\n    };\n    const removeCustomModel = (providerId, modelId)=>{\n        setSelectedModels((prev)=>{\n            const currentModels = prev[providerId] || [];\n            return {\n                ...prev,\n                [providerId]: currentModels.filter((id)=>id !== modelId)\n            };\n        });\n    };\n    const saveProviderModels = (providerId)=>{\n        const models = selectedModels[providerId] || [];\n        updateProvider(providerId, {\n            selectedModels: models\n        });\n        setExpandedProviders((prev)=>({\n                ...prev,\n                [providerId]: false\n            }));\n    };\n    const handleAddProvider = async ()=>{\n        setErrorMessage(\"\");\n        if (!selectedProviderId) {\n            setErrorMessage(translations.pleaseSelectProvider);\n            return;\n        }\n        const providerTemplate = (0,_lib_llmProviders__WEBPACK_IMPORTED_MODULE_4__.getProviderById)(selectedProviderId);\n        if (!providerTemplate) {\n            setErrorMessage(translations.providerNotFound);\n            return;\n        }\n        const existingProvider = getProvider(selectedProviderId);\n        if (existingProvider) {\n            setErrorMessage(translations.providerAlreadyExists);\n            setShowAddProvider(false);\n            setSelectedProviderId(\"\");\n            return;\n        }\n        try {\n            const newProvider = {\n                id: selectedProviderId,\n                apiKey: \"\",\n                selectedModels: [],\n                isEnabled: false,\n                validationStatus: \"pending\",\n                priority: 1,\n                isBackup: false\n            };\n            addProvider(newProvider);\n            setShowAddProvider(false);\n            setSelectedProviderId(\"\");\n            setErrorMessage(\"\");\n        } catch (error) {\n            console.error(\"Error adding provider:\", error);\n            setErrorMessage(translations.errorAddingProvider);\n        }\n    };\n    const handleValidateProvider = async (providerId)=>{\n        setValidationStates((prev)=>({\n                ...prev,\n                [providerId]: {\n                    status: \"validating\"\n                }\n            }));\n        try {\n            const isValid = await validateProvider(providerId);\n            setValidationStates((prev)=>({\n                    ...prev,\n                    [providerId]: {\n                        status: isValid ? \"valid\" : \"invalid\",\n                        message: isValid ? translations.valid : translations.invalid,\n                        lastValidated: new Date()\n                    }\n                }));\n        } catch (error) {\n            setValidationStates((prev)=>({\n                    ...prev,\n                    [providerId]: {\n                        status: \"error\",\n                        message: error instanceof Error ? error.message : translations.error\n                    }\n                }));\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"validating\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"w-4 h-4 animate-spin text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 221,\n                    columnNumber: 16\n                }, this);\n            case \"valid\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"w-4 h-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 16\n                }, this);\n            case \"invalid\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"w-4 h-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 16\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"w-4 h-4 text-orange-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"w-4 h-4 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const configuredProviders = apiSettings.providers || [];\n    const availableProviders = _lib_llmProviders__WEBPACK_IMPORTED_MODULE_4__.LLM_PROVIDERS_DATABASE.filter((p)=>!configuredProviders.some((cp)=>cp.id === p.id));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        dir: isArabic ? \"rtl\" : \"ltr\",\n        className: \"jsx-2abf06658c86e3df\" + \" \" + \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"2abf06658c86e3df\",\n                children: '[dir=\"rtl\"].jsx-2abf06658c86e3df{font-family:\"Tajawal\",\"Arial\",sans-serif}[dir=\"rtl\"].jsx-2abf06658c86e3df *.jsx-2abf06658c86e3df{font-family:\"Tajawal\",\"Arial\",sans-serif;text-align:right}[dir=\"rtl\"].jsx-2abf06658c86e3df .page-container.jsx-2abf06658c86e3df{direction:rtl}[dir=\"rtl\"].jsx-2abf06658c86e3df .header-section.jsx-2abf06658c86e3df{direction:rtl}[dir=\"rtl\"].jsx-2abf06658c86e3df .header-content.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between}[dir=\"rtl\"].jsx-2abf06658c86e3df .header-left.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse}[dir=\"rtl\"].jsx-2abf06658c86e3df .header-right.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse}[dir=\"rtl\"].jsx-2abf06658c86e3df .section-header.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between}[dir=\"rtl\"].jsx-2abf06658c86e3df .provider-card.jsx-2abf06658c86e3df{direction:rtl}[dir=\"rtl\"].jsx-2abf06658c86e3df .provider-header.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between}[dir=\"rtl\"].jsx-2abf06658c86e3df .provider-info.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse;text-align:right}[dir=\"rtl\"].jsx-2abf06658c86e3df .provider-controls.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse}[dir=\"rtl\"].jsx-2abf06658c86e3df .form-section.jsx-2abf06658c86e3df{direction:rtl}[dir=\"rtl\"].jsx-2abf06658c86e3df .form-row.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse}[dir=\"rtl\"].jsx-2abf06658c86e3df .models-section.jsx-2abf06658c86e3df{direction:rtl}[dir=\"rtl\"].jsx-2abf06658c86e3df .models-header.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between}[dir=\"rtl\"].jsx-2abf06658c86e3df .model-item.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse}[dir=\"rtl\"].jsx-2abf06658c86e3df .model-tags.jsx-2abf06658c86e3df{-webkit-box-pack:end;-webkit-justify-content:flex-end;-moz-box-pack:end;-ms-flex-pack:end;justify-content:flex-end}[dir=\"rtl\"].jsx-2abf06658c86e3df input[type=\"text\"].jsx-2abf06658c86e3df,[dir=\"rtl\"].jsx-2abf06658c86e3df input[type=\"password\"].jsx-2abf06658c86e3df,[dir=\"rtl\"].jsx-2abf06658c86e3df select.jsx-2abf06658c86e3df,[dir=\"rtl\"].jsx-2abf06658c86e3df textarea.jsx-2abf06658c86e3df{text-align:right;direction:rtl;font-family:\"Tajawal\",\"Arial\",sans-serif}[dir=\"rtl\"].jsx-2abf06658c86e3df .button-group.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse}[dir=\"rtl\"].jsx-2abf06658c86e3df .button-with-icon.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse}[dir=\"rtl\"].jsx-2abf06658c86e3df .modal-content.jsx-2abf06658c86e3df{direction:rtl;text-align:right}[dir=\"rtl\"].jsx-2abf06658c86e3df .modal-buttons.jsx-2abf06658c86e3df{-webkit-box-orient:horizontal;-webkit-box-direction:reverse;-webkit-flex-direction:row-reverse;-moz-box-orient:horizontal;-moz-box-direction:reverse;-ms-flex-direction:row-reverse;flex-direction:row-reverse}[dir=\"rtl\"].jsx-2abf06658c86e3df .text-content.jsx-2abf06658c86e3df{text-align:right;direction:rtl}[dir=\"rtl\"].jsx-2abf06658c86e3df .font-arabic.jsx-2abf06658c86e3df{font-family:\"Tajawal\",\"Arial\",sans-serif;text-align:right}'\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-2abf06658c86e3df\" + \" \" + \"header-section bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"header-content flex items-center justify-between h-16 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"header-left flex items-center gap-4 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        href: \"/\",\n                                        className: \"flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                        children: [\n                                            isArabic ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-5 h-5 rotate-180\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"font-arabic text-content\",\n                                                children: translations.backToHome\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex items-center gap-3 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"w-6 h-6 text-blue-600 dark:text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-content\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-xl font-bold text-gray-900 dark:text-white font-arabic\",\n                                                        children: translations.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm text-gray-600 dark:text-gray-400 font-arabic\",\n                                                        children: translations.subtitle\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"header-right flex items-center gap-3 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LanguageToggle__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeToggle__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 368,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 367,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 366,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-2abf06658c86e3df\" + \" \" + \"page-container max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"p-6 border-b border-gray-200 dark:border-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-lg font-semibold text-gray-900 dark:text-white font-arabic text-content\",\n                                        children: translations.generalSettings\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 413,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"space-y-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex-1 \".concat(isArabic ? \"text-right mr-4\" : \"text-left ml-4\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm font-medium text-gray-900 dark:text-white font-arabic\",\n                                                            children: translations.showAdvancedOptions\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 423,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm text-gray-500 dark:text-gray-400 mt-1 font-arabic\",\n                                                            children: translations.advancedOptionsDescription\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 426,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowAdvancedOptions(!showAdvancedOptions),\n                                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 \".concat(showAdvancedOptions ? \"bg-blue-600\" : \"bg-gray-200 dark:bg-gray-700\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"inline-block h-4 w-4 transform rounded-full bg-white transition-transform \".concat(showAdvancedOptions ? \"translate-x-6\" : \"translate-x-1\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 437,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 431,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 418,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 412,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"p-6 border-b border-gray-200 dark:border-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"section-header flex items-center justify-between \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-lg font-semibold text-gray-900 dark:text-white font-arabic text-content\",\n                                                children: translations.providers\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 453,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    setShowAddProvider(true);\n                                                    setErrorMessage(\"\");\n                                                    setSelectedProviderId(\"\");\n                                                },\n                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"button-with-icon flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-arabic \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 464,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-content\",\n                                                        children: translations.addProvider\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 465,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 452,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"p-6 space-y-4\",\n                                    children: configuredProviders.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-gray-500 dark:text-gray-400 font-arabic text-content\",\n                                                children: translations.noProvidersConfigured\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 17\n                                    }, this) : configuredProviders.map((provider)=>{\n                                        const providerInfo = (0,_lib_llmProviders__WEBPACK_IMPORTED_MODULE_4__.getProviderById)(provider.id);\n                                        const validationState = validationStates[provider.id];\n                                        if (!providerInfo) return null;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"provider-card border border-gray-200 dark:border-gray-600 rounded-lg p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"provider-header flex items-start justify-between mb-6 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"provider-info flex items-center gap-4 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-2xl\",\n                                                                    children: providerInfo.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 489,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"space-y-1 text-content\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"font-semibold text-gray-900 dark:text-white font-arabic\",\n                                                                            children: providerInfo.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 491,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm text-gray-600 dark:text-gray-400 font-arabic\",\n                                                                            children: providerInfo.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 494,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 490,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 488,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"provider-controls flex items-center gap-3 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                            children: [\n                                                                getStatusIcon((validationState === null || validationState === void 0 ? void 0 : validationState.status) || \"idle\"),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex items-center gap-2 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: provider.isEnabled,\n                                                                            onChange: (e)=>updateProvider(provider.id, {\n                                                                                    isEnabled: e.target.checked\n                                                                                }),\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 505,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm text-gray-600 dark:text-gray-400 font-arabic whitespace-nowrap text-content\",\n                                                                            children: translations.active\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 511,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 504,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>removeProvider(provider.id),\n                                                                    title: isArabic ? \"حذف مقدم الخدمة\" : \"Remove Provider\",\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"p-2 text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-colors\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 521,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 516,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 500,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 487,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"form-section space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"grid grid-cols-1 lg:grid-cols-2 gap-4 items-end\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex flex-col\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 font-arabic text-content\",\n                                                                            children: translations.apiKey\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 529,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"relative\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: showKeys[provider.id] ? \"text\" : \"password\",\n                                                                                    value: provider.apiKey,\n                                                                                    onChange: (e)=>updateProvider(provider.id, {\n                                                                                            apiKey: e.target.value\n                                                                                        }),\n                                                                                    placeholder: providerInfo.apiKeyPlaceholder,\n                                                                                    dir: isArabic ? \"rtl\" : \"ltr\",\n                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"w-full h-10 px-3 py-2 \".concat(isArabic ? \"pr-10 pl-3\" : \"pr-10 pl-3\", \" border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent font-arabic\")\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 533,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>setShowKeys((prev)=>({\n                                                                                                ...prev,\n                                                                                                [provider.id]: !prev[provider.id]\n                                                                                            })),\n                                                                                    title: isArabic ? showKeys[provider.id] ? \"إخفاء المفتاح\" : \"إظهار المفتاح\" : showKeys[provider.id] ? \"Hide key\" : \"Show key\",\n                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"absolute \".concat(isArabic ? \"left-3\" : \"right-3\", \" top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\"),\n                                                                                    children: showKeys[provider.id] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 546,\n                                                                                        columnNumber: 58\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 546,\n                                                                                        columnNumber: 91\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 541,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 532,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 528,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex flex-col\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 font-arabic text-content\",\n                                                                            children: translations.baseUrl\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 552,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: provider.baseUrl || providerInfo.baseUrl,\n                                                                            onChange: (e)=>updateProvider(provider.id, {\n                                                                                    baseUrl: e.target.value\n                                                                                }),\n                                                                            placeholder: providerInfo.baseUrl,\n                                                                            dir: isArabic ? \"rtl\" : \"ltr\",\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"w-full h-10 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent font-arabic\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 555,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 551,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 527,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"grid grid-cols-1 lg:grid-cols-2 gap-4 mt-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex \".concat(isArabic ? \"justify-start\" : \"justify-start\"),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleValidateProvider(provider.id),\n                                                                        disabled: !provider.apiKey || (validationState === null || validationState === void 0 ? void 0 : validationState.status) === \"validating\",\n                                                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"button-with-icon flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-arabic \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                className: \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 574,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-content\",\n                                                                                children: (validationState === null || validationState === void 0 ? void 0 : validationState.status) === \"validating\" ? translations.validating : translations.testConnection\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 575,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 569,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 568,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex \".concat(isArabic ? \"justify-end\" : \"justify-end\"),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm text-gray-600 dark:text-gray-400 font-arabic text-content\",\n                                                                        children: [\n                                                                            translations.models,\n                                                                            \": \",\n                                                                            providerInfo.models.length\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 583,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 582,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 566,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        (validationState === null || validationState === void 0 ? void 0 : validationState.message) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"mt-2 p-2 rounded text-sm text-content \".concat(validationState.status === \"valid\" ? \"bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300\" : \"bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300\"),\n                                                            children: validationState.message\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 590,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"models-section mt-4 border-t border-gray-200 dark:border-gray-600 pt-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"grid grid-cols-1 lg:grid-cols-2 gap-4 mb-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex \".concat(isArabic ? \"justify-start\" : \"justify-start\"),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm font-medium text-gray-900 dark:text-white font-arabic text-content\",\n                                                                                children: translations.selectedModels\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 604,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 603,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex \".concat(isArabic ? \"justify-end\" : \"justify-end\"),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>{\n                                                                                    setExpandedProviders((prev)=>({\n                                                                                            ...prev,\n                                                                                            [provider.id]: !prev[provider.id]\n                                                                                        }));\n                                                                                    if (!selectedModels[provider.id]) {\n                                                                                        setSelectedModels((prev)=>({\n                                                                                                ...prev,\n                                                                                                [provider.id]: provider.selectedModels || []\n                                                                                            }));\n                                                                                    }\n                                                                                },\n                                                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm text-blue-600 dark:text-blue-400 hover:underline font-arabic text-content\",\n                                                                                children: expandedProviders[provider.id] ? translations.saveModels : translations.editModels\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 611,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 610,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 601,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"mb-3\",\n                                                                    children: (provider.selectedModels || []).length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm text-gray-500 dark:text-gray-400 font-arabic text-content\",\n                                                                        children: translations.noModelsSelected\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 628,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"model-tags flex flex-wrap gap-2 \".concat(isArabic ? \"justify-end\" : \"justify-start\"),\n                                                                        children: (provider.selectedModels || []).map((modelId)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded text-xs font-arabic text-content\",\n                                                                                children: modelId\n                                                                            }, modelId, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 634,\n                                                                                columnNumber: 35\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 632,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 626,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                expandedProviders[provider.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"space-y-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-2abf06658c86e3df\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm font-medium text-gray-900 dark:text-white mb-2 font-arabic text-content\",\n                                                                                    children: translations.models\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 650,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"grid grid-cols-1 md:grid-cols-2 gap-2 max-h-40 overflow-y-auto\",\n                                                                                    children: providerInfo.models.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"model-item flex items-center gap-2 p-2 hover:bg-gray-100 dark:hover:bg-gray-600 rounded cursor-pointer \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                    type: \"checkbox\",\n                                                                                                    checked: (selectedModels[provider.id] || []).includes(model.id),\n                                                                                                    onChange: ()=>toggleModelSelection(provider.id, model.id),\n                                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"rounded border-gray-300 text-blue-600 focus:ring-blue-500 flex-shrink-0\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                    lineNumber: 659,\n                                                                                                    columnNumber: 39\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex-1 min-w-0 text-content\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm font-medium text-gray-900 dark:text-white truncate font-arabic\",\n                                                                                                            children: model.name\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                            lineNumber: 666,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-xs text-gray-500 dark:text-gray-400 truncate font-arabic\",\n                                                                                                            children: model.description\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                            lineNumber: 669,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                    lineNumber: 665,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, model.id, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 655,\n                                                                                            columnNumber: 37\n                                                                                        }, this))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 653,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 649,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-2abf06658c86e3df\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm font-medium text-gray-900 dark:text-white mb-2 font-arabic text-content\",\n                                                                                    children: translations.addCustomModel\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 680,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex gap-2 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"text\",\n                                                                                            value: newCustomModel,\n                                                                                            onChange: (e)=>setNewCustomModel(e.target.value),\n                                                                                            placeholder: translations.customModelName,\n                                                                                            onKeyDown: (e)=>e.key === \"Enter\" && addCustomModel(provider.id),\n                                                                                            dir: isArabic ? \"rtl\" : \"ltr\",\n                                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm font-arabic\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 684,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                            onClick: ()=>addCustomModel(provider.id),\n                                                                                            disabled: !newCustomModel.trim(),\n                                                                                            title: isArabic ? \"إضافة النموذج\" : \"Add Model\",\n                                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm flex-shrink-0\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                                className: \"w-4 h-4\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                lineNumber: 699,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 693,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 683,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 679,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        (selectedModels[provider.id] || []).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-2abf06658c86e3df\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm font-medium text-gray-900 dark:text-white mb-2 font-arabic text-content\",\n                                                                                    children: translations.selectedModels\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 707,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"model-tags flex flex-wrap gap-2 \".concat(isArabic ? \"justify-end\" : \"justify-start\"),\n                                                                                    children: (selectedModels[provider.id] || []).map((modelId)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex items-center gap-1 px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded text-xs \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"font-arabic text-content\",\n                                                                                                    children: modelId\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                    lineNumber: 716,\n                                                                                                    columnNumber: 41\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>removeCustomModel(provider.id, modelId),\n                                                                                                    title: isArabic ? \"حذف النموذج\" : \"Remove Model\",\n                                                                                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-blue-600 dark:text-blue-400 hover:text-red-600 dark:hover:text-red-400 flex-shrink-0\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                                        className: \"w-3 h-3\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                        lineNumber: 722,\n                                                                                                        columnNumber: 43\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                    lineNumber: 717,\n                                                                                                    columnNumber: 41\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, modelId, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 712,\n                                                                                            columnNumber: 39\n                                                                                        }, this))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 710,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 706,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"button-group flex \".concat(isArabic ? \"justify-start\" : \"justify-end\"),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>saveProviderModels(provider.id),\n                                                                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm font-arabic text-content\",\n                                                                                children: translations.saveModels\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 732,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 731,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 647,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 600,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 525,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, provider.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 486,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 470,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 450,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TestAIGeneration__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 751,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 409,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 408,\n                columnNumber: 7\n            }, this),\n            showAddProvider && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-2abf06658c86e3df\" + \" \" + \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    dir: isArabic ? \"rtl\" : \"ltr\",\n                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"modal-content bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-lg font-semibold text-gray-900 dark:text-white mb-4 font-arabic text-content\",\n                            children: translations.addProvider\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 759,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"space-y-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: selectedProviderId,\n                                onChange: (e)=>setSelectedProviderId(e.target.value),\n                                dir: isArabic ? \"rtl\" : \"ltr\",\n                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white font-arabic\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        className: \"jsx-2abf06658c86e3df\",\n                                        children: translations.selectProvider\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 770,\n                                        columnNumber: 17\n                                    }, this),\n                                    availableProviders.map((provider)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: provider.id,\n                                            className: \"jsx-2abf06658c86e3df\",\n                                            children: [\n                                                provider.icon,\n                                                \" \",\n                                                provider.name\n                                            ]\n                                        }, provider.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 772,\n                                            columnNumber: 19\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 764,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 763,\n                            columnNumber: 13\n                        }, this),\n                        errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex items-center gap-2 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowRight_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-4 h-4 text-red-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 783,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-2abf06658c86e3df\" + \" \" + \"text-sm text-red-700 dark:text-red-300 font-arabic text-content\",\n                                        children: errorMessage\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 784,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 782,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 781,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-2abf06658c86e3df\" + \" \" + \"modal-buttons flex gap-3 mt-6 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        setShowAddProvider(false);\n                                        setErrorMessage(\"\");\n                                        setSelectedProviderId(\"\");\n                                    },\n                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors font-arabic text-content\",\n                                    children: translations.cancel\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 792,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleAddProvider,\n                                    disabled: !selectedProviderId,\n                                    className: \"jsx-2abf06658c86e3df\" + \" \" + \"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-arabic text-content\",\n                                    children: translations.add\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 802,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 791,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 758,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 757,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n        lineNumber: 239,\n        columnNumber: 5\n    }, this);\n}\n_s(SettingsPage, \"pKs+72xp9+Lvo6vc4bQUX4ox3H8=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_3__.useContextStore\n    ];\n});\n_c = SettingsPage;\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/settings/page.tsx\n"));

/***/ })

});