'use client';

import { useContextStore } from '@/store/contextStore';
import { 
  PROJECT_TYPES, 
  TARGET_PLATFORMS, 
  PROGRAMMING_LANGUAGES, 
  COMPLEXITY_LEVELS,
  BUDGET_RANGES,
  TEAM_SIZES,
  DEPLOYMENT_TYPES
} from '@/lib/projectOptions';
import { 
  ARCHITECTURE_PATTERNS,
  SCALING_STRATEGIES,
  SECURITY_REQUIREMENTS,
  PERFORMANCE_TARGETS
} from '@/lib/technicalOptions';

interface Recommendation {
  id: string;
  title: string;
  titleAr: string;
  description: string;
  descriptionAr: string;
  icon: string;
  priority: 'high' | 'medium' | 'low';
  category: string;
  categoryAr: string;
}

export default function SmartRecommendations() {
  const { projectDefinition, technicalLayer, currentLanguage } = useContextStore();
  const isArabic = currentLanguage === 'ar';

  const generateRecommendations = (): Recommendation[] => {
    const recommendations: Recommendation[] = [];

    // توصيات بناءً على نوع المشروع
    if (projectDefinition.projectType === 'mobile-app') {
      if (!projectDefinition.targetPlatforms?.includes('android') && !projectDefinition.targetPlatforms?.includes('ios')) {
        recommendations.push({
          id: 'mobile-platforms',
          title: 'Select Mobile Platforms',
          titleAr: 'اختر منصات الجوال',
          description: 'Consider targeting Android and/or iOS for maximum reach',
          descriptionAr: 'فكر في استهداف أندرويد و/أو iOS للوصول الأقصى',
          icon: '📱',
          priority: 'high',
          category: 'Platform',
          categoryAr: 'المنصة'
        });
      }

      if (!projectDefinition.primaryLanguages?.some(lang => ['kotlin', 'swift', 'dart'].includes(lang))) {
        recommendations.push({
          id: 'mobile-languages',
          title: 'Mobile Development Languages',
          titleAr: 'لغات تطوير الجوال',
          description: 'Consider Kotlin for Android, Swift for iOS, or Dart/Flutter for cross-platform',
          descriptionAr: 'فكر في Kotlin لأندرويد، Swift لـ iOS، أو Dart/Flutter للمنصات المتعددة',
          icon: '💻',
          priority: 'high',
          category: 'Technology',
          categoryAr: 'التقنية'
        });
      }
    }

    // توصيات بناءً على نوع المشروع - تطبيق ويب
    if (projectDefinition.projectType === 'web-app') {
      if (!projectDefinition.primaryLanguages?.includes('javascript')) {
        recommendations.push({
          id: 'web-languages',
          title: 'Web Development Stack',
          titleAr: 'مجموعة تطوير الويب',
          description: 'JavaScript/TypeScript is essential for modern web development',
          descriptionAr: 'JavaScript/TypeScript ضروري لتطوير الويب الحديث',
          icon: '🌐',
          priority: 'high',
          category: 'Technology',
          categoryAr: 'التقنية'
        });
      }

      if (!technicalLayer.architecturePattern) {
        recommendations.push({
          id: 'web-architecture',
          title: 'Choose Architecture Pattern',
          titleAr: 'اختر نمط الهندسة المعمارية',
          description: 'Consider microservices for scalability or monolithic for simplicity',
          descriptionAr: 'فكر في المايكروسيرفس للتوسع أو الأحادي للبساطة',
          icon: '🏗️',
          priority: 'medium',
          category: 'Architecture',
          categoryAr: 'الهندسة المعمارية'
        });
      }
    }

    // توصيات بناءً على مستوى التعقيد
    if (projectDefinition.complexity === 'enterprise') {
      if (!technicalLayer.securityRequirements) {
        recommendations.push({
          id: 'enterprise-security',
          title: 'Enterprise Security Requirements',
          titleAr: 'متطلبات الأمان المؤسسي',
          description: 'Enterprise projects need comprehensive security measures',
          descriptionAr: 'المشاريع المؤسسية تحتاج إجراءات أمان شاملة',
          icon: '🔒',
          priority: 'high',
          category: 'Security',
          categoryAr: 'الأمان'
        });
      }

      if (!technicalLayer.scalingStrategy) {
        recommendations.push({
          id: 'enterprise-scaling',
          title: 'Scaling Strategy Required',
          titleAr: 'استراتيجية التوسع مطلوبة',
          description: 'Plan for horizontal scaling and load balancing',
          descriptionAr: 'خطط للتوسع الأفقي وتوزيع الأحمال',
          icon: '📈',
          priority: 'high',
          category: 'Performance',
          categoryAr: 'الأداء'
        });
      }
    }

    // توصيات بناءً على حجم الفريق
    if (projectDefinition.teamSize === 'large' || projectDefinition.teamSize === 'enterprise') {
      if (!technicalLayer.monitoringTools) {
        recommendations.push({
          id: 'team-monitoring',
          title: 'Team Monitoring Tools',
          titleAr: 'أدوات مراقبة الفريق',
          description: 'Large teams need comprehensive monitoring and logging',
          descriptionAr: 'الفرق الكبيرة تحتاج مراقبة وتسجيل شامل',
          icon: '📊',
          priority: 'medium',
          category: 'Operations',
          categoryAr: 'العمليات'
        });
      }
    }

    // توصيات بناءً على نوع النشر
    if (projectDefinition.deploymentType === 'cloud') {
      if (!technicalLayer.scalingStrategy) {
        recommendations.push({
          id: 'cloud-scaling',
          title: 'Cloud Auto-scaling',
          titleAr: 'التوسع التلقائي السحابي',
          description: 'Leverage cloud auto-scaling capabilities',
          descriptionAr: 'استفد من قدرات التوسع التلقائي السحابي',
          icon: '☁️',
          priority: 'medium',
          category: 'Infrastructure',
          categoryAr: 'البنية التحتية'
        });
      }
    }

    // توصيات بناءً على الميزانية
    if (projectDefinition.budget === 'startup') {
      recommendations.push({
        id: 'startup-budget',
        title: 'Cost-Effective Solutions',
        titleAr: 'حلول فعالة من ناحية التكلفة',
        description: 'Consider open-source tools and serverless architecture',
        descriptionAr: 'فكر في الأدوات مفتوحة المصدر والهندسة بلا خادم',
        icon: '💰',
        priority: 'high',
        category: 'Budget',
        categoryAr: 'الميزانية'
      });
    }

    // توصيات عامة للذكاء الاصطناعي
    if (projectDefinition.projectType === 'ai-model' || projectDefinition.name?.toLowerCase().includes('ai')) {
      if (!projectDefinition.primaryLanguages?.includes('python')) {
        recommendations.push({
          id: 'ai-python',
          title: 'Python for AI Development',
          titleAr: 'Python لتطوير الذكاء الاصطناعي',
          description: 'Python is the most popular language for AI/ML projects',
          descriptionAr: 'Python هي اللغة الأكثر شعبية لمشاريع الذكاء الاصطناعي',
          icon: '🐍',
          priority: 'high',
          category: 'Technology',
          categoryAr: 'التقنية'
        });
      }
    }

    return recommendations.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  };

  const recommendations = generateRecommendations();

  if (recommendations.length === 0) {
    return null;
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-900/20';
      case 'medium': return 'border-yellow-200 dark:border-yellow-800 bg-yellow-50 dark:bg-yellow-900/20';
      case 'low': return 'border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/20';
      default: return 'border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800';
    }
  };

  const getPriorityLabel = (priority: string) => {
    const labels = {
      high: { ar: 'عالية', en: 'High' },
      medium: { ar: 'متوسطة', en: 'Medium' },
      low: { ar: 'منخفضة', en: 'Low' }
    };
    return labels[priority as keyof typeof labels]?.[isArabic ? 'ar' : 'en'] || priority;
  };

  return (
    <div className="bg-gradient-to-br from-purple-50 to-pink-100 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl p-6 border border-purple-200 dark:border-purple-800">
      <div className={`text-center mb-6 ${isArabic ? 'text-right' : 'text-left'}`}>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
          {isArabic ? '🎯 توصيات ذكية لمشروعك' : '🎯 Smart Recommendations for Your Project'}
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          {isArabic 
            ? 'اقتراحات مخصصة بناءً على خيارات مشروعك'
            : 'Personalized suggestions based on your project options'
          }
        </p>
      </div>

      <div className="space-y-4">
        {recommendations.map((rec) => (
          <div
            key={rec.id}
            className={`p-4 rounded-lg border ${getPriorityColor(rec.priority)} ${isArabic ? 'text-right' : 'text-left'}`}
          >
            <div className="flex items-start justify-between mb-2">
              <div className={`flex items-center gap-2 ${isArabic ? 'flex-row-reverse' : ''}`}>
                <span className="text-lg">{rec.icon}</span>
                <h4 className="font-medium text-gray-900 dark:text-gray-100">
                  {isArabic ? rec.titleAr : rec.title}
                </h4>
              </div>
              <div className="flex items-center gap-2">
                <span className={`text-xs px-2 py-1 rounded-full ${
                  rec.priority === 'high' ? 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300' :
                  rec.priority === 'medium' ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300' :
                  'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300'
                }`}>
                  {getPriorityLabel(rec.priority)}
                </span>
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {isArabic ? rec.categoryAr : rec.category}
                </span>
              </div>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {isArabic ? rec.descriptionAr : rec.description}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
}
