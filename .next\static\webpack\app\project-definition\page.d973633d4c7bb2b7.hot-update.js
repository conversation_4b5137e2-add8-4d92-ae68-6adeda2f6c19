"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/project-definition/page",{

/***/ "(app-pages-browser)/./src/components/AdvancedOptionsPanel.tsx":
/*!*************************************************!*\
  !*** ./src/components/AdvancedOptionsPanel.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AdvancedOptionsPanel; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* harmony import */ var _components_AdvancedOptionsSelector__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AdvancedOptionsSelector */ \"(app-pages-browser)/./src/components/AdvancedOptionsSelector.tsx\");\n/* harmony import */ var _lib_projectOptions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/projectOptions */ \"(app-pages-browser)/./src/lib/projectOptions.ts\");\n/* harmony import */ var _lib_technicalOptions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/technicalOptions */ \"(app-pages-browser)/./src/lib/technicalOptions.ts\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronUp_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronUp,Sparkles!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction AdvancedOptionsPanel(param) {\n    let { moduleType } = param;\n    _s();\n    const { projectDefinition, technicalLayer, updateProjectDefinition, updateTechnicalLayer, showAdvancedOptions, currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore)();\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const isArabic = currentLanguage === \"ar\";\n    if (!showAdvancedOptions) {\n        return null;\n    }\n    const handleProjectFieldChange = (field, value)=>{\n        updateProjectDefinition({\n            [field]: value\n        });\n    };\n    const handleProjectArrayFieldChange = (field, values)=>{\n        updateProjectDefinition({\n            [field]: values\n        });\n    };\n    const handleTechnicalFieldChange = (field, value)=>{\n        updateTechnicalLayer({\n            [field]: value\n        });\n    };\n    const getTitle = ()=>{\n        if (moduleType === \"project-definition\") {\n            return isArabic ? \"خيارات التخصيص المتقدمة\" : \"Advanced Customization Options\";\n        } else {\n            return isArabic ? \"خيارات تقنية متقدمة\" : \"Advanced Technical Options\";\n        }\n    };\n    const getIcon = ()=>{\n        return moduleType === \"project-definition\" ? \"\\uD83C\\uDFAF\" : \"⚙️\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gradient-to-br from-indigo-50 to-purple-100 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-xl border border-indigo-200 dark:border-indigo-800 mb-8 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 cursor-pointer hover:bg-indigo-100/50 dark:hover:bg-indigo-800/30 transition-colors duration-200\",\n                onClick: ()=>setIsExpanded(!isExpanded),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-2 bg-indigo-100 dark:bg-indigo-800 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl\",\n                                        children: getIcon()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: isArabic ? \"text-right\" : \"text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 dark:text-gray-100\",\n                                            children: getTitle()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                            children: isArabic ? \"خصص مشروعك بخيارات متقدمة للحصول على نتائج أكثر دقة\" : \"Customize your project with advanced options for more precise results\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1 px-3 py-1 bg-indigo-100 dark:bg-indigo-800 rounded-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-3 h-3 text-indigo-600 dark:text-indigo-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs font-medium text-indigo-700 dark:text-indigo-300\",\n                                            children: isArabic ? \"ذكي\" : \"Smart\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this),\n                                isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-5 h-5 text-gray-500 dark:text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronUp_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-5 h-5 text-gray-500 dark:text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this),\n            isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 pb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700\",\n                    children: [\n                        moduleType === \"project-definition\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedOptionsSelector__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    title: \"Project Type\",\n                                    titleAr: \"نوع المشروع\",\n                                    options: _lib_projectOptions__WEBPACK_IMPORTED_MODULE_4__.PROJECT_TYPES,\n                                    selectedValues: projectDefinition.projectType ? [\n                                        projectDefinition.projectType\n                                    ] : [],\n                                    onSelectionChange: (values)=>handleProjectFieldChange(\"projectType\", values[0] || \"\"),\n                                    placeholder: \"Select project type\",\n                                    placeholderAr: \"اختر نوع المشروع\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedOptionsSelector__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    title: \"Complexity Level\",\n                                    titleAr: \"مستوى التعقيد\",\n                                    options: _lib_projectOptions__WEBPACK_IMPORTED_MODULE_4__.COMPLEXITY_LEVELS,\n                                    selectedValues: projectDefinition.complexity ? [\n                                        projectDefinition.complexity\n                                    ] : [],\n                                    onSelectionChange: (values)=>handleProjectFieldChange(\"complexity\", values[0] || \"\"),\n                                    placeholder: \"Select complexity level\",\n                                    placeholderAr: \"اختر مستوى التعقيد\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedOptionsSelector__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    title: \"Target Platforms\",\n                                    titleAr: \"المنصات المستهدفة\",\n                                    options: _lib_projectOptions__WEBPACK_IMPORTED_MODULE_4__.TARGET_PLATFORMS,\n                                    selectedValues: projectDefinition.targetPlatforms || [],\n                                    onSelectionChange: (values)=>handleProjectArrayFieldChange(\"targetPlatforms\", values),\n                                    multiSelect: true,\n                                    placeholder: \"Select target platforms\",\n                                    placeholderAr: \"اختر المنصات المستهدفة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedOptionsSelector__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    title: \"Primary Programming Languages\",\n                                    titleAr: \"لغات البرمجة الأساسية\",\n                                    options: _lib_projectOptions__WEBPACK_IMPORTED_MODULE_4__.PROGRAMMING_LANGUAGES,\n                                    selectedValues: projectDefinition.primaryLanguages || [],\n                                    onSelectionChange: (values)=>handleProjectArrayFieldChange(\"primaryLanguages\", values),\n                                    multiSelect: true,\n                                    placeholder: \"Select programming languages\",\n                                    placeholderAr: \"اختر لغات البرمجة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedOptionsSelector__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    title: \"Team Size\",\n                                    titleAr: \"حجم الفريق\",\n                                    options: _lib_projectOptions__WEBPACK_IMPORTED_MODULE_4__.TEAM_SIZES,\n                                    selectedValues: projectDefinition.teamSize ? [\n                                        projectDefinition.teamSize\n                                    ] : [],\n                                    onSelectionChange: (values)=>handleProjectFieldChange(\"teamSize\", values[0] || \"\"),\n                                    placeholder: \"Select team size\",\n                                    placeholderAr: \"اختر حجم الفريق\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedOptionsSelector__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    title: \"Budget Range\",\n                                    titleAr: \"نطاق الميزانية\",\n                                    options: _lib_projectOptions__WEBPACK_IMPORTED_MODULE_4__.BUDGET_RANGES,\n                                    selectedValues: projectDefinition.budget ? [\n                                        projectDefinition.budget\n                                    ] : [],\n                                    onSelectionChange: (values)=>handleProjectFieldChange(\"budget\", values[0] || \"\"),\n                                    placeholder: \"Select budget range\",\n                                    placeholderAr: \"اختر نطاق الميزانية\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:col-span-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedOptionsSelector__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        title: \"Deployment Type\",\n                                        titleAr: \"نوع النشر\",\n                                        options: _lib_projectOptions__WEBPACK_IMPORTED_MODULE_4__.DEPLOYMENT_TYPES,\n                                        selectedValues: projectDefinition.deploymentType ? [\n                                            projectDefinition.deploymentType\n                                        ] : [],\n                                        onSelectionChange: (values)=>handleProjectFieldChange(\"deploymentType\", values[0] || \"\"),\n                                        placeholder: \"Select deployment type\",\n                                        placeholderAr: \"اختر نوع النشر\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedOptionsSelector__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    title: \"Architecture Pattern\",\n                                    titleAr: \"نمط الهندسة المعمارية\",\n                                    options: _lib_technicalOptions__WEBPACK_IMPORTED_MODULE_5__.ARCHITECTURE_PATTERNS,\n                                    selectedValues: technicalLayer.architecturePattern ? [\n                                        technicalLayer.architecturePattern\n                                    ] : [],\n                                    onSelectionChange: (values)=>handleTechnicalFieldChange(\"architecturePattern\", values[0] || \"\"),\n                                    placeholder: \"Select architecture pattern\",\n                                    placeholderAr: \"اختر نمط الهندسة المعمارية\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedOptionsSelector__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    title: \"Scaling Strategy\",\n                                    titleAr: \"استراتيجية التوسع\",\n                                    options: _lib_technicalOptions__WEBPACK_IMPORTED_MODULE_5__.SCALING_STRATEGIES,\n                                    selectedValues: technicalLayer.scalingStrategy ? [\n                                        technicalLayer.scalingStrategy\n                                    ] : [],\n                                    onSelectionChange: (values)=>handleTechnicalFieldChange(\"scalingStrategy\", values[0] || \"\"),\n                                    placeholder: \"Select scaling strategy\",\n                                    placeholderAr: \"اختر استراتيجية التوسع\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedOptionsSelector__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    title: \"Security Requirements\",\n                                    titleAr: \"متطلبات الأمان\",\n                                    options: _lib_technicalOptions__WEBPACK_IMPORTED_MODULE_5__.SECURITY_REQUIREMENTS,\n                                    selectedValues: technicalLayer.securityRequirements ? [\n                                        technicalLayer.securityRequirements\n                                    ] : [],\n                                    onSelectionChange: (values)=>handleTechnicalFieldChange(\"securityRequirements\", values[0] || \"\"),\n                                    placeholder: \"Select security requirements\",\n                                    placeholderAr: \"اختر متطلبات الأمان\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedOptionsSelector__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    title: \"Performance Targets\",\n                                    titleAr: \"أهداف الأداء\",\n                                    options: _lib_technicalOptions__WEBPACK_IMPORTED_MODULE_5__.PERFORMANCE_TARGETS,\n                                    selectedValues: technicalLayer.performanceTargets ? [\n                                        technicalLayer.performanceTargets\n                                    ] : [],\n                                    onSelectionChange: (values)=>handleTechnicalFieldChange(\"performanceTargets\", values[0] || \"\"),\n                                    placeholder: \"Select performance targets\",\n                                    placeholderAr: \"اختر أهداف الأداء\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedOptionsSelector__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    title: \"Integration Needs\",\n                                    titleAr: \"احتياجات التكامل\",\n                                    options: _lib_technicalOptions__WEBPACK_IMPORTED_MODULE_5__.INTEGRATION_NEEDS,\n                                    selectedValues: technicalLayer.integrationNeeds ? [\n                                        technicalLayer.integrationNeeds\n                                    ] : [],\n                                    onSelectionChange: (values)=>handleTechnicalFieldChange(\"integrationNeeds\", values[0] || \"\"),\n                                    placeholder: \"Select integration needs\",\n                                    placeholderAr: \"اختر احتياجات التكامل\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedOptionsSelector__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    title: \"Monitoring Tools\",\n                                    titleAr: \"أدوات المراقبة\",\n                                    options: _lib_technicalOptions__WEBPACK_IMPORTED_MODULE_5__.MONITORING_TOOLS,\n                                    selectedValues: technicalLayer.monitoringTools ? [\n                                        technicalLayer.monitoringTools\n                                    ] : [],\n                                    onSelectionChange: (values)=>handleTechnicalFieldChange(\"monitoringTools\", values[0] || \"\"),\n                                    placeholder: \"Select monitoring tools\",\n                                    placeholderAr: \"اختر أدوات المراقبة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 p-4 bg-blue-50 dark:bg-blue-900/30 rounded-lg border border-blue-200 dark:border-blue-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: \"\\uD83D\\uDCA1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: isArabic ? \"text-right\" : \"text-left\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-blue-900 dark:text-blue-100 mb-1\",\n                                                children: isArabic ? \"نصيحة ذكية\" : \"Smart Tip\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-blue-700 dark:text-blue-300\",\n                                                children: isArabic ? \"كلما زادت دقة اختياراتك، كانت اقتراحات الذكاء الاصطناعي أكثر تخصصاً وفائدة لمشروعك.\" : \"The more precise your selections, the more specialized and useful AI suggestions will be for your project.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n                lineNumber: 115,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\AdvancedOptionsPanel.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n_s(AdvancedOptionsPanel, \"EPSJEo+XZJIf6IgMyymVi4P0tnQ=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore\n    ];\n});\n_c = AdvancedOptionsPanel;\nvar _c;\n$RefreshReg$(_c, \"AdvancedOptionsPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AdvancedOptionsPanel.tsx\n"));

/***/ })

});